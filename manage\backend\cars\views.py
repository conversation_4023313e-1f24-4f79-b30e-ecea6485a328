from django.shortcuts import render, get_object_or_404
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django_filters import rest_framework as filters
from django.db.models import Q, Avg, Count
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
from .models import Car, CarRating, CarComment, CommentReport, SensitiveWord, PropulsionLevelTable, CalculationHistory
from .serializers import CarSerializer, PropulsionLevelTableSerializer, CalculationHistorySerializer
from django.http import HttpResponse
import logging
import uuid
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.db import models
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
import os
import tempfile
import zipfile
import xml.etree.ElementTree as ET
import shutil
from copy import copy
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from openpyxl.styles import Font, Alignment
import io
from datetime import datetime
from django.conf import settings
from django.db.models import F
from django.db.models.functions import Cast, Substr, Replace
from django.db.models import FloatField, Value
from openpyxl.utils import get_column_letter

logger = logging.getLogger(__name__)

class CarFilter(filters.FilterSet):
    search = filters.CharFilter(method='search_filter')
    level = filters.CharFilter(field_name='level', lookup_expr='contains')

    class Meta:
        model = Car
        fields = ['level']

    def search_filter(self, queryset, name, value):
        return queryset.filter(
            Q(car_id__icontains=value) |
            Q(name__icontains=value)
        )

class CarViewSet(viewsets.ModelViewSet):
    queryset = Car.objects.all()
    serializer_class = CarSerializer
    filterset_class = CarFilter

    def get_queryset(self):
        """
        获取赛车列表，支持多种排序方式

        可选的排序参数：
        - sort_by: 排序字段
        - order: 排序方式（asc/desc）
        - show_all: 是否显示所有记录（默认只显示编号包含数字的记录）
        """
        from django.db.models import F
        from django.db.models.functions import Cast
        from django.db.models import FloatField

        # 获取排序参数
        sort_by = self.request.query_params.get('sort_by', '')
        order = self.request.query_params.get('order', 'desc')
        show_all = self.request.query_params.get('show_all', '').lower() == 'true'

        # 基础查询集
        queryset = Car.objects.all()

        # 如果不是显示全部，则只显示包含数字的记录
        if not show_all:
            queryset = queryset.filter(car_id__regex=r'\d')

        # 定义允许的排序字段映射
        allowed_sort_fields = {
            'car_id': 'car_id',
            'normal_speed': 'normal_speed',
            'nitro_speed': 'nitro_speed',
            'drift_factor': 'drift_factor',
            'friction_factor': 'friction_factor',
            'weight': 'weight',
            'high_speed_steering': 'high_speed_steering',
            'low_speed_steering': 'low_speed_steering',
            'suspension': 'suspension',
            'angle_normal_speed': 'angle_normal_speed',
            'angle_nitro_speed': 'angle_nitro_speed',
            'normal_180_acceleration': 'normal_180_acceleration',
            'normal_speed_acceleration': 'normal_speed_acceleration',
            'nitro_250_acceleration': 'nitro_250_acceleration',
            'nitro_290_acceleration': 'nitro_290_acceleration',
            'angle_normal_speed_advance40': 'angle_normal_speed_advance40',
            'angle_nitro_speed_advance40': 'angle_nitro_speed_advance40',
            'normal_180_acceleration_advance40': 'normal_180_acceleration_advance40',
            'normal_speed_acceleration_advance40': 'normal_speed_acceleration_advance40',
            'nitro_250_acceleration_advance40': 'nitro_250_acceleration_advance40',
            'nitro_290_acceleration_advance40': 'nitro_290_acceleration_advance40',
            # 新增字段排序支持
            'fuel_duration': 'fuel_duration',
            'fuel_intensity': 'fuel_intensity',
            'ignition_duration': 'ignition_duration',
            'ignition_intensity': 'ignition_intensity',
            'original_intake_coefficient': 'original_intake_coefficient',
            'intake_coefficient': 'intake_coefficient',
            'drift_steering': 'drift_steering',
            'drift_swing': 'drift_swing',
            'drift_reverse': 'drift_reverse',
            'drift_correction': 'drift_correction',
            'super_nitro_intensity': 'super_nitro_intensity',
            'super_nitro_duration': 'super_nitro_duration',
            'super_nitro_trigger_condition': 'super_nitro_trigger_condition',
            'super_nitro_250_acceleration': 'super_nitro_250_acceleration',
            'super_nitro_290_acceleration': 'super_nitro_290_acceleration',
            'angle_super_nitro_speed': 'angle_super_nitro_speed',
            'gem_slots': 'gem_slots'
        }

        # 定义需要提取数字的字段
        numeric_extract_fields = ['car_id', 'weight',
                                  'angle_normal_speed', 'angle_nitro_speed',
                                  'normal_180_acceleration', 'normal_speed_acceleration',
                                  'nitro_250_acceleration', 'nitro_290_acceleration',
                                  'angle_normal_speed_advance40', 'angle_nitro_speed_advance40',
                                  'normal_180_acceleration_advance40', 'normal_speed_acceleration_advance40',
                                  'nitro_250_acceleration_advance40', 'nitro_290_acceleration_advance40',
                                  # 新增需要提取数字的字段
                                  'fuel_duration', 'fuel_intensity',
                                  'ignition_duration', 'ignition_intensity',
                                  'original_intake_coefficient', 'intake_coefficient',
                                  'drift_steering', 'drift_swing',
                                  'drift_reverse', 'drift_correction',
                                  'super_nitro_intensity', 'super_nitro_duration',
                                  'super_nitro_250_acceleration', 'super_nitro_290_acceleration',
                                  'angle_super_nitro_speed', 'gem_slots']

        # 如果提供了有效的排序字段，才进行排序和过滤
        if sort_by in allowed_sort_fields:
            field_name = allowed_sort_fields[sort_by]

            # 排除当前排序字段值为"缺"的记录
            queryset = queryset.exclude(**{field_name: '缺'})

            # 对需要提取数字的字段进行特殊处理
            if field_name in numeric_extract_fields:
                # 定义不需要进行数字过滤的字段列表
                no_numeric_filter_fields = [
                    'angle_normal_speed', 'angle_nitro_speed',
                    'normal_180_acceleration', 'normal_speed_acceleration',
                    'nitro_250_acceleration', 'nitro_290_acceleration',
                    'angle_normal_speed_advance40', 'angle_nitro_speed_advance40',
                    'normal_180_acceleration_advance40', 'normal_speed_acceleration_advance40',
                    'nitro_250_acceleration_advance40', 'nitro_290_acceleration_advance40'
                ]

                # 对非极速和提速字段，过滤掉不包含数字的记录
                if field_name not in no_numeric_filter_fields:
                    queryset = queryset.filter(**{f"{field_name}__regex": r'\d'})

                # 使用Python排序，而不是依赖数据库功能
                # 先获取所有需要的对象
                car_objects = list(queryset)

                # 定义一个函数来提取字符串中的数字部分
                def extract_number(s, field=None, car=None):
                    # 如果为空值，返回0
                    if not s:
                        return 0.0

                    # 如果值为"缺"，返回0
                    if s == '缺':
                        return 0.0

                    # 字段对应关系映射（推进40字段 -> 非推进40字段）
                    advance40_field_mapping = {
                        'angle_normal_speed_advance40': 'angle_normal_speed',
                        'angle_nitro_speed_advance40': 'angle_nitro_speed',
                        'normal_180_acceleration_advance40': 'normal_180_acceleration',
                        'normal_speed_acceleration_advance40': 'normal_speed_acceleration',
                        'nitro_250_acceleration_advance40': 'nitro_250_acceleration',
                        'nitro_290_acceleration_advance40': 'nitro_290_acceleration'
                    }

                    # 对极速相关字段进行特殊处理
                    angle_fields = ['angle_normal_speed', 'angle_nitro_speed']

                    # 对提速相关字段进行特殊处理
                    acceleration_fields = ['normal_180_acceleration', 'normal_speed_acceleration',
                                           'nitro_250_acceleration', 'nitro_290_acceleration']

                    # 如果是推进40字段且值为"暂无"，使用对应非推进40字段的值
                    if field in advance40_field_mapping and s == '暂无' and car:
                        # 获取对应的非推进40字段
                        base_field = advance40_field_mapping[field]
                        # 获取对应字段的值
                        base_value = getattr(car, base_field, None)
                        # 递归调用，处理对应字段的值
                        if base_value:
                            return extract_number(base_value, base_field, car)


                    # 如果是提速相关字段且值为"无法到达"，返回1000
                    if field in acceleration_fields and s == '无法到达':
                        return 1000.0

                    # 匹配所有数字字符和小数点
                    import re
                    number_str = ''.join(re.findall(r'[0-9.]+', str(s)))

                    # # 如果是极速或提速相关字段且提取不到数字，返回一个较大的值用于排序
                    # no_numeric_filter_fields = [
                    #     'angle_normal_speed', 'angle_nitro_speed',
                    #     'normal_180_acceleration', 'normal_speed_acceleration',
                    #     'nitro_250_acceleration', 'nitro_290_acceleration',
                    #     'angle_normal_speed_advance40', 'angle_nitro_speed_advance40',
                    #     'normal_180_acceleration_advance40', 'normal_speed_acceleration_advance40',
                    #     'nitro_250_acceleration_advance40', 'nitro_290_acceleration_advance40'
                    # ]

                    # if field in no_numeric_filter_fields and not number_str:
                    #     # 对于这些特殊字段，如果无法提取数字，我们返回一个大值(999)让它们排在后面
                    #     # 但比"无法达到"和"暂无"(1000)稍微靠前一点
                    #     return 999.0

                    try:
                        return float(number_str) if number_str else 0.0
                    except ValueError:
                        return 0.0

                # 提取car_id中的数字作为二级排序键
                def extract_car_id_number(car):
                    # 对car_id提取数字，并返回负值，确保降序排序
                    car_id_num = extract_number(car.car_id)
                    return -car_id_num  # 取负值确保降序排序

                # 定义需要特殊排序的字段（极速和提速相关字段）
                special_sort_fields = [
                    'angle_normal_speed', 'angle_nitro_speed',
                    'normal_180_acceleration', 'normal_speed_acceleration',
                    'nitro_250_acceleration', 'nitro_290_acceleration',
                    'angle_normal_speed_advance40', 'angle_nitro_speed_advance40',
                    'normal_180_acceleration_advance40', 'normal_speed_acceleration_advance40',
                    'nitro_250_acceleration_advance40', 'nitro_290_acceleration_advance40'
                ]

                # 根据字段类型选择不同的排序方式
                if field_name == 'angle_normal_speed_advance40' or field_name == 'angle_nitro_speed_advance40':
                    # 特殊处理angle_normal_speed_advance40和angle_nitro_speed_advance40字段
                    # 次级排序为normal_180_acceleration_advance40（方向与主排序相反），最后是car_id降序

                    # 定义一个帮助函数获取次级排序值
                    def get_normal_180_sort_value(car):
                        # 首先尝试使用advance40版本
                        adv40_value = getattr(car, 'normal_180_acceleration_advance40', None)
                        # 如果值为"暂无"，则使用非advance40版本
                        if adv40_value == '暂无':
                            return extract_number(getattr(car, 'normal_180_acceleration', 0), 'normal_180_acceleration', car)
                        else:
                            return extract_number(adv40_value, 'normal_180_acceleration_advance40', car)

                    if order.lower() == 'desc':
                        # 主排序降序，则次级排序升序，最后car_id降序
                        car_objects.sort(
                            key=lambda car: (
                                -extract_number(getattr(car, field_name), field_name, car),
                                get_normal_180_sort_value(car),  # 使用辅助函数获取次级排序值
                                -extract_number(car.car_id)
                            ),
                            reverse=False
                        )
                    else:
                        # 主排序升序，则次级排序降序，最后car_id降序
                        car_objects.sort(
                            key=lambda car: (
                                extract_number(getattr(car, field_name), field_name, car),
                                -get_normal_180_sort_value(car),  # 注意这里取负，保持与主排序方向相反
                                -extract_number(car.car_id)
                            ),
                            reverse=False
                        )
                elif field_name in ['normal_180_acceleration_advance40', 'normal_speed_acceleration_advance40',
                                     'nitro_250_acceleration_advance40', 'nitro_290_acceleration_advance40']:
                    # 特殊处理提速相关字段
                    # 次级排序为angle_normal_speed_advance40（方向与主排序相反），最后是car_id降序
                    # 当angle_normal_speed_advance40为"暂无"时，使用angle_normal_speed作为次级排序

                    # 定义一个帮助函数获取次级排序值
                    def get_secondary_sort_value(car):
                        # 首先尝试使用advance40版本
                        adv40_value = getattr(car, 'angle_normal_speed_advance40', None)
                        # 如果值为"暂无"，则使用非advance40版本
                        if adv40_value == '暂无':
                            return extract_number(getattr(car, 'angle_normal_speed', 0), 'angle_normal_speed', car)
                        else:
                            return extract_number(adv40_value, 'angle_normal_speed_advance40', car)

                    # 定义提速字段提取函数，正确处理"无法到达"
                    def extract_acceleration_value(car, field_name):
                        # 获取字段值
                        value = getattr(car, field_name, None)
                        # 无法到达的处理：
                        # - 升序排序时，值为1000（排在最后）
                        # - 降序排序时，值为-1000（排在最后）
                        if value == '无法到达':
                            if order.lower() == 'desc':
                                return -1000.0  # 降序排序时让它排最后
                            else:
                                return 1000.0   # 升序排序时让它排最后
                        else:
                            return extract_number(value, field_name, car)

                    # 获取角度值档位（特别为normal_speed_acceleration_advance40添加）
                    def get_angle_tier(car):
                        """
                        获取角度值的档位分类
                        大于227 -> 0
                        ==227 -> 1
                        ==226 -> 2
                        ==225 -> 3
                        ...
                        ==196 -> 32
                        ==195 -> 33
                        小于195 -> 34
                        """
                        # 首先尝试使用advance40版本
                        angle_value = getattr(car, 'angle_normal_speed_advance40', None)

                        # 如果值为"暂无"，则使用非advance40版本
                        if angle_value == '暂无':
                            angle_value = getattr(car, 'angle_normal_speed', '0')

                        # 提取数值
                        angle_num = extract_number(angle_value, 'angle_normal_speed_advance40' if angle_value != '暂无' else 'angle_normal_speed', car)

                        # 根据数值返回档位
                        if angle_num > 227:
                            return 0  # 最高档
                        elif angle_num < 195:
                            return 34  # 最低档
                        else:
                            # 195到227之间的每个整数值都对应一个档位
                            # 227对应档位1，226对应档位2，以此类推
                            return int(228 - angle_num)

                    # if field_name in ['normal_180_acceleration_advance40','normal_speed_acceleration_advance40', 'nitro_250_acceleration_advance40', 'nitro_290_acceleration_advance40']:
                    if field_name in ['normal_speed_acceleration_advance40']:
                        # 特殊处理normal_speed_acceleration_advance40字段，先按angle_normal_speed_advance40档位分类
                        if order.lower() == 'desc':
                            # 主排序降序：先按角度档位升序（小值更好），再按提速值降序，最后car_id降序
                            car_objects.sort(
                                key=lambda car: (
                                    get_angle_tier(car),  # 先按角度档位排序
                                    extract_acceleration_value(car, field_name),  # 使用新的提速值提取函数
                                    -get_secondary_sort_value(car),  # 角度值（取负，与主排序相反）
                                    -extract_number(car.car_id)
                                ),
                                reverse=True  # 使用True，因为提速降序
                            )
                        else:
                            # 主排序升序：先按角度档位升序（小值更好），再按提速值升序，最后car_id降序
                            car_objects.sort(
                                key=lambda car: (
                                    get_angle_tier(car),  # 先按角度档位排序
                                    extract_acceleration_value(car, field_name),  # 提速值
                                    -get_secondary_sort_value(car),  # 角度值（取负，与提速方向相反）
                                    -extract_number(car.car_id)
                                ),
                                reverse=False
                            )
                    else:
                        # 其他提速字段保持原有逻辑
                        if order.lower() == 'desc':
                            # 主排序降序，则次级排序升序，最后car_id降序
                            car_objects.sort(
                                key=lambda car: (
                                    extract_acceleration_value(car, field_name),  # 使用新的提速值提取函数
                                    get_secondary_sort_value(car),  # 使用辅助函数获取次级排序值
                                    -extract_number(car.car_id)
                                ),
                                reverse=True  # 使用True，因为我们现在直接返回正确的排序值
                            )
                        else:
                            # 主排序升序，则次级排序降序，最后car_id降序
                            car_objects.sort(
                                key=lambda car: (
                                    extract_acceleration_value(car, field_name),  # 使用新的提速值提取函数
                                    -get_secondary_sort_value(car),  # 注意这里取负，保持与主排序方向相反
                                    -extract_number(car.car_id)
                                ),
                                reverse=False
                            )
                elif field_name in special_sort_fields:
                    # 对于其他特殊字段（极速和提速相关），需要分别处理升序和降序情况
                    if order.lower() == 'desc':
                        # 降序排序：主字段降序，car_id也降序
                        car_objects.sort(
                            key=lambda car: (-extract_number(getattr(car, field_name), field_name, car), -extract_number(car.car_id)),
                            reverse=False  # 使用负值实现降序，所以reverse设为False
                        )
                    else:
                        # 升序排序：主字段升序，但car_id降序
                        car_objects.sort(
                            key=lambda car: (extract_number(getattr(car, field_name), field_name, car), -extract_number(car.car_id)),
                            reverse=False
                        )
                else:
                    # 对于普通字段，同样分别处理升序和降序情况
                    if order.lower() == 'desc':
                        # 降序排序：主字段降序，car_id也降序
                        car_objects.sort(
                            key=lambda car: (-extract_number(getattr(car, field_name), field_name, car), -extract_number(car.car_id)),
                            reverse=False
                        )
                    else:
                        # 升序排序：主字段升序，但car_id降序
                        car_objects.sort(
                            key=lambda car: (extract_number(getattr(car, field_name), field_name, car), -extract_number(car.car_id)),
                            reverse=False
                        )

                # 创建一个包含排序后对象ID的列表
                sorted_ids = [car.id for car in car_objects]

                # 使用排序后的ID列表来保持顺序
                if sorted_ids:
                    # 使用Field查询表达式的pk__in来保持顺序
                    from django.db.models import Case, When, Value, IntegerField
                    preserved_order = Case(
                        *[When(pk=pk, then=Value(i)) for i, pk in enumerate(sorted_ids)],
                        output_field=IntegerField()
                    )
                    # 返回排序后的查询集
                    return queryset.filter(pk__in=sorted_ids).order_by(preserved_order)
                return queryset

            else:
                # 其他字段使用普通排序，并添加car_id数字部分作为次级排序
                order_prefix = '-' if order.lower() == 'desc' else ''
                queryset = queryset.extra(
                    select={
                        'car_id_number': "CAST(REPLACE(REPLACE(car_id, 't', ''), '缺', '0') AS FLOAT)"
                    },
                    order_by=[f'{order_prefix}{field_name}', '-car_id_number']
                )
        else:
            # 默认按car_id数字部分降序排序
            queryset = queryset.extra(
                select={
                    'car_id_number': "CAST(REPLACE(REPLACE(car_id, 't', ''), '缺', '0') AS FLOAT)"
                },
                order_by=['-car_id_number']
            )

        return queryset

    @action(detail=False, methods=['post'])
    def upload_image(self, request):
        """上传赛车图片，同时上传到腾讯云存储桶"""
        try:
            file = request.FILES.get('image')
            if not file:
                return Response({'error': 'No image file uploaded'}, status=status.HTTP_400_BAD_REQUEST)

            # 检查文件类型
            if not file.content_type.startswith('image/'):
                return Response({'error': 'File type not allowed'}, status=status.HTTP_400_BAD_REQUEST)

            # 检查文件大小（限制为2MB）
            if file.size > 2 * 1024 * 1024:
                return Response({'error': 'File size too large'}, status=status.HTTP_400_BAD_REQUEST)

            # 生成唯一的文件名
            ext = file.name.split('.')[-1]
            filename = f"{uuid.uuid4()}.{ext}"
            filepath = f"car_images/{filename}"

            # 保存文件到本地
            file_path = default_storage.save(filepath, file)
            file_url = default_storage.url(file_path)

            # 上传到腾讯云COS
            try:
                from qcloud_cos import CosConfig
                from qcloud_cos import CosS3Client
                import os
                from django.conf import settings

                # 腾讯云配置信息
                secret_id = getattr(settings, 'TENCENT_COS_SECRET_ID', '')
                secret_key = getattr(settings, 'TENCENT_COS_SECRET_KEY', '')
                region = getattr(settings, 'TENCENT_COS_REGION', 'ap-guangzhou')
                bucket = getattr(settings, 'TENCENT_COS_BUCKET', '')
                cos_path = getattr(settings, 'TENCENT_COS_PATH', 'car_images/')

                if not all([secret_id, secret_key, region, bucket]):
                    logger.warning("腾讯云COS配置不完整，跳过云存储上传")
                else:
                    # 初始化客户端
                    config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key)
                    client = CosS3Client(config)

                    # 获取本地文件路径
                    local_file_path = os.path.join(settings.MEDIA_ROOT, filepath)

                    # 确保cos_path以/结尾
                    if cos_path and not cos_path.endswith('/'):
                        cos_path += '/'

                    # 上传到腾讯云，保持同名
                    cloud_file_path = f"{cos_path}{filename}"

                    # 确保cloud_file_path不以/开头
                    if cloud_file_path.startswith('/'):
                        cloud_file_path = cloud_file_path[1:]

                    # 执行上传
                    response = client.upload_file(
                        Bucket=bucket,
                        LocalFilePath=local_file_path,
                        Key=cloud_file_path,
                    )

                    # 构建访问URL
                    cos_file_url = f"https://{bucket}.cos.{region}.myqcloud.com/{cloud_file_path}"
                    logger.info(f"成功上传文件到腾讯云: {cos_file_url}")
            except Exception as cos_err:
                # 仅记录错误，不影响正常上传流程
                logger.error(f"上传到腾讯云COS失败: {str(cos_err)}")
                cos_file_url = None

            # 返回结果
            result = {
                'success': True,
                'message': '图片上传成功',
                'image_id': filename,
                'local_url': file_url
            }

            if locals().get('cos_file_url'):
                result['cos_url'] = cos_file_url

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error uploading image: {str(e)}")
            return Response({
                'error': 'Failed to upload image',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def upload_images_by_name(self, request):
        """
        通过赛车名称或编号批量上传图片

        请求格式：
        {
            "images": [
                {
                    "name": "风之子",  # 赛车名称或编号
                    "image": <file>    # 图片文件
                },
                ...
            ]
        }
        """
        try:
            images_data = request.FILES.getlist('images[]')
            names_data = request.POST.getlist('names[]')

            if not images_data or not names_data or len(images_data) != len(names_data):
                return Response({
                    'error': '图片和名称数据不匹配'
                }, status=status.HTTP_400_BAD_REQUEST)

            results = []
            for image, name in zip(images_data, names_data):
                try:
                    # 查找赛车
                    cars = Car.objects.filter(Q(name=name) | Q(car_id=name))
                    if not cars.exists():
                        results.append({
                            'name': name,
                            'success': False,
                            'message': f'未找到匹配的赛车: {name}'
                        })
                        continue

                    car = cars.first()

                    # 检查文件类型
                    if not image.content_type.startswith('image/'):
                        results.append({
                            'name': name,
                            'success': False,
                            'message': f'文件类型不允许: {image.name}'
                        })
                        continue

                    # 检查文件大小
                    if image.size > 2 * 1024 * 1024:  # 2MB
                        results.append({
                            'name': name,
                            'success': False,
                            'message': f'文件过大: {image.name}'
                        })
                        continue

                    # 生成新的文件名
                    ext = image.name.split('.')[-1]
                    filename = f"{uuid.uuid4()}.{ext}"
                    filepath = f"car_images/{filename}"

                    # 保存文件
                    default_storage.save(filepath, image)

                    # 更新赛车记录
                    car.image_id = filename
                    car.save()

                    results.append({
                        'name': name,
                        'success': True,
                        'message': '上传成功',
                        'image_id': filename
                    })

                except Exception as item_error:
                    logger.error(f"处理图片时出错 {name}: {str(item_error)}")
                    results.append({
                        'name': name,
                        'success': False,
                        'message': f'处理失败: {str(item_error)}'
                    })

            return Response({
                'success': True,
                'results': results
            })

        except Exception as e:
            logger.error(f"批量上传图片失败: {str(e)}")
            return Response({
                'error': '批量上传失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        try:
            # 获取查询参数
            search = request.query_params.get('search', '')
            level = request.query_params.get('level', '')

            # 获取基础查询集
            queryset = self.get_queryset()

            # 应用搜索过滤
            if search:
                queryset = queryset.filter(
                    Q(car_id__icontains=search) |
                    Q(name__icontains=search)
                )

            # 应用级别过滤
            if level:
                queryset = queryset.filter(level=level)

            logger.info(f"Exporting cars with filters - search: {search}, level: {level}")
            logger.info(f"Total cars to export: {queryset.count()}")

            # 使用模板文件
            template_path = 'templates/cars_template.xlsx'
            if not os.path.exists(template_path):
                logger.warning("Template file not found, using default template")
                # 如果模板不存在，创建新的工作簿
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "赛车列表"

                # 设置表头
                headers = [
                    '赛车编号', '赛车名称', '赛车级别',
                    '平跑极速', '氮气极速', '漂移速率',
                    '摩擦系数', '车重', '最大转向',
                    '最小转向', '引擎1挡', '引擎2挡',
                    '引擎3挡', '引擎4挡', '引擎5挡',
                    '引擎6挡', '原装推进1档', '原装推进2档',
                    '原装推进3档', '原装推进4档', '原装推进5档',
                    '原装推进6档', '原装推进7档', '悬挂',
                    '夹角平跑极速', '夹角氮气极速', '平跑180提速',
                    '平跑极速提速', '大喷250提速', '大喷290提速',
                    '夹角平跑极速（推进40）', '夹角氮气极速（推进40）', '平跑180提速（推进40）',
                    '平跑极速提速（推进40）', '大喷250提速（推进40）', '大喷290提速（推进40）',
                    # 新增字段
                    '燃料时长', '燃料强度', '点火时长', '点火强度',
                    '原装进气系数', '进气系数', '漂移转向', '漂移摆动',
                    '漂移反向', '漂移回正', '超级喷强度', '超级喷时长',
                    '超级喷触发条件', '超级喷250提速', '超级喷290提速', '夹角超级喷极速',
                    # 新增字段
                    '宝石槽'
                    # '赛车图片' # 注释掉图片列
                ]

                for col, header in enumerate(headers, 1):
                    ws.cell(row=1, column=col).value = header

                # 设置列宽
                for col in range(1, 53):  # 从1到52列，匹配我们实际输出的列数（增加了原装推进1-6档）
                    ws.column_dimensions[get_column_letter(col)].width = 15
                # ws.column_dimensions[get_column_letter(48)].width = 30  # 图片列宽度（已注释）
            else:
                # 使用模板文件
                wb = openpyxl.load_workbook(template_path)
                ws = wb.active

                # 获取模板中的第二行（作为样式模板）
                template_row = 2
                template_cells = {}
                for col in range(1, 53):  # 更新为53列，匹配我们实际输出的列数
                    try:
                        cell = ws.cell(row=template_row, column=col)
                        template_cells[col] = {
                            'font': copy(cell.font),
                            'border': copy(cell.border),
                            'fill': copy(cell.fill),
                            'number_format': cell.number_format,
                            'alignment': copy(cell.alignment)
                        }
                    except Exception as e:
                        # 如果获取样式失败，使用默认值
                        logger.warning(f"Failed to get template style for column {col}: {str(e)}")
                        template_cells[col] = {
                            'font': Font(),
                            'border': None,
                            'fill': None,
                            'number_format': 'General',
                            'alignment': Alignment()
                        }

                # 清除模板中的示例数据（保留第一行表头）
                for row in ws.iter_rows(min_row=2):
                    for cell in row:
                        cell.value = None

            # 写入数据（使用过滤后的queryset）
            current_row = 2  # 从第2行开始（第1行是表头）
            for car in queryset:
                # 写入数据到对应的单元格
                for col in range(1, 52):  # 更新为1到51列写入数据（增加了原装推进1-6档）
                    cell = ws.cell(row=current_row, column=col)
                    # 根据列号设置对应的值
                    if col == 1:
                        cell.value = car.car_id
                    elif col == 2:
                        cell.value = car.name
                    elif col == 3:
                        cell.value = car.level
                    elif col == 4:
                        cell.value = car.normal_speed
                    elif col == 5:
                        cell.value = car.nitro_speed
                    elif col == 6:
                        cell.value = car.drift_factor
                    elif col == 7:
                        cell.value = car.friction_factor
                    elif col == 8:
                        cell.value = car.weight
                    elif col == 9:
                        cell.value = car.low_speed_steering
                    elif col == 10:
                        cell.value = car.high_speed_steering
                    elif col == 11:
                        cell.value = car.engine_gear_1
                    elif col == 12:
                        cell.value = car.engine_gear_2
                    elif col == 13:
                        cell.value = car.engine_gear_3
                    elif col == 14:
                        cell.value = car.engine_gear_4
                    elif col == 15:
                        cell.value = car.engine_gear_5
                    elif col == 16:
                        cell.value = car.engine_gear_6
                    elif col == 17:
                        cell.value = car.original_propulsion_1
                    elif col == 18:
                        cell.value = car.original_propulsion_2
                    elif col == 19:
                        cell.value = car.original_propulsion_3
                    elif col == 20:
                        cell.value = car.original_propulsion_4
                    elif col == 21:
                        cell.value = car.original_propulsion_5
                    elif col == 22:
                        cell.value = car.original_propulsion_6
                    elif col == 23:
                        cell.value = car.original_propulsion_7
                    elif col == 24:
                        cell.value = car.suspension
                    elif col == 25:
                        cell.value = car.angle_normal_speed
                    elif col == 26:
                        cell.value = car.angle_nitro_speed
                    elif col == 27:
                        cell.value = car.normal_180_acceleration
                    elif col == 28:
                        cell.value = car.normal_speed_acceleration
                    elif col == 29:
                        cell.value = car.nitro_250_acceleration
                    elif col == 30:
                        cell.value = car.nitro_290_acceleration
                    elif col == 31:
                        cell.value = car.angle_normal_speed_advance40
                    elif col == 32:
                        cell.value = car.angle_nitro_speed_advance40
                    elif col == 33:
                        cell.value = car.normal_180_acceleration_advance40
                    elif col == 34:
                        cell.value = car.normal_speed_acceleration_advance40
                    elif col == 35:
                        cell.value = car.nitro_250_acceleration_advance40
                    elif col == 36:
                        cell.value = car.nitro_290_acceleration_advance40
                    # 新增字段的处理
                    elif col == 37:
                        cell.value = car.fuel_duration
                    elif col == 38:
                        cell.value = car.fuel_intensity
                    elif col == 39:
                        cell.value = car.ignition_duration
                    elif col == 40:
                        cell.value = car.ignition_intensity
                    elif col == 41:
                        cell.value = car.original_intake_coefficient
                    elif col == 42:
                        cell.value = car.intake_coefficient
                    elif col == 43:
                        cell.value = car.drift_steering
                    elif col == 44:
                        cell.value = car.drift_swing
                    elif col == 45:
                        cell.value = car.drift_reverse
                    elif col == 46:
                        cell.value = car.drift_correction
                    elif col == 47:
                        cell.value = car.super_nitro_intensity
                    elif col == 48:
                        cell.value = car.super_nitro_duration
                    elif col == 49:
                        cell.value = car.super_nitro_trigger_condition
                    elif col == 50:
                        cell.value = car.super_nitro_250_acceleration
                    elif col == 51:
                        cell.value = car.super_nitro_290_acceleration
                    elif col == 52:
                        cell.value = car.angle_super_nitro_speed
                    elif col == 53:
                        cell.value = car.gem_slots

                    # 如果有模板格式，应用到当前单元格
                    if template_cells:
                        try:
                            # 如果当前列不在template_cells中，使用第1列的样式
                            style_col = col if col in template_cells else 1
                            cell.font = copy(template_cells[style_col]['font'])
                            cell.border = copy(template_cells[style_col]['border'])
                            cell.fill = copy(template_cells[style_col]['fill'])
                            cell.number_format = template_cells[style_col]['number_format']
                            cell.alignment = copy(template_cells[style_col]['alignment'])
                        except Exception as style_error:
                            logger.warning(f"Error applying style to cell at row {current_row}, col {col}: {str(style_error)}")

                # 如果有图片，插入到对应单元格
                if car.image_id:
                    # 注释掉图片处理代码，减小Excel文件大小
                    """
                    try:
                        image_path = f'media/car_images/{car.image_id}'
                        if os.path.exists(image_path):
                            # 添加图片到工作表
                            img = openpyxl.drawing.image.Image(image_path)

                            # 设置图片大小为正方形
                            img.width = 90  # 像素
                            img.height = 90  # 像素

                            # 设置图片位置（第25列）
                            cell = ws.cell(row=current_row, column=25)
                            # 如果有模板格式，应用到图片单元格
                            if template_cells:
                                cell.font = copy(template_cells[1]['font'])  # 使用第一列的样式
                                cell.border = copy(template_cells[1]['border'])
                                cell.fill = copy(template_cells[1]['fill'])
                                cell.number_format = template_cells[1]['number_format']
                                cell.alignment = copy(template_cells[1]['alignment'])

                            img.anchor = f'Y{current_row}'  # 使用正确的列标识 Y 而不是 S

                            # 调整单元格大小以适应正方形图片
                            ws.row_dimensions[current_row].height = 68  # 约等于90像素
                            # 确保图片列宽足够
                            ws.column_dimensions['Y'].width = 13  # 约等于90像素

                            # 将图片添加到工作表
                            ws.add_image(img)

                            logger.info(f"Added image {car.image_id} to cell Y{current_row}")
                        else:
                            logger.warning(f"Image file not found: {image_path}")
                    except Exception as img_error:
                        logger.error(f"Error adding image to Excel: {str(img_error)}")
                    """
                    # 仅记录图片信息，不添加到Excel
                    car_id = car.car_id if hasattr(car, 'car_id') else 'unknown'
                    logger.info(f"Skipped adding image for car {car_id} (disabled to reduce file size)")

                current_row += 1

            # 创建HTTP响应
            response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = 'attachment; filename=cars.xlsx'

            # 保存前确保目录存在
            temp_dir = tempfile.mkdtemp()
            temp_file = os.path.join(temp_dir, 'cars.xlsx')

            try:
                # 先保存到临时文件
                wb.save(temp_file)

                # 读取临时文件并写入响应
                with open(temp_file, 'rb') as f:
                    response.write(f.read())

                return response
            finally:
                # 清理临时文件
                shutil.rmtree(temp_dir)

        except Exception as e:
            logger.error(f"Error exporting Excel: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def import_excel(self, request):
        try:
            excel_file = request.FILES.get('file')
            if not excel_file:
                return Response({'error': '未上传Excel文件'}, status=status.HTTP_400_BAD_REQUEST)

            # 创建media/car_images目录（如果不存在）
            car_images_dir = 'media/car_images'
            if not os.path.exists(car_images_dir):
                os.makedirs(car_images_dir)

            # 保存上传的Excel文件到临时目录
            temp_dir = tempfile.mkdtemp()
            temp_excel_path = os.path.join(temp_dir, 'temp.xlsx')
            with open(temp_excel_path, 'wb') as f:
                for chunk in excel_file.chunks():
                    f.write(chunk)

            # 解压Excel文件（xlsx实际上是一个zip文件）
            temp_extract_dir = os.path.join(temp_dir, 'extract')
            with zipfile.ZipFile(temp_excel_path, 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)

            # 检查media目录中的图片
            media_dir = os.path.join(temp_extract_dir, 'xl', 'media')
            image_files = {}
            if os.path.exists(media_dir):
                for filename in os.listdir(media_dir):
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                        image_files[filename] = os.path.join(media_dir, filename)
                        logger.info(f"Found embedded image: {filename}")

            workbook = openpyxl.load_workbook(temp_excel_path, data_only=True)
            worksheet = workbook.active

            # 获取表头
            headers = [cell.value for cell in worksheet[1]]
            logger.info(f"Excel headers: {headers}")

            # 检查图片信息
            logger.info(f"Checking workbook properties:")
            logger.info(f"Worksheet names: {workbook.sheetnames}")
            logger.info(f"Active worksheet: {worksheet.title}")

            # 读取drawing关系文件以获取图片映射
            rels_path = os.path.join(temp_extract_dir, 'xl', 'drawings', '_rels', 'drawing1.xml.rels')
            image_rels = {}
            if os.path.exists(rels_path):
                tree = ET.parse(rels_path)
                root = tree.getroot()
                for rel in root.findall('.//{http://schemas.openxmlformats.org/package/2006/relationships}Relationship'):
                    rid = rel.get('Id')
                    target = rel.get('Target')
                    if target and target.startswith('../media/'):
                        image_name = os.path.basename(target)
                        image_rels[rid] = image_name
                        logger.info(f"Found image relationship: {rid} -> {image_name}")

            # 读取drawing文件以获取单元格和图片的对应关系
            drawing_path = os.path.join(temp_extract_dir, 'xl', 'drawings', 'drawing1.xml')
            cell_images = {}

            # WPS可能使用不同的路径存储图片关系
            if not os.path.exists(drawing_path):
                # 检查其他可能的路径
                possible_paths = [
                    os.path.join(temp_extract_dir, 'xl', 'worksheets', 'drawing1.xml'),
                    os.path.join(temp_extract_dir, 'xl', 'worksheets', '_rels', 'drawing1.xml.rels'),
                ]
                for path in possible_paths:
                    if os.path.exists(path):
                        drawing_path = path
                        break

            # 如果找不到drawing文件，尝试直接匹配图片到单元格
            if not os.path.exists(drawing_path):
                logger.info("No drawing file found, trying direct cell mapping")
                # 按顺序将图片分配给有数据的行
                available_images = list(image_files.values())
                if available_images:
                    row_num = 2  # 从第二行开始
                    for img_path in available_images:
                        cell_images[row_num] = img_path
                        logger.info(f"Mapped image to cell Y{row_num}: {os.path.basename(img_path)}")
                        row_num += 1
            else:
                try:
                    tree = ET.parse(drawing_path)
                    root = tree.getroot()
                    ns = {'xdr': 'http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing',
                          'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'}

                    for pic in root.findall('.//xdr:pic', ns):
                        blip_rid = pic.find('.//a:blip', ns).get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                        cell_marker = pic.find('.//xdr:from', ns)
                        if cell_marker is not None and blip_rid in image_rels:
                            row = int(cell_marker.find('xdr:row', ns).text) + 1
                            col = int(cell_marker.find('xdr:col', ns).text)
                            if col == 24:  # 第25列，但在XML中是从0开始计数
                                image_name = image_rels[blip_rid]
                                cell_images[row] = image_files.get(image_name)
                                logger.info(f"Found image for cell Y{row}: {image_name}")
                except Exception as xml_error:
                    logger.error(f"Error parsing drawing file: {str(xml_error)}")
                    # 如果解析失败，也尝试直接匹配
                    available_images = list(image_files.values())
                    if available_images:
                        row_num = 2  # 从第二行开始
                        for img_path in available_images:
                            cell_images[row_num] = img_path
                            logger.info(f"Fallback: Mapped image to cell Y{row_num}: {os.path.basename(img_path)}")
                            row_num += 1

            # 从第二行开始读取数据
            for row in worksheet.iter_rows(min_row=2):
                if not any(cell.value for cell in row):  # 跳过空行
                    continue

                # 只收集有值的字段
                car_data = {}
                field_mapping = {
                    0: 'car_id',
                    1: 'name',
                    2: 'level',
                    3: 'normal_speed',
                    4: 'nitro_speed',
                    5: 'drift_factor',
                    6: 'friction_factor',
                    7: 'weight',
                    8: 'low_speed_steering',
                    9: 'high_speed_steering',
                    10: 'engine_gear_1',
                    11: 'engine_gear_2',
                    12: 'engine_gear_3',
                    13: 'engine_gear_4',
                    14: 'engine_gear_5',
                    15: 'engine_gear_6',
                    16: 'original_propulsion_1',
                    17: 'original_propulsion_2',
                    18: 'original_propulsion_3',
                    19: 'original_propulsion_4',
                    20: 'original_propulsion_5',
                    21: 'original_propulsion_6',
                    22: 'original_propulsion_7',
                    23: 'suspension',
                    24: 'angle_normal_speed',
                    25: 'angle_nitro_speed',
                    26: 'normal_180_acceleration',
                    27: 'normal_speed_acceleration',
                    28: 'nitro_250_acceleration',
                    29: 'nitro_290_acceleration',
                    30: 'angle_normal_speed_advance40',
                    31: 'angle_nitro_speed_advance40',
                    32: 'normal_180_acceleration_advance40',
                    33: 'normal_speed_acceleration_advance40',
                    34: 'nitro_250_acceleration_advance40',
                    35: 'nitro_290_acceleration_advance40',
                    # 新增字段映射
                    36: 'fuel_duration',
                    37: 'fuel_intensity',
                    38: 'ignition_duration',
                    39: 'ignition_intensity',
                    40: 'original_intake_coefficient',
                    41: 'intake_coefficient',
                    42: 'drift_steering',
                    43: 'drift_swing',
                    44: 'drift_reverse',
                    45: 'drift_correction',
                    46: 'super_nitro_intensity',
                    47: 'super_nitro_duration',
                    48: 'super_nitro_trigger_condition',
                    49: 'super_nitro_250_acceleration',
                    50: 'super_nitro_290_acceleration',
                    51: 'angle_super_nitro_speed',
                    52: 'gem_slots'
                }

                # 只添加有值的字段到car_data
                for idx, field_name in field_mapping.items():
                    if row[idx].value is not None:  # 只收集非None的值
                        car_data[field_name] = row[idx].value

                if not car_data.get('car_id'):  # 如果没有car_id则跳过
                    continue

                current_row = row[0].row
                logger.info(f"Processing row {current_row}")

                # 检查是否有对应的图片
                if current_row in cell_images and os.path.exists(cell_images[current_row]):
                    try:
                        # 生成新的image_id
                        image_id = str(uuid.uuid4())
                        image_extension = os.path.splitext(cell_images[current_row])[1]
                        # 将扩展名加入image_id
                        image_id_with_ext = f"{image_id}{image_extension}"
                        new_image_path = f'{car_images_dir}/{image_id_with_ext}'

                        logger.info(f"Copying image for row {current_row} to {new_image_path}")

                        # 复制图片文件
                        shutil.copy2(cell_images[current_row], new_image_path)

                        # 保存带扩展名的image_id
                        car_data['image_id'] = image_id_with_ext
                        logger.info(f"Successfully saved image with ID: {image_id_with_ext}")
                    except Exception as img_error:
                        logger.error(f"Error processing image for row {current_row}: {str(img_error)}")

                # 打印每行数据
                logger.info(f"Processing row data: {car_data}")

                # 创建或更新赛车记录，只更新有值的字段
                car, created = Car.objects.update_or_create(
                    car_id=car_data.pop('car_id'),  # 从car_data中移除car_id
                    defaults=car_data  # 只包含有值的字段
                )
                logger.info(f"{'Created' if created else 'Updated'} car record with ID: {car.id}, image_id: {car.image_id}")

            # 清理临时文件
            shutil.rmtree(temp_dir)

            return Response({
                'message': 'Excel导入成功',
                'imported_cars': worksheet.max_row - 1
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error importing Excel and images: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def levels(self, request):
        """
        获取所有赛车等级列表

        @return {
            "success": True,
            "levels": [
                {"value": "S", "label": "S级"},
                {"value": "A", "label": "A级"},
                ...
            ]
        }
        """
        try:
            # 从数据库中获取所有不重复的等级
            levels = Car.objects.values_list('level', flat=True).distinct()

            # 定义等级顺序
            level_order = {
                'S': 1, 'A': 2, 'B': 3, 'C': 4,
                'T3': 5, 'T2': 6, 'T1': 7,
                'M3': 8, 'M2': 9, 'M1': 10,
                'L3': 11, 'L2': 12, 'L1': 13
            }

            # 按自定义顺序排序
            sorted_levels = sorted(levels, key=lambda x: level_order.get(x, 999))

            # 构建返回数据
            level_list = [
                {
                    "value": level,
                    "label": f"{level}级"
                }
                for level in sorted_levels
            ]

            return Response({
                "success": True,
                "levels": level_list
            })

        except Exception as e:
            logger.error(f"Error getting car levels: {str(e)}")
            return Response(
                {'error': '获取赛车等级失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def check_permission(self, request):
        """
        检查用户是否有赛车管理权限

        @return {
            "success": boolean,
            "message": string
        }
        """
        try:
            # 检查用户是否已登录
            if request.user.is_anonymous:
                return Response({
                    "success": False,
                    "message": "未登录用户"
                }, status=status.HTTP_401_UNAUTHORIZED)

            # 检查用户的赛车权限
            has_permission = getattr(request.user, 'has_car_permission', 0)

            if has_permission:
                return Response({
                    "success": True,
                    "message": "有权限访问"
                })
            else:
                return Response({
                    "success": False,
                    "message": "无权限访问"
                }, status=status.HTTP_403_FORBIDDEN)

        except Exception as e:
            logger.error(f"Error checking permission: {str(e)}")
            return Response({
                'error': '权限检查失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    def search(self, request):
        """
        根据赛车名称或编号查询赛车信息和对应图片

        接受参数:
        - query: 查询字符串，可以是赛车名称或编号的部分内容

        返回:
        - 匹配的赛车名称和图片信息
        """
        try:
            query = request.query_params.get('query', '')
            if not query:
                return Response({
                    'success': False,
                    'message': '请提供查询参数',
                    'data': []
                }, status=status.HTTP_400_BAD_REQUEST)

            # 查询赛车
            cars = Car.objects.filter(
                Q(car_id__icontains=query) |
                Q(name__icontains=query)
            ).values('car_id', 'name', 'image_id')

            # 准备结果数据
            results = []
            for car in cars:
                car_data = {
                    'car_id': car['car_id'],
                    'name': car['name'],
                    'image_id': car['image_id']
                }

                # 如果有图片ID，构建图片URL
                if car['image_id']:
                    car_data['image_url'] = f"/media/car_images/{car['image_id']}"
                else:
                    car_data['image_url'] = None

                results.append(car_data)

            return Response({
                'success': True,
                'message': f'找到 {len(results)} 条匹配记录',
                'data': results
            })

        except Exception as e:
            logger.error(f"搜索赛车出错: {str(e)}")
            return Response({
                'success': False,
                'message': f'搜索赛车出错: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_user_rating(request):
    """
    获取用户对特定赛车的评分
    请求参数：
    - car_id: 赛车编号
    - openid: 用户openid
    """
    try:
        car_id = request.query_params.get('car_id')
        openid = request.query_params.get('openid')

        if not car_id or not openid:
            return Response({
                'error': '缺少必要参数'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 直接查询评分记录
            rating = CarRating.objects.get(car_id=car_id, openid=openid)
            return Response({
                'car_id': car_id,
                'speed_rating': float(rating.speed_rating),
                'handling_rating': float(rating.handling_rating),
                'value_rating': float(rating.value_rating),
                'combat_rating': float(rating.combat_rating),
                'appearance_rating': float(rating.appearance_rating),  # 添加颜值评分
                'overall_rating': float(rating.overall_rating)
            })
        except CarRating.DoesNotExist:
            return Response({
                'car_id': car_id,
                'speed_rating': 0,
                'handling_rating': 0,
                'value_rating': 0,
                'combat_rating': 0,
                'appearance_rating': 0,  # 添加颜值评分
                'overall_rating': 0
            })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def submit_rating(request):
    """
    提交赛车评分
    请求参数：
    {
        "car_id": "赛车编号",
        "openid": "用户openid",
        "speed_rating": 评分(1-10),
        "handling_rating": 评分(1-10),
        "value_rating": 评分(1-10),
        "combat_rating": 评分(1-10),
        "appearance_rating": 评分(1-10)
    }
    """
    try:
        car_id = request.data.get('car_id')
        openid = request.data.get('openid')

        if not car_id or not openid:
            return Response({
                'error': '缺少必要参数'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查车辆是否存在
        try:
            car = Car.objects.get(car_id=car_id)
        except Car.DoesNotExist:
            return Response({
                'error': '车辆不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 验证评分范围
        ratings = {
            'car_id': car_id  # 添加 car_id 到评分数据中
        }
        for rating_field in ['speed_rating', 'handling_rating', 'value_rating', 'combat_rating', 'appearance_rating']:
            try:
                rating = float(request.data.get(rating_field, 0))
                if not 1 <= rating <= 10:
                    return Response({
                        'error': f'{rating_field}必须在1-10之间'
                    }, status=status.HTTP_400_BAD_REQUEST)
                ratings[rating_field] = rating
            except (TypeError, ValueError):
                return Response({
                    'error': f'{rating_field}必须是有效的数字'
                }, status=status.HTTP_400_BAD_REQUEST)

        # 计算加权平均分
        overall_rating = (
            ratings['speed_rating'] * 0.25 +
            ratings['handling_rating'] * 0.25 +
            ratings['combat_rating'] * 0.20 +
            ratings['value_rating'] * 0.15 +
            ratings['appearance_rating'] * 0.15
        )
        ratings['overall_rating'] = round(overall_rating, 1)

        # 创建或更新评分
        rating, created = CarRating.objects.update_or_create(
            car_id=car_id,
            openid=openid,
            defaults=ratings
        )

        # 返回更新后的评分信息
        return Response({
            'message': '评分提交成功',
            'car_id': car_id,
            'overall_rating': float(rating.overall_rating),
            'ratings': {
                'speed_rating': float(rating.speed_rating),
                'handling_rating': float(rating.handling_rating),
                'value_rating': float(rating.value_rating),
                'combat_rating': float(rating.combat_rating),
                'appearance_rating': float(rating.appearance_rating)
            }
        })
    except Exception as e:
        logger.error(f"Error submitting rating: {str(e)}")
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def get_car_ratings(request, car_id):
    """
    获取赛车评分统计信息
    返回：
    {
        "total_ratings": 评分总人数,
        "speed_avg": 速度平均分,
        "handling_avg": 手感平均分,
        "value_avg": 性价比平均分,
        "combat_avg": 对抗平均分,
        "appearance_avg": 颜值平均分,
        "overall_avg": 综合评分平均分
    }
    """
    try:
        # 检查车辆是否存在
        try:
            car = Car.objects.get(car_id=car_id)
        except Car.DoesNotExist:
            return Response({
                'error': '车辆不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 直接使用 car_id 查询评分
        ratings = CarRating.objects.filter(car_id=car_id)
        stats = ratings.aggregate(
            total_ratings=Count('id'),
            speed_avg=Avg('speed_rating'),
            handling_avg=Avg('handling_rating'),
            value_avg=Avg('value_rating'),
            combat_avg=Avg('combat_rating'),
            appearance_avg=Avg('appearance_rating'),  # 添加颜值评分
            overall_avg=Avg('overall_rating')
        )

        # 处理没有评分的情况
        if not stats['total_ratings']:
            return Response({
                'total_ratings': 0,
                'speed_avg': 0,
                'handling_avg': 0,
                'value_avg': 0,
                'combat_avg': 0,
                'appearance_avg': 0,  # 添加颜值评分
                'overall_avg': 0
            })

        # 四舍五入到一位小数
        for key in stats:
            if key != 'total_ratings' and stats[key] is not None:
                stats[key] = round(float(stats[key]), 1)

        return Response(stats)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def create_comment(request):
    """
    创建赛车评论

    请求参数：
    {
        "car_id": "string",  # 赛车编号
        "content": "string", # 评论内容
        "openid": "string"   # 用户openid（可选，优先使用请求头中的X-Openid）
    }
    请求头：
    X-Openid: string  # 用户openid（可选，优先级高于请求体中的openid）
    """
    try:
        car_id = request.data.get('car_id')
        content = request.data.get('content')
        # 按优先级获取openid：请求头 > 请求体 > session
        openid = request.headers.get('X-Openid') or request.data.get('openid') or request.session.get('openid')

        # 参数验证
        if not all([car_id, content, openid]):
            return Response({
                'error': '缺少必要参数',
                'detail': {
                    'car_id': '赛车编号不能为空' if not car_id else None,
                    'content': '评论内容不能为空' if not content else None,
                    'openid': '用户身份未提供' if not openid else None
                }
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证赛车是否存在
        if not Car.objects.filter(car_id=car_id).exists():
            return Response({
                'error': '赛车不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 创建评论
        comment = CarComment.objects.create(
            car_id=car_id,
            openid=openid,
            content=content
        )

        return Response({
            'success': True,
            'message': '评论创建成功',
            'data': {
                'id': comment.id,
                'content': comment.content,
                'created_at': comment.created_at
            }
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"创建评论失败: {str(e)}")
        return Response({
            'error': '创建评论失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
def delete_comment(request, comment_id):
    """
    删除评论

    请求参数：
    - openid: 用户openid（可选，优先使用请求头中的X-Openid）

    请求头：
    X-Openid: string  # 用户openid（可选，优先级高于请求参数中的openid）
    """
    try:
        # 按优先级获取openid：请求头 > 请求参数 > session
        openid = (request.headers.get('X-Openid') or
                 request.query_params.get('openid') or
                 request.session.get('openid'))

        if not openid:
            return Response({
                'error': '用户身份未提供'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 查找评论
        comment = CarComment.objects.filter(
            id=comment_id,
            openid=openid,
            is_deleted=False
        ).first()

        if not comment:
            return Response({
                'error': '评论不存在或无权删除'
            }, status=status.HTTP_404_NOT_FOUND)

        # 软删除评论
        comment.soft_delete()

        return Response({
            'success': True,
            'message': '评论删除成功'
        })

    except Exception as e:
        logger.error(f"删除评论失败: {str(e)}")
        return Response({
            'error': '删除评论失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_car_comments(request, car_id):
    """
    获取指定赛车的评论列表

    请求参数：
    - page: 页码（默认1）
    - page_size: 每页数量（默认10）
    - openid: 用户openid（可选，用于判断是否可以编辑和删除评论）

    请求头：
    X-Openid: string  # 用户openid（可选，优先级高于请求参数中的openid）
    """
    try:
        # 验证赛车是否存在
        if not Car.objects.filter(car_id=car_id).exists():
            return Response({
                'error': '赛车不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取分页参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))

        # 按优先级获取openid：请求头 > 请求参数 > session
        current_openid = (request.headers.get('X-Openid') or
                         request.query_params.get('openid') or
                         request.session.get('openid', ''))

        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询评论
        comments = CarComment.objects.filter(
            car_id=car_id,
            is_deleted=False
        ).order_by('-created_at')[offset:offset + page_size]

        # 获取评论总数
        total_count = CarComment.objects.filter(
            car_id=car_id,
            is_deleted=False
        ).count()

        # 构建响应数据
        comment_list = [{
            'id': comment.id,
            'content': comment.content,
            'openid': comment.openid,
            'created_at': comment.created_at,
            'can_edit': comment.openid == current_openid,  # 是否可以编辑
            'can_delete': comment.openid == current_openid  # 是否可以删除
        } for comment in comments]

        return Response({
            'success': True,
            'data': {
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'list': comment_list
            }
        })

    except Exception as e:
        logger.error(f"获取评论列表失败: {str(e)}")
        return Response({
            'error': '获取评论列表失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def admin_get_comments(request):
    """
    管理员获取评论列表

    请求参数：
    - page: 页码（默认1）
    - page_size: 每页数量（默认20）
    - car_id: 赛车编号（可选）
    - car_name: 赛车名称（可选）
    - car_level: 赛车等级（可选，如：S、A、B等）
    - openid: 用户openid（可选）
    - is_deleted: 是否已删除（可选，true/false）
    - start_time: 开始时间（可选，格式：YYYY-MM-DD HH:mm:ss）
    - end_time: 结束时间（可选，格式：YYYY-MM-DD HH:mm:ss）
    - keyword: 关键词搜索（可选，搜索评论内容）
    """
    try:
        # 获取查询参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        car_id = request.query_params.get('car_id')
        car_name = request.query_params.get('car_name')
        car_level = request.query_params.get('car_level')
        user_openid = request.query_params.get('openid')
        is_deleted = request.query_params.get('is_deleted')
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        keyword = request.query_params.get('keyword')

        # 构建查询条件
        query = Q()

        # 赛车相关的查询条件（ID、名称、等级）
        car_query = Q()
        if car_id:
            car_query |= Q(car_id=car_id)
        if car_name:
            # 按名称查找赛车ID
            car_ids_by_name = Car.objects.filter(name__icontains=car_name).values_list('car_id', flat=True)
            if car_ids_by_name:
                car_query |= Q(car_id__in=car_ids_by_name)
        if car_level:
            # 按等级查找赛车ID
            car_ids_by_level = Car.objects.filter(level=car_level).values_list('car_id', flat=True)
            if car_ids_by_level:
                car_query |= Q(car_id__in=car_ids_by_level)

        # 如果有任何赛车相关的查询条件，添加到主查询中
        if car_query:
            query &= car_query

        if user_openid:
            query &= Q(openid=user_openid)
        if is_deleted is not None:
            query &= Q(is_deleted=(is_deleted.lower() == 'true'))
        if start_time:
            query &= Q(created_at__gte=start_time)
        if end_time:
            query &= Q(created_at__lte=end_time)
        if keyword:
            query &= Q(content__icontains=keyword)

        # 查询评论
        comments = CarComment.objects.filter(query).order_by('-created_at')
        total_count = comments.count()

        # 分页
        offset = (page - 1) * page_size
        comments = comments[offset:offset + page_size]

        # 获取相关的赛车信息
        car_ids = {comment.car_id for comment in comments}
        cars = {car.car_id: car for car in Car.objects.filter(car_id__in=car_ids)}

        # 构建响应数据
        comment_list = []
        for comment in comments:
            car = cars.get(comment.car_id)
            comment_data = {
                'id': comment.id,
                'car_id': comment.car_id,
                'car_name': car.name if car else None,
                'car_level': car.level if car else None,
                'openid': comment.openid,
                'content': comment.content,
                'is_deleted': comment.is_deleted,
                'created_at': comment.created_at,
                'updated_at': comment.updated_at,
                'deleted_at': comment.deleted_at
            }
            comment_list.append(comment_data)

        return Response({
            'success': True,
            'data': {
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'list': comment_list
            }
        })

    except ValueError as ve:
        return Response({
            'error': '参数格式错误',
            'detail': str(ve)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"管理员查询评论失败: {str(e)}")
        return Response({
            'error': '查询评论失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([AllowAny])  # 允许任何人访问
def admin_delete_comment(request, comment_id):
    """
    管理员删除评论（软删除）
    """
    try:
        # 查找评论
        comment = CarComment.objects.filter(
            id=comment_id,
            is_deleted=False
        ).first()

        if not comment:
            return Response({
                'error': '评论不存在或已删除'
            }, status=status.HTTP_404_NOT_FOUND)

        # 软删除评论
        comment.soft_delete()

        return Response({
            'success': True,
            'message': '评论删除成功'
        })

    except Exception as e:
        logger.error(f"管理员删除评论失败: {str(e)}")
        return Response({
            'error': '删除评论失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_user_comments(request):
    """
    获取用户的评论列表

    请求参数：
    - page: 页码（默认1）
    - page_size: 每页数量（默认10）
    """
    try:
        openid = request.session.get('openid')
        if not openid:
            return Response({
                'error': '用户未登录'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 获取分页参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))

        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询评论
        comments = CarComment.objects.filter(
            openid=openid,
            is_deleted=False
        ).order_by('-created_at')[offset:offset + page_size]

        # 获取评论总数
        total_count = CarComment.objects.filter(
            openid=openid,
            is_deleted=False
        ).count()

        # 构建响应数据
        comment_list = []
        for comment in comments:
            car = comment.car
            comment_data = {
                'id': comment.id,
                'car_id': comment.car_id,
                'car_name': car.name if car else None,
                'content': comment.content,
                'created_at': comment.created_at,
                'can_edit': True,  # 用户可以编辑自己的评论
                'can_delete': True  # 用户可以删除自己的评论
            }
            comment_list.append(comment_data)

        return Response({
            'success': True,
            'data': {
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'list': comment_list
            }
        })

    except ValueError as ve:
        return Response({
            'error': '参数格式错误',
            'detail': str(ve)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"获取用户评论列表失败: {str(e)}")
        return Response({
            'error': '获取评论列表失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
def edit_comment(request, comment_id):
    """
    编辑评论

    请求参数：
    {
        "content": "string",  # 新的评论内容
        "openid": "string"    # 用户openid（可选，优先使用请求头中的X-Openid）
    }

    请求头：
    X-Openid: string  # 用户openid（可选，优先级高于请求体中的openid）
    """
    try:
        # 按优先级获取openid：请求头 > 请求体 > session
        openid = (request.headers.get('X-Openid') or
                 request.data.get('openid') or
                 request.session.get('openid'))

        if not openid:
            return Response({
                'error': '用户身份未提供'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 获取评论内容
        content = request.data.get('content')
        if not content:
            return Response({
                'error': '评论内容不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 查找评论
        comment = CarComment.objects.filter(
            id=comment_id,
            openid=openid,
            is_deleted=False
        ).first()

        if not comment:
            return Response({
                'error': '评论不存在或无权编辑'
            }, status=status.HTTP_404_NOT_FOUND)

        # 更新评论内容
        comment.content = content
        comment.save()

        return Response({
            'success': True,
            'message': '评论更新成功',
            'data': {
                'id': comment.id,
                'content': comment.content,
                'updated_at': comment.updated_at
            }
        })

    except Exception as e:
        logger.error(f"编辑评论失败: {str(e)}")
        return Response({
            'error': '编辑评论失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def report_comment(request, comment_id):
    """
    举报评论

    请求参数：
    {
        "reason": "string",  # 举报原因
        "openid": "string"   # 用户openid（可选，优先使用请求头中的X-Openid）
    }

    请求头：
    X-Openid: string  # 用户openid（可选，优先级高于请求体中的openid）
    """
    try:
        # 按优先级获取openid：请求头 > 请求体 > session
        openid = (request.headers.get('X-Openid') or
                 request.data.get('openid') or
                 request.session.get('openid'))

        if not openid:
            return Response({
                'error': '用户openid不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取举报原因
        reason = request.data.get('reason')
        if not reason:
            return Response({
                'error': '举报原因不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 查找评论
        comment = CarComment.objects.filter(
            id=comment_id,
            is_deleted=False
        ).first()

        if not comment:
            return Response({
                'error': '评论不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 检查是否已经举报过
        if CommentReport.objects.filter(
            comment=comment,
            reporter_openid=openid,
            status__in=['pending', 'processing']
        ).exists():
            return Response({
                'error': '您已举报过该评论，请等待处理'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建举报记录
        report = CommentReport.objects.create(
            comment=comment,
            reporter_openid=openid,
            reason=reason
        )

        return Response({
            'success': True,
            'message': '举报提交成功',
            'data': {
                'report_id': report.id,
                'status': report.status,
                'created_at': report.created_at
            }
        })

    except Exception as e:
        logger.error(f"举报评论失败: {str(e)}")
        return Response({
            'error': '举报失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def admin_get_reports(request):
    """
    管理员获取举报列表

    请求参数：
    - page: 页码（默认1）
    - page_size: 每页数量（默认20）
    - status: 处理状态（可选，多个状态用逗号分隔，如：pending,processing,resolved,rejected）
    - reporter_openid: 举报人openid（可选）
    - car_id: 赛车编号（可选）
    - car_name: 赛车名称（可选）
    - start_time: 开始时间（可选，格式：YYYY-MM-DD HH:mm:ss）
    - end_time: 结束时间（可选，格式：YYYY-MM-DD HH:mm:ss）
    - keyword: 关键词搜索（可选，搜索评论内容）
    """
    try:
        # 获取查询参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        status_list = request.query_params.get('status', '').split(',')
        reporter_openid = request.query_params.get('reporter_openid')
        car_id = request.query_params.get('car_id')
        car_name = request.query_params.get('car_name')
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        keyword = request.query_params.get('keyword')

        # 构建查询条件
        query = Q()

        # 状态查询
        if status_list and status_list[0]:  # 确保不是空字符串
            status_query = Q()
            for status in status_list:
                if status.strip():  # 忽略空字符串
                    status_query |= Q(status=status.strip())
            query &= status_query

        # 举报人查询
        if reporter_openid:
            query &= Q(reporter_openid=reporter_openid)

        # 赛车相关查询
        if car_id:
            query &= Q(comment__car_id=car_id)
        if car_name:
            # 先查找符合名称的赛车ID
            car_ids = Car.objects.filter(name__icontains=car_name).values_list('car_id', flat=True)
            if car_ids:
                query &= Q(comment__car_id__in=car_ids)

        # 时间范围查询
        if start_time:
            query &= Q(created_at__gte=start_time)
        if end_time:
            query &= Q(created_at__lte=end_time)

        # 评论内容关键词查询
        if keyword:
            query &= Q(comment__content__icontains=keyword)

        # 查询举报记录，使用select_related优化查询
        reports = CommentReport.objects.select_related('comment').filter(query).order_by('-created_at')
        total_count = reports.count()

        # 分页
        offset = (page - 1) * page_size
        reports = reports[offset:offset + page_size]

        # 获取相关的赛车信息
        car_ids = {report.comment.car_id for report in reports if report.comment}
        cars = {car.car_id: car for car in Car.objects.filter(car_id__in=car_ids)}

        # 构建响应数据
        report_list = []
        for report in reports:
            comment = report.comment
            car = cars.get(comment.car_id) if comment else None
            report_data = {
                'id': report.id,
                'comment_id': comment.id if comment else None,
                'comment_content': comment.content if comment else None,
                'car_id': car.car_id if car else None,
                'car_name': car.name if car else None,
                'car_level': car.level if car else None,  # 添加赛车等级信息
                'reporter_openid': report.reporter_openid,
                'reason': report.reason,
                'status': report.status,
                'handler_note': report.handler_note,
                'created_at': report.created_at,
                'handled_at': report.handled_at
            }
            report_list.append(report_data)

        return Response({
            'success': True,
            'data': {
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'list': report_list
            }
        })

    except ValueError as ve:
        return Response({
            'error': '参数格式错误',
            'detail': str(ve)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"获取举报列表失败: {str(e)}")
        return Response({
            'error': '获取举报列表失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])  # 允许任何人访问
def admin_handle_report(request, report_id):
    """
    管理员处理举报

    请求参数：
    {
        "action": "string",  # 处理动作：resolve（处理）/ reject（驳回）
        "note": "string"     # 处理备注
    }
    """
    try:
        # 获取处理参数
        action = request.data.get('action')
        note = request.data.get('note')

        if action not in ['resolve', 'reject']:
            return Response({
                'error': '无效的处理动作'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 查找举报记录
        report = CommentReport.objects.filter(id=report_id).first()
        if not report:
            return Response({
                'error': '举报记录不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 检查是否可以处理
        if report.status not in ['pending', 'processing']:
            return Response({
                'error': '该举报已处理'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 更新举报状态
        from django.utils import timezone
        report.status = 'resolved' if action == 'resolve' else 'rejected'
        report.handler_note = note
        report.handled_at = timezone.now()
        report.save()

        # 如果是处理（而不是驳回），则删除相关评论
        if action == 'resolve':
            comment = report.comment
            if comment and not comment.is_deleted:
                comment.soft_delete()

        return Response({
            'success': True,
            'message': '处理成功',
            'data': {
                'report_id': report.id,
                'status': report.status,
                'handled_at': report.handled_at
            }
        })

    except Exception as e:
        logger.error(f"处理举报失败: {str(e)}")
        return Response({
            'error': '处理失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def check_sensitive_words(request):
    """
    检查文本是否包含敏感词

    请求参数：
    {
        "content": "string"  # 待检查的文本内容
    }
    """
    try:
        content = request.data.get('content')
        if not content:
            return Response({
                'error': '待检查内容不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 查询所有启用的敏感词
        sensitive_words = SensitiveWord.objects.filter(is_active=True).values_list('word', flat=True)

        # 检查是否包含敏感词
        found_words = []
        for word in sensitive_words:
            if word in content:
                found_words.append(word)

        # 如果本地敏感词库没有找到敏感词，建议调用腾讯云检测
        should_check_tencent = not bool(found_words)
        message = "内容正常" if not found_words else "发现敏感词，请修改内容"

        return Response({
            'success': True,
            'has_sensitive': bool(found_words),
            'found_words': found_words,
            'should_check_tencent': should_check_tencent,
            'message': message
        })

    except Exception as e:
        logger.error(f"检查敏感词失败: {str(e)}")
        return Response({
            'error': '检查失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def add_sensitive_word(request):
    """
    添加敏感词

    请求参数：
    {
        "word": "string",     # 敏感词
        "source": "string",   # 来源：manual/tencent
        "remark": "string"    # 备注（可选）
    }
    """
    try:
        word = request.data.get('word')
        source = request.data.get('source')
        remark = request.data.get('remark')

        if not word:
            return Response({
                'error': '敏感词不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        if source not in ['manual', 'tencent']:
            return Response({
                'error': '无效的来源'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建或更新敏感词
        sensitive_word, created = SensitiveWord.objects.update_or_create(
            word=word,
            defaults={
                'source': source,
                'remark': remark,
                'is_active': True
            }
        )

        return Response({
            'success': True,
            'message': '敏感词添加成功',
            'data': {
                'id': sensitive_word.id,
                'word': sensitive_word.word,
                'source': sensitive_word.source,
                'created_at': sensitive_word.created_at
            }
        })

    except Exception as e:
        logger.error(f"添加敏感词失败: {str(e)}")
        return Response({
            'error': '添加失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([AllowAny])
def delete_sensitive_word(request, word_id):
    """
    删除敏感词（物理删除）
    """
    try:
        # 查找敏感词
        sensitive_word = get_object_or_404(SensitiveWord, id=word_id)

        # 执行物理删除
        sensitive_word.delete()

        return Response({
            'success': True,
            'message': '删除成功'
        })

    except SensitiveWord.DoesNotExist:
        return Response({
            'success': False,
            'message': '敏感词不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"删除敏感词失败: {str(e)}")
        return Response({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def get_sensitive_words(request):
    """
    获取敏感词列表

    请求参数：
    - page: 页码（默认1）
    - page_size: 每页数量（默认20）
    - source: 来源（可选：manual/tencent）
    - is_active: 是否启用（可选：true/false）
    - keyword: 关键词搜索（可选）
    """
    try:
        # 获取查询参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        source = request.query_params.get('source')
        is_active = request.query_params.get('is_active')
        keyword = request.query_params.get('keyword')

        # 构建查询条件
        query = Q()
        if source:
            query &= Q(source=source)
        if is_active is not None:
            query &= Q(is_active=(is_active.lower() == 'true'))
        if keyword:
            query &= Q(word__icontains=keyword)

        # 计算分页
        offset = (page - 1) * page_size

        # 查询敏感词
        words = SensitiveWord.objects.filter(query).order_by('-created_at')
        total_count = words.count()
        words = words[offset:offset + page_size]

        # 构建响应数据
        word_list = [{
            'id': word.id,
            'word': word.word,
            'source': word.source,
            'is_active': word.is_active,
            'remark': word.remark,
            'created_at': word.created_at,
            'updated_at': word.updated_at
        } for word in words]

        return Response({
            'success': True,
            'data': {
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'list': word_list
            }
        })

    except ValueError as ve:
        return Response({
            'error': '参数格式错误',
            'detail': str(ve)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"获取敏感词列表失败: {str(e)}")
        return Response({
            'error': '获取失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def batch_add_sensitive_words(request):
    """
    批量导入敏感词
    使用模板文件上传，只读取第一列(敏感词)和第三列(备注)
    其他字段使用默认值：
    - 来源：手动添加
    - 状态：启用
    """
    try:
        excel_file = request.FILES.get('file')
        if not excel_file:
            return Response({
                'success': False,
                'message': '请上传Excel文件'
            })

        wb = openpyxl.load_workbook(excel_file)
        ws = wb.active

        # 跳过表头
        success_count = 0
        error_words = []

        for row in ws.iter_rows(min_row=2):
            word = row[0].value  # 第一列：敏感词
            remark = row[2].value if len(row) > 2 else None  # 第三列：备注

            if not word:  # 跳过空行
                continue

            try:
                # 创建敏感词，使用默认值
                SensitiveWord.objects.create(
                    word=word,
                    source='manual',  # 默认来源：手动添加
                    remark=remark,
                    is_active=True  # 默认状态：启用
                )
                success_count += 1
            except Exception as e:
                error_words.append({
                    'word': word,
                    'error': str(e)
                })

        return Response({
            'success': True,
            'data': {
                'success_count': success_count,
                'error_words': error_words
            }
        })

    except Exception as e:
        logger.error(f"批量导入敏感词失败: {str(e)}")
        return Response({
            'success': False,
            'message': f'导入失败: {str(e)}'
        })

@api_view(['POST'])
@permission_classes([AllowAny])
def enable_sensitive_word(request, word_id):
    """
    启用敏感词
    """
    try:
        # 查找敏感词
        word = SensitiveWord.objects.filter(id=word_id).first()
        if not word:
            return Response({
                'error': '敏感词不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 启用敏感词
        word.is_active = True
        word.save()

        return Response({
            'success': True,
            'message': '敏感词启用成功',
            'data': {
                'id': word.id,
                'word': word.word,
                'source': word.source,
                'is_active': word.is_active,
                'updated_at': word.updated_at
            }
        })

    except Exception as e:
        logger.error(f"启用敏感词失败: {str(e)}")
        return Response({
            'error': '启用失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def export_sensitive_words_template(request):
    """
    导出敏感词列表数据
    支持参数：
    - keyword: 关键词搜索
    - source: 来源筛选(manual/tencent)
    - is_active: 是否启用(true/false)
    """
    try:
        # 获取查询参数
        keyword = request.GET.get('keyword', '').strip()
        source = request.GET.get('source', '').strip()
        is_active = request.GET.get('is_active')

        # 构建查询条件
        query = Q()
        if keyword:
            query &= Q(word__icontains=keyword)
        if source:
            query &= Q(source=source)
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            query &= Q(is_active=is_active)

        # 查询数据
        sensitive_words = SensitiveWord.objects.filter(query).order_by('-created_at')

        # 读取模板文件
        template_path = os.path.join(settings.BASE_DIR, 'templates', 'sensitive_template.xlsx')
        wb = openpyxl.load_workbook(template_path)
        ws = wb.active

        # 从第二行开始添加数据（保留表头）
        for row_idx, word in enumerate(sensitive_words, 2):
            # 格式化数据
            source_display = '手动添加' if word.source == 'manual' else '腾讯云检测'
            status_display = '启用' if word.is_active else '禁用'

            # 按模板格式填充数据
            ws.cell(row=row_idx, column=1).value = word.word  # 敏感词
            ws.cell(row=row_idx, column=2).value = source_display  # 来源
            ws.cell(row=row_idx, column=3).value = word.remark or ''  # 备注
            ws.cell(row=row_idx, column=4).value = status_display  # 状态
            ws.cell(row=row_idx, column=5).value = word.created_at.strftime('%Y-%m-%d %H:%M:%S')  # 创建时间
            ws.cell(row=row_idx, column=6).value = word.updated_at.strftime('%Y-%m-%d %H:%M:%S')  # 更新时间

            # 设置单元格样式
            for col in range(1, 7):
                cell = ws.cell(row=row_idx, column=col)
                cell.alignment = Alignment(horizontal='center', vertical='center')

        # 保存到内存中
        excel_file = io.BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)

        # 生成文件名
        filename = f'sensitive_words_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        # 设置响应头
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename={filename}'

        return response

    except Exception as e:
        logger.error(f"导出敏感词列表失败: {str(e)}")
        return Response({
            'success': False,
            'message': f'导出失败: {str(e)}'
        })

@api_view(['GET'])
@permission_classes([AllowAny])
def search_car_by_name_id(request):
    """
    根据赛车名称或编号查询赛车信息和对应图片

    接受参数:
    - query: 查询字符串，可以是赛车名称或编号的部分内容

    返回:
    - 匹配的赛车名称和图片信息
    """
    try:
        query = request.query_params.get('query', '')
        if not query:
            return Response({
                'success': False,
                'message': '请提供查询参数',
                'data': []
            }, status=status.HTTP_400_BAD_REQUEST)

        # 查询赛车
        cars = Car.objects.filter(
            Q(car_id__icontains=query) |
            Q(name__icontains=query)
        ).values('car_id', 'name', 'image_id')

        # 准备结果数据
        results = []
        for car in cars:
            car_data = {
                'car_id': car['car_id'],
                'name': car['name'],
                'image_id': car['image_id']
            }

            # 如果有图片ID，构建图片URL
            if car['image_id']:
                car_data['image_url'] = f"/media/car_images/{car['image_id']}"
            else:
                car_data['image_url'] = None

            results.append(car_data)

        return Response({
            'success': True,
            'message': f'找到 {len(results)} 条匹配记录',
            'data': results
        })

    except Exception as e:
        logger.error(f"搜索赛车出错: {str(e)}")
        return Response({
            'success': False,
            'message': f'搜索赛车出错: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PropulsionLevelTableViewSet(viewsets.ModelViewSet):
    """
    推进计算表管理视图集

    提供标准的CRUD接口和以下扩展接口:
    - 获取赛车等级列表 (GET /api/propulsion-levels/levels/)
    - 批量导入数据 (POST /api/propulsion-levels/import_data/)
    - 导出Excel (GET /api/propulsion-levels/export_excel/)
    - 导入Excel (POST /api/propulsion-levels/import_excel/)
    """
    queryset = PropulsionLevelTable.objects.all()
    serializer_class = PropulsionLevelTableSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        支持按以下字段筛选:
        - car_level: 赛车等级
        """
        queryset = PropulsionLevelTable.objects.all().order_by('id')

        # 按赛车等级筛选
        car_level = self.request.query_params.get('car_level')
        if car_level:
            queryset = queryset.filter(car_level=car_level)

        return queryset

    @action(detail=False, methods=['get'])
    def levels(self, request):
        """获取所有赛车等级列表"""
        try:
            levels = PropulsionLevelTable.objects.values_list('car_level', flat=True).distinct()
            return Response({
                'success': True,
                'levels': list(levels)
            })
        except Exception as e:
            logger.error(f"获取赛车等级列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取赛车等级列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def import_data(self, request):
        """批量导入或更新推进计算表数据"""
        try:
            data_list = request.data.get('data', [])
            if not data_list:
                return Response({
                    'success': False,
                    'message': '没有提供数据'
                }, status=status.HTTP_400_BAD_REQUEST)

            created_count = 0
            updated_count = 0
            errors = []

            for item_data in data_list:
                car_level = item_data.get('car_level')
                if not car_level:
                    errors.append('缺少赛车等级字段')
                    continue

                try:
                    # 尝试获取现有记录
                    obj, created = PropulsionLevelTable.objects.update_or_create(
                        car_level=car_level,
                        defaults=item_data
                    )

                    if created:
                        created_count += 1
                    else:
                        updated_count += 1

                except Exception as e:
                    errors.append(f'处理等级 {car_level} 时出错: {str(e)}')

            return Response({
                'success': True,
                'message': f'导入完成：创建 {created_count} 条，更新 {updated_count} 条',
                'created_count': created_count,
                'updated_count': updated_count,
                'errors': errors
            })

        except Exception as e:
            logger.error(f"批量导入数据失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'批量导入数据失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        """导出Excel文件"""
        try:
            # 获取所有推进计算表数据
            propulsion_tables = self.get_queryset()

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "推进计算表"

            # 定义表头
            headers = [
                'ID', '赛车等级',
                '推进1上限', '推进2上限', '推进3上限', '推进4上限', '推进5上限', '推进6上限', '推进7上限',
                '推进1差值', '推进2差值', '推进3差值', '推进4差值', '推进5差值', '推进6差值', '推进7差值',
                '推进1平均提升', '推进2平均提升', '推进3平均提升', '推进4平均提升',
                '推进5平均提升', '推进6平均提升', '推进7平均提升',
                '创建时间', '更新时间'
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # 写入数据
            for row, table in enumerate(propulsion_tables, 2):
                data = [
                    table.id,
                    table.car_level,
                    table.level_1_max,
                    table.level_2_max,
                    table.level_3_max,
                    table.level_4_max,
                    table.level_5_max,
                    table.level_6_max,
                    table.level_7_max,
                    table.level_1_diff,
                    table.level_2_diff,
                    table.level_3_diff,
                    table.level_4_diff,
                    table.level_5_diff,
                    table.level_6_diff,
                    table.level_7_diff,
                    table.level_1_avg_increase,
                    table.level_2_avg_increase,
                    table.level_3_avg_increase,
                    table.level_4_avg_increase,
                    table.level_5_avg_increase,
                    table.level_6_avg_increase,
                    table.level_7_avg_increase,
                    table.created_at.strftime('%Y-%m-%d %H:%M:%S') if table.created_at else '',
                    table.updated_at.strftime('%Y-%m-%d %H:%M:%S') if table.updated_at else ''
                ]

                for col, value in enumerate(data, 1):
                    ws.cell(row=row, column=col, value=value)

            # 调整列宽
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[get_column_letter(col)].width = 15

            # 创建响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="propulsion_level_table.xlsx"'

            # 保存到响应
            wb.save(response)
            return response

        except Exception as e:
            logger.error(f"导出Excel失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'导出Excel失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def import_excel(self, request):
        """导入Excel文件"""
        try:
            file = request.FILES.get('file')
            if not file:
                return Response({
                    'success': False,
                    'message': '没有上传文件'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查文件类型
            if not file.name.endswith(('.xlsx', '.xls')):
                return Response({
                    'success': False,
                    'message': '文件格式不正确，请上传Excel文件'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 读取Excel文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active

            # 获取表头行
            headers = []
            for cell in ws[1]:
                headers.append(cell.value)

            # 字段映射
            field_mapping = {
                'ID': 'id',
                '赛车等级': 'car_level',
                '推进1上限': 'level_1_max',
                '推进2上限': 'level_2_max',
                '推进3上限': 'level_3_max',
                '推进4上限': 'level_4_max',
                '推进5上限': 'level_5_max',
                '推进6上限': 'level_6_max',
                '推进7上限': 'level_7_max',
                '推进1差值': 'level_1_diff',
                '推进2差值': 'level_2_diff',
                '推进3差值': 'level_3_diff',
                '推进4差值': 'level_4_diff',
                '推进5差值': 'level_5_diff',
                '推进6差值': 'level_6_diff',
                '推进7差值': 'level_7_diff',
                '推进1平均提升': 'level_1_avg_increase',
                '推进2平均提升': 'level_2_avg_increase',
                '推进3平均提升': 'level_3_avg_increase',
                '推进4平均提升': 'level_4_avg_increase',
                '推进5平均提升': 'level_5_avg_increase',
                '推进6平均提升': 'level_6_avg_increase',
                '推进7平均提升': 'level_7_avg_increase'
            }

            created_count = 0
            updated_count = 0
            errors = []

            # 处理数据行
            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                try:
                    # 构建数据字典
                    data = {}
                    for col_idx, value in enumerate(row):
                        if col_idx < len(headers):
                            header = headers[col_idx]
                            field_name = field_mapping.get(header)
                            if field_name and field_name != 'id':  # 跳过ID字段
                                # 处理空值
                                if value is None or value == '':
                                    if 'max' in field_name:  # 上限字段允许为空
                                        data[field_name] = None
                                    elif 'diff' in field_name or 'avg_increase' in field_name:
                                        data[field_name] = 0  # 差值和平均提升默认为0
                                    else:
                                        data[field_name] = value
                                else:
                                    data[field_name] = value

                    # 检查必填字段
                    if not data.get('car_level'):
                        errors.append(f'第{row_num}行：缺少赛车等级')
                        continue

                    # 创建或更新记录
                    obj, created = PropulsionLevelTable.objects.update_or_create(
                        car_level=data['car_level'],
                        defaults=data
                    )

                    if created:
                        created_count += 1
                    else:
                        updated_count += 1

                except Exception as e:
                    errors.append(f'第{row_num}行处理失败: {str(e)}')

            return Response({
                'success': True,
                'message': f'导入完成：创建 {created_count} 条，更新 {updated_count} 条',
                'created_count': created_count,
                'updated_count': updated_count,
                'errors': errors
            })

        except Exception as e:
            logger.error(f"导入Excel失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'导入Excel失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CalculationHistoryViewSet(viewsets.ModelViewSet):
    """
    计算历史记录管理视图集

    提供标准的CRUD接口和以下扩展接口:
    - 按用户ID筛选 (GET /api/calculation-history/?user_id=xxx)
    - 清空历史记录 (DELETE /api/calculation-history/clear/)
    """
    queryset = CalculationHistory.objects.all()
    serializer_class = CalculationHistorySerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        支持按以下字段筛选:
        - user_id: 用户ID
        """
        queryset = CalculationHistory.objects.all().order_by('-created_at')

        # 按用户ID筛选
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        return queryset

    @action(detail=False, methods=['delete'])
    def clear(self, request):
        """清空所有历史记录"""
        try:
            user_id = request.query_params.get('user_id')
            if user_id:
                # 只清空指定用户的历史记录
                deleted_count = CalculationHistory.objects.filter(user_id=user_id).delete()[0]
                message = f'已清空用户 {user_id} 的 {deleted_count} 条历史记录'
            else:
                # 清空所有历史记录
                deleted_count = CalculationHistory.objects.all().delete()[0]
                message = f'已清空所有 {deleted_count} 条历史记录'

            return Response({
                'success': True,
                'message': message,
                'deleted_count': deleted_count
            })

        except Exception as e:
            logger.error(f"清空历史记录失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'清空历史记录失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)