# Generated by Django 3.2.23 on 2025-03-23 10:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('treasure', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='treasureitem',
            name='expiry_days',
            field=models.IntegerField(blank=True, default=0, help_text='0表示永久', null=True, verbose_name='奖品期限(天)'),
        ),
        migrations.AlterField(
            model_name='treasureitem',
            name='item_type',
            field=models.CharField(max_length=50, verbose_name='奖品类型'),
        ),
        migrations.AlterField(
            model_name='treasureitem',
            name='quality',
            field=models.Char<PERSON>ield(max_length=50, verbose_name='奖品品质'),
        ),
    ]
