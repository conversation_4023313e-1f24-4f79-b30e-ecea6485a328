from django.db import models

# Create your models here.

class TreasureItem(models.Model):
    """
    赛车夺宝奖品配置模型
    """
    # 保留定义但不用于字段限制，仅用于API展示
    QUALITY_CHOICES = [
        ('top_s', '顶级227S车'),
        ('normal_s', '普通S级赛车'),
        ('t1_mecha', 'T1机甲'),
        ('normal_a', '普通A级赛车'),
        ('renault', '雷诺类型赛车'),
        ('parts', '改装配件')
    ]

    TYPE_CHOICES = [
        ('fixed', '固定奖品'),
        ('optional', '自选奖品')
    ]

    name = models.CharField('奖品名称', max_length=100)
    probability = models.CharField('奖品概率', max_length=50, help_text='概率值，支持任意文本格式')
    expiry_days = models.IntegerField('奖品期限(天)', default=0, help_text='0表示永久', null=True, blank=True)
    image_id = models.CharField('奖品图片ID', max_length=100, null=True, blank=True)
    quality = models.CharField('奖品品质', max_length=50)  # 移除choices限制
    item_type = models.CharField('奖品类型', max_length=50)  # 移除choices限制
    fragment_value = models.IntegerField('分解碎片数', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '夺宝奖品'
        verbose_name_plural = '夺宝奖品'
        ordering = ['-created_at']
        app_label = 'treasure'

    def __str__(self):
        return f"{self.name} - {self.quality} ({self.item_type})"


class PrizeSource(models.Model):
    """
    奖品来源模型（道具或模式）

    用于记录奖品的来源信息，可以是游戏道具或游戏模式
    """
    SOURCE_TYPE_CHOICES = [
        ('item', '道具'),
        ('mode', '模式'),
    ]

    source_code = models.CharField('道具编号', max_length=50, unique=True, null=True, blank=True, help_text='唯一的道具/模式编号，如：ITEM001')
    name = models.CharField('名称', max_length=100)
    source_type = models.CharField('类型', max_length=20, choices=SOURCE_TYPE_CHOICES, default='item')
    image_url = models.URLField('图片URL', max_length=500, null=True, blank=True)
    description = models.TextField('描述', null=True, blank=True)
    is_active = models.BooleanField('是否上架', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '奖品来源'
        verbose_name_plural = '奖品来源'
        ordering = ['-created_at']
        app_label = 'treasure'

    def __str__(self):
        return f"{self.name} ({self.get_source_type_display()})"


class Prize(models.Model):
    """
    奖品模型

    记录游戏中可获得的奖品信息
    """
    RARITY_CHOICES = [
        ('common', '普通'),
        ('rare', '稀有'),
        ('epic', '史诗'),
        ('legendary', '传说'),
    ]

    PRIZE_TYPE_CHOICES = [
        ('car', '赛车'),
        ('part', '配件'),
        ('item', '道具'),
        ('currency', '货币'),
        ('costume', '装饰'),
        ('action', '动作'),
        ('emotion', '表情'),
        ('horn', '喇叭'),
        ('gift', '礼包'),
        ('fragment', '碎片'),
        ('card', '卡片'),
        ('pet', '宠物'),
        ('title', '称号'),
        ('other', '其他'),
    ]

    prize_code = models.CharField('奖品编号', max_length=50, null=True, blank=True, help_text='奖品编号，如：PRIZE001')
    name = models.CharField('奖品名称', max_length=100)
    quantity = models.CharField('奖品数量', max_length=50, default='1')
    image_url = models.URLField('图片URL', max_length=500, null=True, blank=True)
    rarity = models.CharField('稀有度', max_length=20, choices=RARITY_CHOICES, default='common')
    probability = models.DecimalField('获取概率', max_digits=10, decimal_places=6, null=True, blank=True, help_text='百分比，例如5.25表示5.25%')
    description = models.TextField('奖品描述', null=True, blank=True)
    prize_type = models.CharField('奖品类型', max_length=50, default='其他')
    # 移除直接的外键关系，改为通过中间表关联
    # source = models.ForeignKey(PrizeSource, on_delete=models.CASCADE, related_name='prizes', verbose_name='所属来源')
    sources = models.ManyToManyField(PrizeSource, through='PrizeSourceRelation', related_name='related_prizes', verbose_name='所属来源')
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '奖品'
        verbose_name_plural = '奖品'
        ordering = ['-created_at']
        app_label = 'treasure'

    def __str__(self):
        sources_str = ", ".join([source.name for source in self.sources.all()[:3]])
        if self.sources.count() > 3:
            sources_str += f" 等{self.sources.count()}个来源"
        return f"{self.name} - {self.get_rarity_display()} (来源: {sources_str})"


class PrizeSourceRelation(models.Model):
    """
    奖品与来源的关联表

    用于记录奖品与来源之间的多对多关系
    """
    prize = models.ForeignKey(Prize, on_delete=models.CASCADE, related_name='source_relations', verbose_name='奖品')
    source = models.ForeignKey(PrizeSource, on_delete=models.CASCADE, related_name='prize_relations', verbose_name='来源')
    probability = models.DecimalField('获取概率', max_digits=10, decimal_places=6, null=True, blank=True, help_text='在特定来源中的获取概率，百分比')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '奖品来源关系'
        verbose_name_plural = '奖品来源关系'
        ordering = ['-created_at']
        app_label = 'treasure'
        # 允许一个奖品在一个来源中有多条记录
        # unique_together = ('prize', 'source')

    def __str__(self):
        return f"{self.prize.name} - {self.source.name}"
