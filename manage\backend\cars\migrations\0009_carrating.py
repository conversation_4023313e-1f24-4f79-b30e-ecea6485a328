# Generated by Django 3.2.23 on 2025-02-02 12:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0008_auto_20250112_2111'),
    ]

    operations = [
        migrations.CreateModel(
            name='CarRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('car_id', models.CharField(default='', max_length=50, verbose_name='赛车编号')),
                ('openid', models.CharField(max_length=100, verbose_name='用户OpenID')),
                ('speed_rating', models.DecimalField(decimal_places=1, max_digits=3, verbose_name='速度评分')),
                ('handling_rating', models.DecimalField(decimal_places=1, max_digits=3, verbose_name='手感评分')),
                ('value_rating', models.DecimalField(decimal_places=1, max_digits=3, verbose_name='性价比评分')),
                ('combat_rating', models.DecimalField(decimal_places=1, default=5.0, max_digits=3, verbose_name='对抗评分')),
                ('overall_rating', models.DecimalField(decimal_places=1, max_digits=3, verbose_name='综合评分')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='评分时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '赛车评分',
                'verbose_name_plural': '赛车评分',
                'ordering': ['-created_at'],
                'unique_together': {('car_id', 'openid')},
            },
        ),
    ]
