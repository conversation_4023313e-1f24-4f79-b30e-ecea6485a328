import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from scipy.optimize import fsolve

# 设置Seaborn主题
sns.set_theme(style="whitegrid", font="SimHei")  # 设置字体为黑体

# 定义数据
speed_power = [
    (0, 4527), #第一个数字是速度，第二个数字是推进1档动力值
    (76.5, 4742), #第一个数字是速度，第二个数字是推进2档动力值
    (87.21, 5400), #第一个数字是速度，第二个数字是推进3档动力值
    (137.7, 5757), #第一个数字是速度，第二个数字是推进4档动力值
    (142.29, 6450), #第一个数字是速度，第二个数字是推进5档动力值
    (168.3, 6612), #第一个数字是速度，第二个数字是推进6档动力值
    (180.54, 6792), #第一个数字是速度，第二个数字是推进7档动力值
    (229.5, 7500), #第一个数字是速度，第二个数字是引擎1档动力值
    (244.8, 7500), #第一个数字是速度，第二个数字是引擎2档动力值
    (306, 7500), #第一个数字是速度，第二个数字是引擎3档动力值
    (382.5, 7200), #第一个数字是速度，第二个数字是引擎4档动力值
    (459, 7250), #第一个数字是速度，第二个数字是引擎5档动力值
    (535.5, 7300) #第一个数字是速度，第二个数字是引擎6档动力值
]

Big_jet_power = 6290 #燃料强度
Small_jet_power = 6290 #点火强度

# 提取速度和基础动力
speeds, base_powers = zip(*speed_power)

# 计算燃料动力和叠喷动力
fuel_powers = [power + (Big_jet_power / 1.2) for power in base_powers]
boost_powers = [power + ((Big_jet_power + Small_jet_power) / 1.2) for power in base_powers]

# 定义速度范围
x_range = np.linspace(0, 382, 1000)

# 定义速度动力平衡线
def balance_curve(x):
    return np.piecewise(x, [x < 45.9, x >= 45.9],
                        [lambda x: 10.9 * x + 0.14 * x * x,
                         lambda x: 500.81703297 - 0.00308486 * x + 0.14017491 * x * x])

# 计算速度动力平衡线的 y 值
y_balance = balance_curve(x_range)

# 创建绘图
plt.figure(figsize=(20, 12))

# 绘制速度动力平衡线
sns.lineplot(x=x_range, y=y_balance, label="速度动力平衡线", color="black")

# 绘制赛车基础动力线
sns.lineplot(x=speeds, y=base_powers, label="赛车基础动力线", color="blue", marker="o")

# 绘制赛车大喷动力线
sns.lineplot(x=speeds, y=fuel_powers, label="赛车大喷动力线", color="red", marker="o")

# 绘制赛车cww动力线
sns.lineplot(x=speeds, y=boost_powers, label="赛车cww动力线", color="purple", marker="o")

# 设置图表标题和标签
plt.title("QQ飞车A级赛车 收割者（推进40） 动力-速度曲线图 （百度QQ飞车吧出品）", fontsize=24)
plt.xlabel("速度(km/h)", fontsize=24)
plt.ylabel("动力(N)", fontsize=24)
plt.legend(fontsize=12)

# 计算交点
def find_intersection(x_values, y_values, balance_func):
    intersections = []
    for i in range(len(x_values) - 1):
        x1, x2 = x_values[i], x_values[i + 1]
        y1, y2 = y_values[i], y_values[i + 1]
        if (y1 - balance_func(x1)) * (y2 - balance_func(x2)) < 0:
            # 使用fsolve找到交点
            root = fsolve(lambda x: balance_func(x) - np.interp(x, [x1, x2], [y1, y2]), x1)
            intersections.append((root[0], balance_func(root[0])))
    return intersections

# 计算交点
base_intersections = find_intersection(speeds, base_powers, balance_curve)
fuel_intersections = find_intersection(speeds, fuel_powers, balance_curve)
boost_intersections = find_intersection(speeds, boost_powers, balance_curve)

# 可视化交点
for x, y in base_intersections:
    plt.scatter(x, y, color='blue', s=50, zorder=5)  # 减小标记大小
    plt.annotate(f"({x:.2f}, {y:.2f})", (x, y), textcoords="offset points", xytext=(0, -30), ha='center', fontsize=10, color='blue', arrowprops=dict(arrowstyle="->", color='blue'))

for x, y in fuel_intersections:
    plt.scatter(x, y, color='red', s=50, zorder=5)  # 减小标记大小
    plt.annotate(f"({x:.2f}, {y:.2f})", (x, y), textcoords="offset points", xytext=(0, -30), ha='center', fontsize=10, color='red', arrowprops=dict(arrowstyle="->", color='red'))

for x, y in boost_intersections:
    plt.scatter(x, y, color='purple', s=50, zorder=5)  # 减小标记大小
    plt.annotate(f"({x:.2f}, {y:.2f})", (x, y), textcoords="offset points", xytext=(0, -30), ha='center', fontsize=10, color='purple', arrowprops=dict(arrowstyle="->", color='purple'))

# 显示图表
plt.show()