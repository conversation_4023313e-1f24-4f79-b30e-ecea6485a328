from django.shortcuts import render
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.parsers import MultiPart<PERSON>arser, FormParser
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.db import transaction
import logging
import uuid
import pandas as pd
import os
from urllib.parse import quote
from django.conf import settings

# 自定义分页类，设置默认每页显示10条数据
class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

from .models import TreasureItem, PrizeSource, Prize, PrizeSourceRelation
from .serializers import TreasureItemSerializer, PrizeSourceSerializer, PrizeSerializer, PrizeSourceDetailSerializer, PrizeSourceRelationSerializer, PrizeQuerySerializer

# 配置日志
logger = logging.getLogger(__name__)

class TreasureItemViewSet(viewsets.ModelViewSet):
    """
    夺宝奖品视图集，提供增删改查功能
    """
    queryset = TreasureItem.objects.all()
    serializer_class = TreasureItemSerializer
    permission_classes = [AllowAny]  # 所有接口均允许任何人访问，无需认证

    def get_queryset(self):
        """
        获取奖品列表，支持搜索功能
        """
        queryset = TreasureItem.objects.all()

        # 获取查询参数
        search = self.request.query_params.get('search', '')
        quality = self.request.query_params.get('quality', '')
        item_type = self.request.query_params.get('item_type', '')
        active_only = self.request.query_params.get('active_only', 'false').lower() == 'true'

        # 应用过滤条件
        if search:
            queryset = queryset.filter(name__icontains=search)

        if quality:
            queryset = queryset.filter(quality=quality)

        if item_type:
            queryset = queryset.filter(item_type=item_type)

        if active_only:
            queryset = queryset.filter(is_active=True)

        return queryset

    def create(self, request, *args, **kwargs):
        """
        创建夺宝奖品
        """
        try:
            # 打印接收到的数据，便于调试
            logger.info(f"创建奖品请求数据: {request.data}")

            serializer = self.get_serializer(data=request.data)

            # 验证数据
            if not serializer.is_valid():
                logger.error(f"创建数据验证失败: {serializer.errors}")
                return Response({
                    'success': False,
                    'message': f'创建失败: {serializer.errors}',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)

            return Response({
                'success': True,
                'message': '奖品创建成功',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED, headers=headers)

        except Exception as e:
            logger.error(f"创建夺宝奖品失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'创建失败: {str(e)}',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        """
        更新夺宝奖品
        """
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()

            # 打印接收到的数据，便于调试
            logger.info(f"更新奖品请求数据: {request.data}")

            serializer = self.get_serializer(instance, data=request.data, partial=partial)

            # 验证数据
            if not serializer.is_valid():
                logger.error(f"更新数据验证失败: {serializer.errors}")
                return Response({
                    'success': False,
                    'message': f'更新失败: {serializer.errors}',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            self.perform_update(serializer)

            return Response({
                'success': True,
                'message': '奖品更新成功',
                'data': serializer.data
            })

        except Exception as e:
            logger.error(f"更新夺宝奖品失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'更新失败: {str(e)}',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        """
        删除夺宝奖品
        """
        try:
            instance = self.get_object()
            self.perform_destroy(instance)

            return Response({
                'success': True,
                'message': '奖品删除成功'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"删除夺宝奖品失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'删除失败: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request, *args, **kwargs):
        """
        获取夺宝奖品列表
        """
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                response = self.get_paginated_response(serializer.data)
                return Response({
                    'success': True,
                    'message': '获取奖品列表成功',
                    'data': response.data
                })

            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'success': True,
                'message': '获取奖品列表成功',
                'data': serializer.data
            })

        except Exception as e:
            logger.error(f"获取夺宝奖品列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def retrieve(self, request, *args, **kwargs):
        """
        获取夺宝奖品详情
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)

            return Response({
                'success': True,
                'message': '获取奖品详情成功',
                'data': serializer.data
            })

        except Exception as e:
            logger.error(f"获取夺宝奖品详情失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取详情失败: {str(e)}',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def qualities(self, request):
        """
        获取所有奖品品质选项（作为建议，不强制限制）
        """
        try:
            qualities = [
                {'value': choice[0], 'label': choice[1]}
                for choice in TreasureItem.QUALITY_CHOICES
            ]

            return Response({
                'success': True,
                'message': '获取品质列表成功',
                'data': qualities
            })

        except Exception as e:
            logger.error(f"获取奖品品质列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取品质列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def item_types(self, request):
        """
        获取所有奖品类型选项（作为建议，不强制限制）
        """
        try:
            types = [
                {'value': choice[0], 'label': choice[1]}
                for choice in TreasureItem.TYPE_CHOICES
            ]

            return Response({
                'success': True,
                'message': '获取类型列表成功',
                'data': types
            })

        except Exception as e:
            logger.error(f"获取奖品类型列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取类型列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def expiry_options(self, request):
        """
        获取可用的期限选项（作为建议，不强制限制）
        """
        try:
            options = [
                {'value': 0, 'label': '永久'},
                {'value': 30, 'label': '30天'},
                {'value': 15, 'label': '15天'},
                {'value': 7, 'label': '7天'},
            ]

            return Response({
                'success': True,
                'message': '获取期限选项成功',
                'data': options
            })

        except Exception as e:
            logger.error(f"获取期限选项失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取期限选项失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['patch'])
    def toggle_status(self, request, pk=None):
        """
        切换奖品启用状态
        """
        try:
            instance = self.get_object()
            instance.is_active = not instance.is_active
            instance.save()

            serializer = self.get_serializer(instance)
            return Response({
                'success': True,
                'message': f'奖品状态已切换为{"启用" if instance.is_active else "禁用"}',
                'data': serializer.data
            })

        except Exception as e:
            logger.error(f"切换奖品状态失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'切换状态失败: {str(e)}',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)


class PrizeSourceViewSet(viewsets.ModelViewSet):
    """
    奖品来源视图集，提供增删改查功能
    """
    queryset = PrizeSource.objects.all()
    serializer_class = PrizeSourceSerializer
    permission_classes = [AllowAny]
    parser_classes = [MultiPartParser, FormParser]
    pagination_class = StandardResultsSetPagination  # 应用自定义分页类

    def get_queryset(self):
        """
        获取奖品来源列表，支持搜索功能
        """
        queryset = PrizeSource.objects.all()

        # 获取查询参数
        search = self.request.query_params.get('search', '')
        source_type = self.request.query_params.get('source_type', '')
        active_only = self.request.query_params.get('active_only', 'false').lower() == 'true'

        # 应用过滤条件
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        if source_type:
            queryset = queryset.filter(source_type=source_type)

        if active_only:
            queryset = queryset.filter(is_active=True)

        return queryset

    def get_serializer_class(self):
        """根据操作选择合适的序列化器"""
        if self.action == 'retrieve' or self.action == 'prizes':
            return PrizeSourceDetailSerializer
        return PrizeSourceSerializer

    @action(detail=True, methods=['get'])
    def prizes(self, request, pk=None):
        """
        获取指定来源的所有奖品
        """
        try:
            source = self.get_object()
            serializer = PrizeSourceDetailSerializer(source)
            return Response({
                'success': True,
                'message': '获取奖品列表成功',
                'data': serializer.data
            })
        except Exception as e:
            logger.error(f"获取来源奖品列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取奖品列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def source_types(self, request):
        """
        获取所有来源类型选项
        """
        try:
            types = [
                {'value': choice[0], 'label': choice[1]}
                for choice in PrizeSource.SOURCE_TYPE_CHOICES
            ]

            return Response({
                'success': True,
                'message': '获取类型列表成功',
                'data': types
            })
        except Exception as e:
            logger.error(f"获取来源类型列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取类型列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def clear_data(self, request):
        """
        清空所有奖品和道具数据，包括所有关联关系
        """
        try:
            with transaction.atomic():
                # 首先删除所有关联关系
                relation_count = PrizeSourceRelation.objects.count()
                PrizeSourceRelation.objects.all().delete()

                # 然后删除所有奖品
                prize_count = Prize.objects.count()
                Prize.objects.all().delete()

                # 最后删除所有道具
                source_count = PrizeSource.objects.count()
                PrizeSource.objects.all().delete()

                return Response({
                    'success': True,
                    'message': '所有数据已成功清空',
                    'data': {
                        'relations_deleted': relation_count,
                        'prizes_deleted': prize_count,
                        'sources_deleted': source_count
                    }
                })
        except Exception as e:
            logger.error(f"清空数据失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'清空数据失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def import_excel(self, request):
        """
        从Excel导入奖品和道具数据

        Excel表格格式要求：
        - 表头: 序号, 奖品名称, 所属道具, 数量, 道具编号, 道具类型, 奖品编号, 奖品类型
        - 奖品图片URL会自动生成为: https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/prize/奖品名称.png
        - 道具图片URL会自动生成为: https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/property/道具名称.png
        """
        try:
            # 检查是否上传了文件
            if 'file' not in request.FILES:
                return Response({
                    'success': False,
                    'message': '未上传文件',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            excel_file = request.FILES['file']

            # 检查文件扩展名
            _, ext = os.path.splitext(excel_file.name)
            if ext.lower() not in ['.xlsx', '.xls']:
                return Response({
                    'success': False,
                    'message': '仅支持Excel文件(.xlsx, .xls)',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            # 读取Excel文件
            df = pd.read_excel(excel_file)

            # 检查必要的列是否存在
            required_columns = ['奖品名称', '所属道具']  # 只有这两列是必须的
            optional_columns = ['序号', '数量', '道具编号', '道具类型', '奖品编号', '奖品类型']  # 这些列是可选的
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return Response({
                    'success': False,
                    'message': f'Excel文件缺少必要的列: {", ".join(missing_columns)}',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            # 统计信息
            stats = {
                'sources_created': 0,
                'sources_updated': 0,
                'prizes_created': 0,
                'prizes_updated': 0,
                'errors': []
            }

            # 处理每一行数据
            for _, row in df.iterrows():
                try:
                    # 为每一行数据单独使用事务，这样一行的错误不会影响其他行
                    with transaction.atomic():
                        # 提取数据
                        source_name = str(row['所属道具']).strip()
                        source_code = str(row['道具编号']).strip() if not pd.isna(row['道具编号']) else None

                        # 处理道具类型，添加中文到代码值的映射
                        source_type_raw = str(row['道具类型']).strip() if not pd.isna(row['道具类型']) else ''
                        # 创建中文类型到代码值的映射
                        source_type_map = {
                            '道具': 'item',
                            '模式': 'mode',
                            # 添加更多可能的映射
                            '抽奖': 'item',  # 假设抽奖属于道具类
                            '礼包': 'item',  # 假设礼包属于道具类
                        }

                        # 尝试根据映射表获取类型代码，如果找不到则使用原始值
                        source_type = source_type_map.get(source_type_raw, source_type_raw.lower())
                        # 如果映射后仍然为空，则使用默认值
                        if not source_type:
                            source_type = 'item'

                        prize_name = str(row['奖品名称']).strip()
                        prize_code = str(row['奖品编号']).strip() if not pd.isna(row['奖品编号']) else None

                        # 将数量作为字符串处理，而不是整数
                        quantity = str(row['数量']).strip() if not pd.isna(row['数量']) else '1'

                        # 直接使用Excel中的奖品类型值
                        prize_type = str(row['奖品类型']).strip() if not pd.isna(row['奖品类型']) else '其他'

                        # 如果编号为空，则生成唯一编号
                        if not source_code:
                            source_code = f"SOURCE_{uuid.uuid4().hex[:8]}"

                        if not prize_code:
                            prize_code = f"PRIZE_{uuid.uuid4().hex[:8]}"

                        # 生成图片URL
                        source_image_url = f"https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/property/{quote(source_name)}.png"
                        prize_image_url = f"https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/prize/{quote(prize_name)}.png"

                        # 标准化来源类型值
                        source_type_dict = dict([(choice[0], choice[0]) for choice in PrizeSource.SOURCE_TYPE_CHOICES])
                        if source_type not in source_type_dict:
                            source_type = 'item'  # 默认为道具类型

                        # 查找或创建奖品来源
                        # 如果提供了道具编号，则按编号查找；否则按名称查找
                        if source_code:
                            source, source_created = PrizeSource.objects.get_or_create(
                                source_code=source_code,
                                defaults={
                                    'name': source_name,
                                    'source_type': source_type,
                                    'image_url': source_image_url,
                                    'is_active': True
                                }
                            )
                        else:
                            # 如果没有提供道具编号，则按名称查找
                            source, source_created = PrizeSource.objects.get_or_create(
                                name=source_name,
                                defaults={
                                    'source_code': f"SOURCE_{uuid.uuid4().hex[:8]}",
                                    'source_type': source_type,
                                    'image_url': source_image_url,
                                    'is_active': True
                                }
                            )

                        if source_created:
                            stats['sources_created'] += 1
                        else:
                            stats['sources_updated'] += 1

                        # 始终创建新的奖品记录，不进行去重
                        # 如果提供了奖品编号，则使用该编号；否则生成新的编号
                        if not prize_code:
                            prize_code = f"PRIZE_{uuid.uuid4().hex[:8]}"

                        # 创建新的奖品记录
                        prize = Prize.objects.create(
                            prize_code=prize_code,
                            name=prize_name,
                            quantity=quantity,
                            image_url=prize_image_url,
                            prize_type=prize_type,
                            probability=None,  # 概率在关联表中设置
                            is_active=True
                        )

                        # 标记为新创建
                        prize_created = True

                        # 创建奖品与来源的关联（不去重，允许一个奖品关联多个相同的道具）
                        relation = PrizeSourceRelation.objects.create(
                            prize=prize,
                            source=source,
                            probability=None  # 可以从Excel中读取特定来源的概率
                        )

                        if prize_created:
                            stats['prizes_created'] += 1
                        else:
                            stats['prizes_updated'] += 1

                except Exception as row_error:
                    # 记录处理单行数据时的错误，但继续处理其他行
                    error_msg = f"处理第{_ + 2}行数据时出错: {str(row_error)}"
                    stats['errors'].append(error_msg)
                    logger.error(error_msg)

            # 返回导入结果
            return Response({
                'success': True,
                'message': '导入成功',
                'data': {
                    'stats': stats
                }
            })

        except Exception as e:
            logger.error(f"导入Excel失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'导入失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PrizeViewSet(viewsets.ModelViewSet):
    """
    奖品视图集，提供增删改查功能
    """
    queryset = Prize.objects.all()
    permission_classes = [AllowAny]
    pagination_class = StandardResultsSetPagination  # 应用自定义分页类

    def get_serializer_class(self):
        """
        根据操作选择合适的序列化器
        - 对于列表查询，使用 PrizeQuerySerializer（去重版本，不包含数量字段）
        - 对于其他操作，使用 PrizeSerializer（完整版本）
        """
        if self.action == 'list':
            return PrizeQuerySerializer
        return PrizeSerializer

    def get_queryset(self):
        """
        获取奖品列表，支持搜索功能
        对奖品编号进行去重，只返回每个奖品编号的第一条记录
        """
        # 获取查询参数
        search = self.request.query_params.get('search', '')
        rarity = self.request.query_params.get('rarity', '')
        prize_type = self.request.query_params.get('prize_type', '')
        source_id = self.request.query_params.get('source_id', '')
        source_type = self.request.query_params.get('source_type', '')
        active_only = self.request.query_params.get('active_only', 'false').lower() == 'true'

        # 基础查询集
        queryset = Prize.objects.all()

        # 应用过滤条件
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        if rarity:
            queryset = queryset.filter(rarity=rarity)

        if prize_type:
            queryset = queryset.filter(prize_type=prize_type)

        if source_id:
            queryset = queryset.filter(sources__id=source_id)

        if source_type:
            queryset = queryset.filter(sources__source_type=source_type)

        if active_only:
            queryset = queryset.filter(is_active=True)

        # 如果是列表查询，对奖品编号进行去重
        if self.action == 'list':
            # 获取所有不为空的奖品编号
            prize_codes = queryset.exclude(prize_code__isnull=True).values_list('prize_code', flat=True).distinct()

            # 对于每个奖品编号，只保留第一条记录
            distinct_prizes = []
            for code in prize_codes:
                prize = queryset.filter(prize_code=code).first()
                if prize:
                    distinct_prizes.append(prize.id)

            # 添加没有奖品编号的记录
            no_code_prizes = queryset.filter(prize_code__isnull=True)
            for prize in no_code_prizes:
                distinct_prizes.append(prize.id)

            # 返回去重后的查询集
            return Prize.objects.filter(id__in=distinct_prizes)

        return queryset

    @action(detail=False, methods=['get'])
    def rarity_types(self, request):
        """
        获取所有稀有度类型选项
        """
        try:
            types = [
                {'value': choice[0], 'label': choice[1]}
                for choice in Prize.RARITY_CHOICES
            ]

            return Response({
                'success': True,
                'message': '获取稀有度类型列表成功',
                'data': types
            })
        except Exception as e:
            logger.error(f"获取稀有度类型列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取类型列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def prize_types(self, request):
        """
        获取数据库中实际存在的奖品类型选项（去重后）
        """
        try:
            # 从数据库中获取所有不同的奖品类型
            distinct_types = Prize.objects.values_list('prize_type', flat=True).distinct().order_by('prize_type')

            # 过滤掉空值并转换为列表
            types_list = [t for t in distinct_types if t]

            # 如果数据库中没有奖品类型，则返回一个默认的"其他"类型
            if not types_list:
                types_list = ['其他']

            # 转换为前端需要的格式
            result_types = [{'value': t, 'label': t} for t in types_list]

            return Response({
                'success': True,
                'message': '获取奖品类型列表成功',
                'data': result_types
            })
        except Exception as e:
            logger.error(f"获取奖品类型列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取类型列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def miniprogram(self, request):
        """
        小程序专用的奖品列表接口，返回适合卡片展示的数据
        使用去重后的奖品列表，不包含数量字段
        """
        try:
            # 获取查询参数
            search = request.query_params.get('search', '')
            rarity = request.query_params.get('rarity', '')
            prize_type = request.query_params.get('prize_type', '')
            source_id = request.query_params.get('source_id', '')
            source_type = request.query_params.get('source_type', '')

            # 使用现有的查询逻辑获取奖品列表（已经进行了去重）
            queryset = self.get_queryset()

            # 使用 PrizeQuerySerializer 序列化器（不包含数量字段）
            serializer_class = PrizeQuerySerializer

            # 分页
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = serializer_class(page, many=True)
                paginated_response = self.get_paginated_response(serializer.data)

                # 将奖品按照所属道具分组
                grouped_prizes = {}
                for prize in serializer.data:
                    # 获取奖品所属的所有道具
                    sources = prize.get('sources_info', [])

                    if not sources:
                        # 如果奖品没有关联任何道具，将其放入"未分类"组
                        source_key = "未分类"
                        if source_key not in grouped_prizes:
                            grouped_prizes[source_key] = {
                                "source_id": None,
                                "source_name": "未分类",
                                "source_type": None,
                                "source_image_url": None,
                                "prizes": []
                            }
                        grouped_prizes[source_key]["prizes"].append(prize)
                    else:
                        # 将奖品添加到每个关联的道具组中
                        for source in sources:
                            source_name = source.get('source_name', '未知')
                            source_key = source_name

                            if source_key not in grouped_prizes:
                                grouped_prizes[source_key] = {
                                    "source_id": source.get('source'),
                                    "source_name": source_name,
                                    "source_type": source.get('source_type'),
                                    "source_image_url": f"https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/property/{source_name}.png",
                                    "prizes": []
                                }

                            # 添加奖品到道具组
                            grouped_prizes[source_key]["prizes"].append(prize)

                # 转换为列表格式
                result = []
                for source_name, source_data in grouped_prizes.items():
                    result.append(source_data)

                # 按照道具名称排序
                result.sort(key=lambda x: x["source_name"])

                return Response({
                    'success': True,
                    'message': '获取小程序奖品列表成功',
                    'data': {
                        'count': paginated_response.data['count'],
                        'next': paginated_response.data['next'],
                        'previous': paginated_response.data['previous'],
                        'sources': result
                    }
                })

            # 如果没有分页，则直接返回所有结果
            serializer = serializer_class(queryset, many=True)

            # 将奖品按照所属道具分组
            grouped_prizes = {}
            for prize in serializer.data:
                # 获取奖品所属的所有道具
                sources = prize.get('sources_info', [])

                if not sources:
                    # 如果奖品没有关联任何道具，将其放入"未分类"组
                    source_key = "未分类"
                    if source_key not in grouped_prizes:
                        grouped_prizes[source_key] = {
                            "source_id": None,
                            "source_name": "未分类",
                            "source_type": None,
                            "source_image_url": None,
                            "prizes": []
                        }
                    grouped_prizes[source_key]["prizes"].append(prize)
                else:
                    # 将奖品添加到每个关联的道具组中
                    for source in sources:
                        source_name = source.get('source_name', '未知')
                        source_key = source_name

                        if source_key not in grouped_prizes:
                            grouped_prizes[source_key] = {
                                "source_id": source.get('source'),
                                "source_name": source_name,
                                "source_type": source.get('source_type'),
                                "source_image_url": f"https://qqspeedguide-1257038267.cos.ap-guangzhou.myqcloud.com/props/property/{source_name}.png",
                                "prizes": []
                            }

                        # 添加奖品到道具组
                        grouped_prizes[source_key]["prizes"].append(prize)

            # 转换为列表格式
            result = []
            for source_name, source_data in grouped_prizes.items():
                result.append(source_data)

            # 按照道具名称排序
            result.sort(key=lambda x: x["source_name"])

            return Response({
                'success': True,
                'message': '获取小程序奖品列表成功',
                'data': {
                    'count': len(serializer.data),
                    'sources': result
                }
            })

        except Exception as e:
            logger.error(f"获取小程序奖品列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取小程序奖品列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)