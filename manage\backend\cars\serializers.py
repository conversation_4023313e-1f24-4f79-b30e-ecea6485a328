from rest_framework import serializers
from .models import Car, PropulsionLevelTable, CalculationHistory

class CarSerializer(serializers.ModelSerializer):
    class Meta:
        model = Car
        fields = [
            'id', 'car_id', 'name', 'level',
            'normal_speed', 'nitro_speed', 'drift_factor',
            'friction_factor', 'weight', 'low_speed_steering',
            'high_speed_steering',
            # 引擎和悬挂相关字段
            'engine_gear_1', 'engine_gear_2', 'engine_gear_3',
            'engine_gear_4', 'engine_gear_5', 'engine_gear_6',
            # 原装推进档位字段（新增）
            'original_propulsion_1', 'original_propulsion_2', 'original_propulsion_3',
            'original_propulsion_4', 'original_propulsion_5', 'original_propulsion_6',
            'original_propulsion_7', 'suspension',
            # 新增加的提速和夹角字段
            'angle_normal_speed', 'angle_nitro_speed',
            'normal_180_acceleration', 'normal_speed_acceleration',
            'nitro_250_acceleration', 'nitro_290_acceleration',
            # 推进40相关字段
            'angle_normal_speed_advance40', 'angle_nitro_speed_advance40',
            'normal_180_acceleration_advance40', 'normal_speed_acceleration_advance40',
            'nitro_250_acceleration_advance40', 'nitro_290_acceleration_advance40',
            # 新增字段
            'fuel_duration', 'fuel_intensity', 'ignition_duration', 'ignition_intensity',
            'original_intake_coefficient', 'intake_coefficient', 'drift_steering',
            'drift_swing', 'drift_reverse', 'drift_correction', 'super_nitro_intensity',
            'super_nitro_duration', 'super_nitro_trigger_condition', 'super_nitro_250_acceleration',
            'super_nitro_290_acceleration', 'angle_super_nitro_speed',
            # 其他字段
            'gem_slots', 'image_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate(self, data):
        """
        验证提交的数据
        """
        # 对数值字段进行验证
        strict_numeric_fields = {
            'drift_factor': '漂移速率',
            'friction_factor': '摩擦系数',
            'low_speed_steering': '最大转向',
            'high_speed_steering': '最小转向'
        }

        # 允许文本+数字混合的字段
        mixed_content_fields = {
            # 原装推进档位字段
            'original_propulsion_1': '原装推进1档',
            'original_propulsion_2': '原装推进2档',
            'original_propulsion_3': '原装推进3档',
            'original_propulsion_4': '原装推进4档',
            'original_propulsion_5': '原装推进5档',
            'original_propulsion_6': '原装推进6档',
            'original_propulsion_7': '原装推进7档',
            # 引擎档位字段
            'engine_gear_1': '引擎1挡',
            'engine_gear_2': '引擎2挡',
            'engine_gear_3': '引擎3挡',
            'engine_gear_4': '引擎4挡',
            'engine_gear_5': '引擎5挡',
            'engine_gear_6': '引擎6挡',
            # 其他字段
            'angle_normal_speed': '夹角平跑极速',
            'angle_nitro_speed': '夹角氮气极速',
            'normal_180_acceleration': '平跑180提速',
            'normal_speed_acceleration': '平跑极速提速',
            'nitro_250_acceleration': '大喷250提速',
            'nitro_290_acceleration': '大喷290提速',
            'angle_normal_speed_advance40': '夹角平跑极速（推进40）',
            'angle_nitro_speed_advance40': '夹角氮气极速（推进40）',
            'normal_180_acceleration_advance40': '平跑180提速（推进40）',
            'normal_speed_acceleration_advance40': '平跑极速提速（推进40）',
            'nitro_250_acceleration_advance40': '大喷250提速（推进40）',
            'nitro_290_acceleration_advance40': '大喷290提速（推进40）',
            # 新增字段验证规则
            'fuel_duration': '燃料时长',
            'fuel_intensity': '燃料强度',
            'ignition_duration': '点火时长',
            'ignition_intensity': '点火强度',
            'original_intake_coefficient': '原装进气系数',
            'intake_coefficient': '进气系数',
            'drift_steering': '漂移转向',
            'drift_swing': '漂移摆动',
            'drift_reverse': '漂移反向',
            'drift_correction': '漂移回正',
            'super_nitro_intensity': '超级喷强度',
            'super_nitro_duration': '超级喷时长',
            'super_nitro_trigger_condition': '超级喷触发条件',
            'super_nitro_250_acceleration': '超级喷250提速',
            'super_nitro_290_acceleration': '超级喷290提速',
            'angle_super_nitro_speed': '夹角超级喷极速',
            'gem_slots': '宝石槽'
        }

        # 严格数字字段验证
        for field, field_name in strict_numeric_fields.items():
            if field in data:
                value = data[field]
                # 允许空值
                if value is None or value == '':
                    continue
                # 允许"缺"作为特殊值
                if value == '缺':
                    continue
                try:
                    float_value = float(value)
                    data[field] = float_value
                except (ValueError, TypeError):
                    raise serializers.ValidationError({
                        field: f'{field_name}必须是数字或"缺"'
                    })

        # 混合内容字段验证 - 允许任何文本输入
        for field, field_name in mixed_content_fields.items():
            if field in data:
                value = data[field]
                # 允许空值
                if value is None or value == '':
                    continue
                # 值必须是字符串类型
                if not isinstance(value, str) and not isinstance(value, (int, float)):
                    raise serializers.ValidationError({
                        field: f'{field_name}必须是文本或数字'
                    })
                # 确保数值类型转换为字符串
                if isinstance(value, (int, float)):
                    data[field] = str(value)

        return data

class PropulsionLevelTableSerializer(serializers.ModelSerializer):
    """推进档位计算表序列化器"""
    class Meta:
        model = PropulsionLevelTable
        fields = '__all__'

class CalculationHistorySerializer(serializers.ModelSerializer):
    """计算历史记录序列化器"""
    class Meta:
        model = CalculationHistory
        fields = '__all__'
        read_only_fields = ['created_at']
