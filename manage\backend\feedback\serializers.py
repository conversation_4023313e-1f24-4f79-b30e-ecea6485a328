from rest_framework import serializers
from .models import Feedback

class FeedbackCreateSerializer(serializers.ModelSerializer):
    """
    反馈创建序列化器
    """
    class Meta:
        model = Feedback
        fields = ['type', 'content', 'device_info', 'contact']

class FeedbackListSerializer(serializers.ModelSerializer):
    """
    反馈列表序列化器
    """
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Feedback
        fields = ['id', 'type', 'type_display', 'content', 'device_info', 
                 'contact', 'status', 'status_display', 'reply', 
                 'created_at', 'updated_at'] 