from django.db import models

# Create your models here.

class PropulsionLevelTable(models.Model):
    """推进档位计算表模型"""
    car_level = models.CharField('赛车等级', max_length=20, unique=True)  # C/M1, B/M2/L2/R, T1, A/M3/L3, T2, T2皮肤

    # 推进档位上限（null表示无上限）
    level_1_max = models.IntegerField('推进1上限', null=True, blank=True)
    level_2_max = models.IntegerField('推进2上限', null=True, blank=True)
    level_3_max = models.IntegerField('推进3上限', null=True, blank=True)
    level_4_max = models.IntegerField('推进4上限', null=True, blank=True)
    level_5_max = models.IntegerField('推进5上限', null=True, blank=True)
    level_6_max = models.IntegerField('推进6上限', null=True, blank=True)
    level_7_max = models.IntegerField('推进7上限', null=True, blank=True)

    # 推进档位0与40的差值
    level_1_diff = models.IntegerField('推进1差值')
    level_2_diff = models.IntegerField('推进2差值')
    level_3_diff = models.IntegerField('推进3差值')
    level_4_diff = models.IntegerField('推进4差值')
    level_5_diff = models.IntegerField('推进5差值')
    level_6_diff = models.IntegerField('推进6差值')
    level_7_diff = models.IntegerField('推进7差值')

    # 每次改装平均提升
    level_1_avg_increase = models.FloatField('推进1平均提升')
    level_2_avg_increase = models.FloatField('推进2平均提升')
    level_3_avg_increase = models.FloatField('推进3平均提升')
    level_4_avg_increase = models.FloatField('推进4平均提升')
    level_5_avg_increase = models.FloatField('推进5平均提升')
    level_6_avg_increase = models.FloatField('推进6平均提升')
    level_7_avg_increase = models.FloatField('推进7平均提升')

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '推进档位计算表'
        verbose_name_plural = '推进档位计算表'
        ordering = ['car_level']

    def __str__(self):
        return f"{self.car_level} - 推进计算表"

class CalculationHistory(models.Model):
    """计算历史记录模型"""
    user_id = models.CharField('用户ID', max_length=100)
    car_data = models.TextField('赛车数据')  # 存储JSON字符串
    calculation_result = models.TextField('计算结果')  # 存储JSON字符串
    chart_type = models.CharField('图表类型', max_length=20, choices=[
        ('power_speed', '动力-速度曲线'),
        ('speed_time', '速度-时间曲线')
    ], null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '计算历史记录'
        verbose_name_plural = '计算历史记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user_id} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def set_car_data(self, data):
        """设置赛车数据（自动转换为JSON字符串）"""
        import json
        self.car_data = json.dumps(data, ensure_ascii=False)

    def get_car_data(self):
        """获取赛车数据（自动从JSON字符串解析）"""
        import json
        try:
            return json.loads(self.car_data) if self.car_data else {}
        except json.JSONDecodeError:
            return {}

    def set_calculation_result(self, data):
        """设置计算结果（自动转换为JSON字符串）"""
        import json
        self.calculation_result = json.dumps(data, ensure_ascii=False)

    def get_calculation_result(self):
        """获取计算结果（自动从JSON字符串解析）"""
        import json
        try:
            return json.loads(self.calculation_result) if self.calculation_result else {}
        except json.JSONDecodeError:
            return {}

class Car(models.Model):
    car_id = models.CharField('赛车编号', max_length=50, unique=True)
    name = models.CharField('赛车名称', max_length=100)
    level = models.CharField('赛车级别', max_length=20)
    normal_speed = models.CharField('平跑极速', max_length=50)
    nitro_speed = models.CharField('氮气极速', max_length=50)
    drift_factor = models.CharField('漂移速率', max_length=50)
    friction_factor = models.CharField('摩擦系数', max_length=50)
    weight = models.CharField('车重', max_length=50)
    low_speed_steering = models.CharField('最大转向', max_length=50)
    high_speed_steering = models.CharField('最小转向', max_length=50)
    engine_gear_1 = models.CharField('引擎1挡', max_length=50, null=True, blank=True)
    engine_gear_2 = models.CharField('引擎2挡', max_length=50, null=True, blank=True)
    engine_gear_3 = models.CharField('引擎3挡', max_length=50, null=True, blank=True)
    engine_gear_4 = models.CharField('引擎4挡', max_length=50, null=True, blank=True)
    engine_gear_5 = models.CharField('引擎5挡', max_length=50, null=True, blank=True)
    engine_gear_6 = models.CharField('引擎6挡', max_length=50, null=True, blank=True)

    # 原装推进档位字段（新增）
    original_propulsion_1 = models.CharField('原装推进1档', max_length=50, null=True, blank=True)
    original_propulsion_2 = models.CharField('原装推进2档', max_length=50, null=True, blank=True)
    original_propulsion_3 = models.CharField('原装推进3档', max_length=50, null=True, blank=True)
    original_propulsion_4 = models.CharField('原装推进4档', max_length=50, null=True, blank=True)
    original_propulsion_5 = models.CharField('原装推进5档', max_length=50, null=True, blank=True)
    original_propulsion_6 = models.CharField('原装推进6档', max_length=50, null=True, blank=True)
    original_propulsion_7 = models.CharField('原装推进7档', max_length=50, null=True, blank=True)

    suspension = models.CharField('悬挂', max_length=50, null=True, blank=True)
    angle_normal_speed = models.CharField('夹角平跑极速', max_length=50, null=True, blank=True)
    angle_nitro_speed = models.CharField('夹角氮气极速', max_length=50, null=True, blank=True)
    normal_180_acceleration = models.CharField('平跑180提速', max_length=50, null=True, blank=True)
    normal_speed_acceleration = models.CharField('平跑极速提速', max_length=50, null=True, blank=True)
    nitro_250_acceleration = models.CharField('大喷250提速', max_length=50, null=True, blank=True)
    nitro_290_acceleration = models.CharField('大喷290提速', max_length=50, null=True, blank=True)
    angle_normal_speed_advance40 = models.CharField('夹角平跑极速（推进40）', max_length=50, null=True, blank=True)
    angle_nitro_speed_advance40 = models.CharField('夹角氮气极速（推进40）', max_length=50, null=True, blank=True)
    normal_180_acceleration_advance40 = models.CharField('平跑180提速（推进40）', max_length=50, null=True, blank=True)
    normal_speed_acceleration_advance40 = models.CharField('平跑极速提速（推进40）', max_length=50, null=True, blank=True)
    nitro_250_acceleration_advance40 = models.CharField('大喷250提速（推进40）', max_length=50, null=True, blank=True)
    nitro_290_acceleration_advance40 = models.CharField('大喷290提速（推进40）', max_length=50, null=True, blank=True)
    fuel_duration = models.CharField('燃料时长', max_length=50, null=True, blank=True)
    fuel_intensity = models.CharField('燃料强度', max_length=50, null=True, blank=True)
    ignition_duration = models.CharField('点火时长', max_length=50, null=True, blank=True)
    ignition_intensity = models.CharField('点火强度', max_length=50, null=True, blank=True)
    original_intake_coefficient = models.CharField('原装进气系数', max_length=50, null=True, blank=True)
    intake_coefficient = models.CharField('进气系数', max_length=50, null=True, blank=True)
    drift_steering = models.CharField('漂移转向', max_length=50, null=True, blank=True)
    drift_swing = models.CharField('漂移摆动', max_length=50, null=True, blank=True)
    drift_reverse = models.CharField('漂移反向', max_length=50, null=True, blank=True)
    drift_correction = models.CharField('漂移回正', max_length=50, null=True, blank=True)
    super_nitro_intensity = models.CharField('超级喷强度', max_length=50, null=True, blank=True)
    super_nitro_duration = models.CharField('超级喷时长', max_length=50, null=True, blank=True)
    super_nitro_trigger_condition = models.CharField('超级喷触发条件', max_length=50, null=True, blank=True)
    super_nitro_250_acceleration = models.CharField('超级喷250提速', max_length=50, null=True, blank=True)
    super_nitro_290_acceleration = models.CharField('超级喷290提速', max_length=50, null=True, blank=True)
    angle_super_nitro_speed = models.CharField('夹角超级喷极速', max_length=50, null=True, blank=True)
    gem_slots = models.CharField('宝石槽', max_length=50, null=True, blank=True)
    image_id = models.CharField('图片ID', max_length=100, null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '赛车'
        verbose_name_plural = '赛车'
        ordering = []

    def __str__(self):
        return f"{self.car_id} - {self.name}"

class CarComment(models.Model):
    """赛车评论模型"""
    car_id = models.CharField('赛车编号', max_length=50)
    openid = models.CharField('用户OpenID', max_length=100)
    content = models.TextField('评论内容')
    is_deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    deleted_at = models.DateTimeField('删除时间', null=True, blank=True)

    class Meta:
        verbose_name = '赛车评论'
        verbose_name_plural = '赛车评论'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['car_id']),
            models.Index(fields=['openid']),
            models.Index(fields=['is_deleted']),
        ]

    @property
    def car(self):
        """获取关联的赛车对象"""
        return Car.objects.filter(car_id=self.car_id).first()

    def soft_delete(self):
        """软删除评论"""
        from django.utils import timezone
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def __str__(self):
        car = self.car
        car_name = car.name if car else self.car_id
        return f"{car_name} - {self.openid[:8]}... - {self.content[:20]}"

class CommentReport(models.Model):
    """评论举报模型"""
    comment = models.ForeignKey(CarComment, on_delete=models.CASCADE, verbose_name='被举报评论')
    reporter_openid = models.CharField('举报人OpenID', max_length=100)
    reason = models.TextField('举报原因')
    status = models.CharField('处理状态', max_length=20, choices=[
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('resolved', '已处理'),
        ('rejected', '已驳回')
    ], default='pending')
    handler_note = models.TextField('处理备注', null=True, blank=True)
    created_at = models.DateTimeField('举报时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    handled_at = models.DateTimeField('处理时间', null=True, blank=True)

    class Meta:
        verbose_name = '评论举报'
        verbose_name_plural = '评论举报'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['comment']),
            models.Index(fields=['reporter_openid']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"举报 - {self.comment.content[:20]} - {self.status}"

class CarRating(models.Model):
    """赛车评分模型"""
    car_id = models.CharField('赛车编号', max_length=50, default='')
    openid = models.CharField('用户OpenID', max_length=100)
    speed_rating = models.DecimalField('速度评分', max_digits=3, decimal_places=1)
    handling_rating = models.DecimalField('手感评分', max_digits=3, decimal_places=1)
    value_rating = models.DecimalField('性价比评分', max_digits=3, decimal_places=1)
    combat_rating = models.DecimalField('对抗评分', max_digits=3, decimal_places=1, default=5.0)
    appearance_rating = models.DecimalField('颜值评分', max_digits=3, decimal_places=1, default=5.0)
    overall_rating = models.DecimalField('综合评分', max_digits=3, decimal_places=1)
    created_at = models.DateTimeField('评分时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '赛车评分'
        verbose_name_plural = '赛车评分'
        # 确保每个用户只能对同一辆车评分一次
        unique_together = ('car_id', 'openid')
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        # 计算综合评分：速度30%，手感25%，性价比20%，对抗25%
        self.overall_rating = round(
            self.speed_rating * 0.25 +
            self.handling_rating * 0.25 +
            self.value_rating * 0.20 +
            self.combat_rating * 0.15 +
            self.appearance_rating * 0.15,
            1
        )
        super().save(*args, **kwargs)

    @property
    def car(self):
        """获取关联的赛车对象，使用赛车编号查询"""
        return Car.objects.filter(car_id=self.car_id).first()

    def __str__(self):
        car = self.car
        car_name = car.name if car else self.car_id
        return f"{car_name} - {self.openid} - {self.overall_rating}"

class SensitiveWord(models.Model):
    """敏感词模型"""
    word = models.CharField('敏感词', max_length=100, unique=True)
    source = models.CharField('来源', max_length=20, choices=[
        ('manual', '手动添加'),
        ('tencent', '腾讯云检测')
    ], default='manual')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    is_active = models.BooleanField('是否启用', default=True)
    remark = models.TextField('备注', null=True, blank=True)

    class Meta:
        verbose_name = '敏感词'
        verbose_name_plural = '敏感词'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['word']),
            models.Index(fields=['source']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.word} ({self.source})"
