from django.test import TestCase
from rest_framework.test import APITestCase
from rest_framework import status
from django.urls import reverse
from .models import Car
import tempfile
import openpyxl

class CarAPITests(APITestCase):
    def setUp(self):
        """测试前创建一些初始数据"""
        self.car_data = {
            'car_id': 'A001',
            'name': '烈风',
            'level': 'A',
            'normal_speed': 120.0,
            'nitro_speed': 150.0,
            'drift_factor': 1.2,
            'friction_factor': 0.8,
            'weight': 1000.0,
            'low_speed_steering': 0.9,
            'high_speed_steering': 0.7
        }
        self.car = Car.objects.create(**self.car_data)

    def test_create_car(self):
        """测试创建赛车"""
        url = reverse('car-list')
        new_car_data = {
            'car_id': 'A002',
            'name': '疾风',
            'level': 'A',
            'normal_speed': 125.0,
            'nitro_speed': 155.0,
            'drift_factor': 1.3,
            'friction_factor': 0.7,
            'weight': 950.0,
            'low_speed_steering': 0.95,
            'high_speed_steering': 0.75
        }
        response = self.client.post(url, new_car_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Car.objects.count(), 2)
        self.assertEqual(Car.objects.get(car_id='A002').name, '疾风')

    def test_get_car_list(self):
        """测试获取赛车列表"""
        url = reverse('car-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_get_car_detail(self):
        """测试获取单个赛车详情"""
        url = reverse('car-detail', args=[self.car.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['car_id'], 'A001')

    def test_update_car(self):
        """测试更新赛车信息"""
        url = reverse('car-detail', args=[self.car.id])
        updated_data = self.car_data.copy()
        updated_data['name'] = '烈风改'
        response = self.client.put(url, updated_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(Car.objects.get(id=self.car.id).name, '烈风改')

    def test_delete_car(self):
        """测试删除赛车"""
        url = reverse('car-detail', args=[self.car.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Car.objects.count(), 0)

    def test_search_car(self):
        """测试搜索赛车"""
        url = reverse('car-list')
        response = self.client.get(url, {'search': '烈风'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], '烈风')

    def test_filter_by_level(self):
        """测试按级别筛选"""
        url = reverse('car-list')
        response = self.client.get(url, {'level': 'A'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['level'], 'A')

    def test_import_export_excel(self):
        """测试Excel导入导出功能"""
        # 创建测试用Excel文件
        wb = openpyxl.Workbook()
        ws = wb.active
        headers = ['赛车编号', '赛车名称', '赛车级别', '平跑极速', '氮气极速', 
                  '飘逸系数', '摩擦系数', '车重', '低速转向', '高速转向']
        ws.append(headers)
        car_data = ['A003', '闪电', 'S', 130, 160, 1.4, 0.6, 900, 1.0, 0.8]
        ws.append(car_data)

        # 测试导入
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            wb.save(tmp.name)
            tmp.seek(0)
            url = reverse('car-import-excel')
            with open(tmp.name, 'rb') as excel_file:
                response = self.client.post(url, {'file': excel_file}, format='multipart')
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertTrue(Car.objects.filter(car_id='A003').exists())

        # 测试导出
        url = reverse('car-export-excel')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response['Content-Type'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
