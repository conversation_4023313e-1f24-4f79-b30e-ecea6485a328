"""
推进计算与绘图功能的工具函数
"""
import re
import numpy as np
from typing import List, Dict, Tuple, Optional


def extract_numeric_value(value_str: str) -> float:
    """
    从字符串中提取数值

    Args:
        value_str: 包含数字的字符串，如 "6290强度"、"40"、"5000+"

    Returns:
        提取出的数值，如果无法提取则返回0
    """
    if not value_str:
        return 0

    # 使用正则表达式提取数字（包括小数）
    match = re.search(r'(\d+\.?\d*)', str(value_str))
    if match:
        return float(match.group(1))
    return 0


def parse_car_level(level: str) -> str:
    """
    解析赛车等级，处理T2皮肤等特殊情况

    Args:
        level: 原始赛车等级，如 "T2(雷诺传奇)"、"A/M3/L3"

    Returns:
        用于查找计算表的等级字符串
    """
    if level.startswith('T2('):
        return 'T2皮肤'
    return level


def calculate_propulsion_40_single(original_value: float, diff_value: int, max_value: Optional[int]) -> int:
    """
    计算单个推进档位的40级数值

    Args:
        original_value: 原装推进档位值
        diff_value: 0级与40级的差值
        max_value: 档位上限，None表示无上限

    Returns:
        推进40级的数值
    """
    calculated_40 = original_value + diff_value

    if max_value is None:
        # 无上限（带+号）
        return int(calculated_40)
    else:
        # 有上限，取最小值
        return int(min(calculated_40, max_value))


def interpolate_power_at_speed(speed: float, speed_anchors: List[float], power_values: List[float]) -> float:
    """
    在指定速度点插值获取动力值

    Args:
        speed: 目标速度
        speed_anchors: 速度锚点列表
        power_values: 对应的动力值列表

    Returns:
        插值得到的动力值
    """
    if speed <= speed_anchors[0]:
        return power_values[0]
    elif speed >= speed_anchors[-1]:
        return power_values[-1]
    else:
        return np.interp(speed, speed_anchors, power_values)


def calculate_balance_curve_power(speed: float) -> float:
    """
    计算速度动力平衡线上指定速度的动力值

    Args:
        speed: 速度值 (km/h)

    Returns:
        平衡线上的动力值 (N)
    """
    if speed < 45.9:
        return 10.9 * speed + 0.14 * speed * speed
    else:
        return 500.81703297 - 0.00308486 * speed + 0.14017491 * speed * speed


def generate_balance_curve_data(max_speed: float = 382, num_points: int = 100) -> Tuple[List[float], List[float]]:
    """
    生成速度动力平衡线数据

    Args:
        max_speed: 最大速度
        num_points: 数据点数量

    Returns:
        (速度列表, 动力列表)
    """
    speeds = np.linspace(0, max_speed, num_points)
    powers = []

    for speed in speeds:
        power = calculate_balance_curve_power(speed)
        powers.append(power)

    return speeds.tolist(), powers


def calculate_fuel_power(base_power: float, fuel_intensity: float) -> float:
    """
    计算大喷动力

    Args:
        base_power: 基础动力
        fuel_intensity: 燃料强度

    Returns:
        大喷动力值
    """
    return base_power + fuel_intensity / 1.2


def calculate_boost_power(base_power: float, fuel_intensity: float) -> float:
    """
    计算cww动力（大喷+小喷）

    Args:
        base_power: 基础动力
        fuel_intensity: 燃料强度

    Returns:
        cww动力值
    """
    big_jet_power = fuel_intensity / 1.2
    small_jet_power = fuel_intensity / 1.2  # 假设小喷也是燃料强度
    return base_power + big_jet_power + small_jet_power


def validate_car_data(car_data: Dict) -> Tuple[bool, str]:
    """
    验证赛车数据的完整性

    Args:
        car_data: 赛车数据字典

    Returns:
        (是否有效, 错误信息)
    """
    required_fields = ['car_id', 'name', 'level']

    for field in required_fields:
        if field not in car_data or not car_data[field]:
            return False, f"缺少必要字段: {field}"

    # 检查推进档位数据
    propulsion_fields = [
        'original_propulsion_1', 'original_propulsion_2', 'original_propulsion_3',
        'original_propulsion_4', 'original_propulsion_5', 'original_propulsion_6',
        'original_propulsion_7'
    ]

    valid_propulsion_count = 0
    for field in propulsion_fields:
        if field in car_data and car_data[field]:
            valid_propulsion_count += 1

    if valid_propulsion_count < 3:  # 至少需要3个推进档位
        return False, "推进档位数据不足，至少需要3个档位的数据"

    return True, ""


def format_chart_data_point(x: float, y: float) -> List[float]:
    """
    格式化图表数据点

    Args:
        x: X轴数值
        y: Y轴数值

    Returns:
        格式化后的数据点 [x, y]
    """
    return [round(float(x), 2), round(float(y), 2)]


def get_chart_colors() -> List[str]:
    """
    获取图表颜色配置

    Returns:
        颜色列表
    """
    return ['#4a90e2', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#e67e22', '#34495e']


def calculate_engine_factor(engine_level: float, base_level: float = 40) -> float:
    """
    计算引擎档位影响系数

    Args:
        engine_level: 当前引擎档位
        base_level: 基准引擎档位

    Returns:
        影响系数
    """
    return 1 + (engine_level - base_level) * 0.01


def get_speed_anchors() -> List[float]:
    """
    获取标准速度锚点

    Returns:
        速度锚点列表
    """
    return [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]


def simulate_acceleration(
    power_data: Dict,
    mass: float = 1000,
    drag_coefficient: float = 0.3,
    max_time: float = 16,
    time_steps: int = 100
) -> Tuple[List[float], List[float]]:
    """
    模拟赛车加速过程

    Args:
        power_data: 动力数据字典，包含speed_anchors和base_powers
        mass: 赛车质量 (kg)
        drag_coefficient: 空气阻力系数
        max_time: 最大模拟时间 (秒)
        time_steps: 时间步数

    Returns:
        (时间列表, 速度列表)
    """
    speed_anchors = power_data.get('speed_anchors', get_speed_anchors())
    base_powers = power_data.get('base_powers', [5000] * len(speed_anchors))

    time_data = np.linspace(0, max_time, time_steps)
    speed_data = []

    current_speed = 0
    dt = time_data[1] - time_data[0] if len(time_data) > 1 else 0.1

    for t in time_data:
        # 插值获取当前速度对应的动力
        power = interpolate_power_at_speed(current_speed, speed_anchors, base_powers)

        # 计算空气阻力
        air_resistance = drag_coefficient * current_speed * current_speed

        # 净推力
        net_force = max(0, power - air_resistance)

        # 加速度
        acceleration = net_force / mass

        # 更新速度
        current_speed += acceleration * dt
        current_speed = min(current_speed, 400)  # 限制最大速度

        speed_data.append(current_speed)

    return time_data.tolist(), speed_data


def create_echarts_series(
    name: str,
    data: List[List[float]],
    color: str,
    line_type: str = 'solid',
    symbol: str = 'circle'
) -> Dict:
    """
    创建ECharts系列数据

    Args:
        name: 系列名称
        data: 数据点列表 [[x1, y1], [x2, y2], ...]
        color: 线条颜色
        line_type: 线条类型 ('solid', 'dashed', 'dotted')
        symbol: 数据点符号 ('circle', 'none')

    Returns:
        ECharts系列配置字典
    """
    return {
        'name': name,
        'type': 'line',
        'data': data,
        'lineStyle': {'color': color, 'type': line_type},
        'symbol': symbol
    }
