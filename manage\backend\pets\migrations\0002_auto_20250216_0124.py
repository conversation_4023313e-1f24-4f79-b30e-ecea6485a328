# Generated by Django 3.2.23 on 2025-02-15 17:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pets', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pet',
            name='aptitude',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='资质'),
        ),
        migrations.AlterField(
            model_name='pet',
            name='basic_skill',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='基本技能'),
        ),
        migrations.AlterField(
            model_name='pet',
            name='combat_power',
            field=models.IntegerField(blank=True, null=True, verbose_name='战斗力'),
        ),
        migrations.AlterField(
            model_name='pet',
            name='enhanced_skill',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='强化技能'),
        ),
        migrations.AlterField(
            model_name='pet',
            name='form',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True, verbose_name='宠物形态'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='pet',
            name='main_attribute',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='主属性'),
        ),
        migrations.AlterField(
            model_name='pet',
            name='normal_skill',
            field=models.TextField(blank=True, null=True, verbose_name='普通技能'),
        ),
        migrations.AlterField(
            model_name='pet',
            name='rage_skill',
            field=models.TextField(blank=True, null=True, verbose_name='怒气技能'),
        ),
    ]
