from django.contrib import admin
from .models import PrizeSource, Prize

class PrizeInline(admin.TabularInline):
    """奖品内联管理"""
    model = Prize
    extra = 1
    fields = ['name', 'quantity', 'rarity', 'probability', 'is_active']

@admin.register(PrizeSource)
class PrizeSourceAdmin(admin.ModelAdmin):
    """奖品来源管理"""
    list_display = ['id', 'name', 'source_type', 'is_active', 'created_at']
    list_filter = ['source_type', 'is_active']
    search_fields = ['name', 'description']
    inlines = [PrizeInline]
    list_per_page = 20

@admin.register(Prize)
class PrizeAdmin(admin.ModelAdmin):
    """奖品管理"""
    list_display = ['id', 'name', 'source', 'rarity', 'quantity', 'probability', 'is_active', 'created_at']
    list_filter = ['rarity', 'source', 'is_active']
    search_fields = ['name', 'description']
    list_per_page = 20
