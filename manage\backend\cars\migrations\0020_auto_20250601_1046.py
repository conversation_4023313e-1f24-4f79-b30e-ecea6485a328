# Generated by Django 3.2.23 on 2025-06-01 02:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0019_rename_original_gear_7_to_original_propulsion_7'),
    ]

    operations = [
        migrations.CreateModel(
            name='PropulsionUpgradeData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('car_level', models.CharField(help_text='如: C/M1, B/M2/L2/R, T1, A/M3/L3, T2, T2皮肤', max_length=20, unique=True, verbose_name='赛车等级')),
                ('level_1_upgrade_limit', models.IntegerField(blank=True, help_text='null表示无上限', null=True, verbose_name='推进1改装上限')),
                ('level_2_upgrade_limit', models.IntegerField(blank=True, help_text='null表示无上限', null=True, verbose_name='推进2改装上限')),
                ('level_3_upgrade_limit', models.IntegerField(blank=True, help_text='null表示无上限', null=True, verbose_name='推进3改装上限')),
                ('level_4_upgrade_limit', models.IntegerField(blank=True, help_text='null表示无上限', null=True, verbose_name='推进4改装上限')),
                ('level_5_upgrade_limit', models.IntegerField(blank=True, help_text='null表示无上限', null=True, verbose_name='推进5改装上限')),
                ('level_6_upgrade_limit', models.IntegerField(blank=True, help_text='null表示无上限', null=True, verbose_name='推进6改装上限')),
                ('level_7_upgrade_limit', models.IntegerField(blank=True, help_text='null表示无上限', null=True, verbose_name='推进7改装上限')),
                ('level_1_power_increase', models.FloatField(help_text='每次改装增加的动力数值', verbose_name='推进1每次改装动力提升')),
                ('level_2_power_increase', models.FloatField(help_text='每次改装增加的动力数值', verbose_name='推进2每次改装动力提升')),
                ('level_3_power_increase', models.FloatField(help_text='每次改装增加的动力数值', verbose_name='推进3每次改装动力提升')),
                ('level_4_power_increase', models.FloatField(help_text='每次改装增加的动力数值', verbose_name='推进4每次改装动力提升')),
                ('level_5_power_increase', models.FloatField(help_text='每次改装增加的动力数值', verbose_name='推进5每次改装动力提升')),
                ('level_6_power_increase', models.FloatField(help_text='每次改装增加的动力数值', verbose_name='推进6每次改装动力提升')),
                ('level_7_power_increase', models.FloatField(help_text='每次改装增加的动力数值', verbose_name='推进7每次改装动力提升')),
                ('max_upgrade_times', models.IntegerField(default=40, help_text='通常为40次改装', verbose_name='最大改装次数')),
                ('description', models.TextField(blank=True, help_text='关于此等级推进改装的说明', verbose_name='备注说明')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '推进改装数据',
                'verbose_name_plural': '推进改装数据',
                'ordering': ['car_level'],
            },
        ),
        migrations.AlterField(
            model_name='car',
            name='original_propulsion_7',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进7档'),
        ),
    ]
