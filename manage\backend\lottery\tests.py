from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from .models import LotteryRecord, LotteryItem
from users.models import WechatUser
from .utils import RarityScoreCalculator

class LotteryTests(TestCase):
    """
    抽奖功能单元测试
    """
    def setUp(self):
        """
        测试前准备
        """
        # 创建测试用户
        self.user = WechatUser.objects.create(
            openid='test_openid',
            nickname='测试用户',
            avatar='http://example.com/avatar.jpg'
        )
        
        # 创建API客户端
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 测试抽奖数据
        self.test_lottery_data = {
            'activityType': 'treasure-hunting',
            'totalDraws': 100,
            'hasLegendaryItems': True,
            'hasTotalGoldItems': True,
            'statistics': [
                {
                    'item': {
                        'id': 1,
                        'name': '传说物品',
                        'background': 'legendary',
                        'probability': 0.001
                    },
                    'count': 1
                },
                {
                    'item': {
                        'id': 2,
                        'name': '金色物品',
                        'background': 'gold',
                        'probability': 0.01
                    },
                    'count': 2
                },
                {
                    'item': {
                        'id': 3,
                        'name': '紫色物品',
                        'background': 'purple',
                        'probability': 0.05
                    },
                    'count': 5
                }
            ]
        }
    
    def test_rarity_score_calculation(self):
        """
        测试稀有度计算
        """
        # 测试物品分数计算
        self.assertEqual(RarityScoreCalculator.calculate_item_score(0.01), 100000)
        self.assertEqual(RarityScoreCalculator.calculate_item_score(0.1), 10000)
        
        # 测试总分计算
        total_score = RarityScoreCalculator.calculate_total_score(self.test_lottery_data['statistics'])
        # 期望分数: (1000/0.001*1) + (1000/0.01*2) = 1000000 + 200000 = 1200000
        self.assertEqual(total_score, 1200000)
        
        # 测试稀有度得分计算
        rarity_score = RarityScoreCalculator.calculate_rarity_score(total_score, 100)
        # 期望得分: 1200000 * (1/100) = 12000
        self.assertEqual(rarity_score, 12000)
    
    def test_create_record(self):
        """
        测试创建抽奖记录
        """
        url = reverse('lottery:records')
        response = self.client.post(url, self.test_lottery_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 0)
        self.assertTrue('recordId' in response.data['data'])
        
        # 检查数据库记录
        record = LotteryRecord.objects.get(id=response.data['data']['recordId'])
        self.assertEqual(record.activity_type, 'treasure-hunting')
        self.assertEqual(record.draw_count, 100)
        self.assertTrue(record.has_legendary_items)
        self.assertTrue(record.has_gold_items)
        
        # 检查物品记录
        items = record.items.all()
        self.assertEqual(items.count(), 3)
    
    def test_get_rankings(self):
        """
        测试获取排行榜
        """
        # 创建记录
        self.client.post(reverse('lottery:records'), self.test_lottery_data, format='json')
        
        # 测试稀有度排行榜
        url = reverse('lottery:rankings') + '?activityType=treasure-hunting&type=rarity'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 0)
        self.assertEqual(len(response.data['data']['rankList']), 1)
        
        # 测试抽数排行榜
        url = reverse('lottery:rankings') + '?activityType=treasure-hunting&type=draws'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 0)
        self.assertEqual(len(response.data['data']['rankList']), 1)
    
    def test_get_record_detail(self):
        """
        测试获取记录详情
        """
        # 创建记录
        create_response = self.client.post(reverse('lottery:records'), self.test_lottery_data, format='json')
        record_id = create_response.data['data']['recordId']
        
        # 获取详情
        url = reverse('lottery:record-detail') + '?activityType=treasure-hunting'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 0)
        self.assertEqual(response.data['data']['id'], record_id)
        self.assertEqual(len(response.data['data']['statistics']), 3)
    
    def test_delete_record(self):
        """
        测试删除记录
        """
        # 创建记录
        self.client.post(reverse('lottery:records'), self.test_lottery_data, format='json')
        
        # 删除记录
        url = reverse('lottery:records') + '?activityType=treasure-hunting'
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 0)
        self.assertTrue(response.data['data']['deleted'])
        
        # 检查记录是否已删除
        self.assertEqual(LotteryRecord.objects.count(), 0)
        self.assertEqual(LotteryItem.objects.count(), 0)
    
    def test_invalid_activity_type(self):
        """
        测试无效的活动类型
        """
        invalid_data = self.test_lottery_data.copy()
        invalid_data['activityType'] = 'invalid-type'
        
        url = reverse('lottery:records')
        response = self.client.post(url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], 1004)  # 活动类型不存在错误码
