import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

# 设置 Matplotlib 支持中文
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 阻力函数
def resistance(v):
    if v < 45.9:
        return 10.9 * v + 0.14 * v ** 2
    else:
        return 500.81703297 - 0.00308486 * v + 0.14017491 * v ** 2

# 速度锚点（两辆车共用）
speed_anchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]

# 赛车名称
car1_name = '青龙偃月刀-关羽 带 +10花间少女'
car2_name = '至尊-麦凯伦'

# 平跑动力值
# power_values_car1 = [4400, 4500, 5100, 5600, 5950, 6500, 6800, 7750, 7750, 7750, 7750, 7750, 7800]  # 终众
# power_values_car2 = [4731, 4950, 5400, 5900, 6200, 6753, 6890, 7590, 7500, 7570, 7300, 7400, 7400]  # 大河神卫
# power_values_car1 = [4100, 4100, 4600, 5100, 5600, 6000, 6100, 7500, 7500, 7500, 7300, 7300, 7300]  # 雷诺（推进0）
# power_values_car2 = [4427, 4442, 5000, 5507, 6000, 6362, 6492, 7500, 7500, 7500, 7300, 7300, 7300]  # 雷诺（推进40）
# power_values_car1 = [4400, 4400, 4900, 5350, 5900, 6500, 6550, 7750, 7750, 7750, 7650, 7650, 7650]  # 爆天甲
# power_values_car2 = [4700, 4500, 5200, 5350, 5900, 6500, 6900, 7800, 7850, 7850, 7650, 7650, 7650]  # 爆天-曜影（十阶）
# power_values_car1 = [4600, 4700, 5300, 5800, 6100, 6600, 6900, 7800, 7850, 7830, 7750, 7750, 7800]  # 至尊-烛龙
# power_values_car1 = [4400, 4400, 4900, 5300, 5900, 6500, 6900, 7624, 7750, 7750, 7600, 7650, 7700]  # 超核玩家-雷蛇
# power_values_car2 = [4600, 4700, 5300, 5800, 6100, 6600, 6900, 7800, 7920, 7810, 7750, 7750, 7800]  # 至尊-真武
# power_values_car1 = [4400, 4500, 4900, 5500, 6000, 6600, 6900, 7750, 7750, 7750, 7750, 7750, 7800]  # 终极爆天甲
# power_values_car2 = [4731, 4750, 5300, 5700, 6200, 6753, 6890, 7730, 7500, 7700, 7300, 7400, 7400]  # 镇魔天王-魔礼青（推进40）
# power_values_car1 = [4200, 4400, 5000, 5350, 6050, 6250, 6400, 7500, 7500, 7500, 7200, 7250, 7300]  # 收割者（推进0）
# power_values_car2 = [4527, 4742, 5400, 5757, 6450, 6612, 6792, 7500, 7500, 7500, 7200, 7250, 7300]  # 收割者（推进40）
# power_values_car1 = [4585, 4800, 5000, 5507, 6000, 6362, 6492, 7500, 7500, 7500, 7300, 7300, 7300]  # 雷诺-弑神三阶（推进40）
# power_values_car2 = [4677, 4742, 5300, 5907, 6400, 6753, 6890, 7500, 7500, 7500, 7500, 7500, 7500]  # 魔王雷诺（推进40）
# power_values_car2 = [4585, 4800, 5000, 5849, 6000, 6617, 6739, 7500, 7500, 7500, 7300, 7300, 7300]  # 雷诺-幻夜五阶（推进40）
# power_values_car1 = [4400, 4500, 5100, 5600, 5950, 6500, 6800, 7750, 7810, 7750, 7750, 7750, 7800]  # 终极镇海之戟-螣蛇
# power_values_car2 = [4500, 4600, 4900, 5500, 6000, 6600, 6900, 7750, 7750, 7800, 7750, 7750, 7800]  # 终极爆天甲-刘备
power_values_car1 = [4400, 4600, 5000, 5300, 5900, 6500, 6900, 7770, 7895, 7750, 7600, 7600, 7700]  # 青龙偃月刀-关羽 带 +10引擎宠
# power_values_car2 = [4600, 4600, 5200, 5700, 6000, 6600, 6900, 7800, 7850, 7750, 7750, 7750, 7800]  # 爆天雷诺
# power_values_car2 = [4600, 4700, 5300, 5800, 6100, 6600, 6900, 7800, 7850, 7750, 7750, 7750, 7800]  # 至尊-火麒麟
power_values_car2 = [4600, 4700, 5250, 5800, 6100, 6600, 6950, 7800, 7850, 7750, 7750, 7750, 7800]  # 至尊-麦凯伦

# 大喷动力值
fuel_power_car1 = 6380 / 1.2
fuel_power_car2 = 6290 / 1.2

# 超级喷动力值
super_power_car1 = 7800 / 1.2
super_power_car2 = 6900 / 1.2

# 设置开关 True False
show_car1  = True    # 是否显示第一辆车的曲线
show_car2  = True   # 是否显示第二辆车的曲线
show_jet   = True   # 是否显示大喷曲线
show_super = False   # 是否显示超级喷曲线

# 动力函数（分段函数）
def power(v, power_values):
    return np.interp(v, speed_anchors, power_values)

# 微分方程：dv/dt = (power(v) - resistance(v)) / mass
def dv_dt(t, v, power_values, fuel_power=0):
    p = power(v, power_values) + fuel_power
    return (p - resistance(v)) / 54.15

# 时间范围
t_span = (0, 16)
t_eval = np.linspace(0, 16, 10000)  # 增加时间点的密度以提高精度

# 提升积分精度
atol = 1e-10  # 绝对误差容限
rtol = 1e-8  # 相对误差容限

# 解微分方程并绘制曲线
def solve_and_plot(power_values, fuel_power, super_power, linestyle="-", label_prefix="", color="blue", show_normal=True, show_jet=True, show_super=True):
    sol_normal = solve_ivp(lambda t, v: dv_dt(t, v, power_values), t_span, [0], t_eval=t_eval, atol=atol, rtol=rtol)
    sol_jet = solve_ivp(lambda t, v: dv_dt(t, v, power_values, fuel_power), t_span, [0], t_eval=t_eval, atol=atol, rtol=rtol)
    sol_super = solve_ivp(lambda t, v: dv_dt(t, v, power_values, super_power), t_span, [0], t_eval=t_eval, atol=atol, rtol=rtol)
    
    if show_normal:
        plt.plot(sol_normal.t, sol_normal.y[0], label=f'{label_prefix} 平跑速度线', linestyle=linestyle, color=color)
    if show_jet:
        plt.plot(sol_jet.t, sol_jet.y[0], label=f'{label_prefix} 大喷速度线', linestyle="--", color=color)
    if show_super:
        plt.plot(sol_super.t, sol_super.y[0], label=f'{label_prefix} 超级喷速度线', linestyle=":", color=color) 
    
    return sol_normal, sol_jet, sol_super 

def annotate_point(t, v, color, offset):
    if t is not None:
        plt.annotate(f'({t:.2f}, {v:.2f})', 
                     xy=(t, v), 
                     xytext=(t, v + offset),
                     arrowprops=dict(facecolor=color, shrink=0.05, alpha=0.5),  # 设置箭头透明度为 50%
                     color=color)

# 找到特定速度点的函数（使用插值提高精度）
def find_speed_point(sol, target_speed, time_limit=None):
    idx = np.where((sol.y[0] >= target_speed) & (sol.t <= time_limit))[0] if time_limit else np.where(sol.y[0] >= target_speed)[0]
    if len(idx) > 0:
        idx = idx[0]  # 第一个满足条件的点
        if idx > 0:  # 使用插值计算更精确的时间
            t1, t2 = sol.t[idx - 1], sol.t[idx]
            v1, v2 = sol.y[0][idx - 1], sol.y[0][idx]
            t_target = t1 + (target_speed - v1) * (t2 - t1) / (v2 - v1)
            return t_target, target_speed
        else:
            return sol.t[idx], sol.y[0][idx]
    else:
        return None, None

# 绘图
plt.figure(figsize=(20, 12))

# 第一辆车的曲线（蓝色）
if show_car1:
    sol_normal_1, sol_jet_1, sol_super_1 = solve_and_plot(power_values_car1, fuel_power_car1, super_power_car1, linestyle="-", label_prefix=car1_name, color="blue", show_normal=True, show_jet=show_jet, show_super=show_super)
    # 标记第一辆车的特殊点
    annotate_point(find_speed_point(sol_normal_1, 180)[0], 180, "blue", -15)
    annotate_point(find_speed_point(sol_normal_1, int(np.max(sol_normal_1.y[0])))[0], int(np.max(sol_normal_1.y[0])), "blue", -15)
    if show_jet:
        annotate_point(find_speed_point(sol_jet_1, 250)[0], 250, "blue", -45)
        annotate_point(find_speed_point(sol_jet_1, 290)[0], 290, "blue", -45)
        annotate_point(find_speed_point(sol_jet_1, int(np.max(sol_jet_1.y[0])), time_limit=8)[0], int(np.max(sol_jet_1.y[0])), "blue", -45)
    if show_super:
        annotate_point(find_speed_point(sol_super_1, 250)[0], 250, "blue", -30)
        annotate_point(find_speed_point(sol_super_1, 290)[0], 290, "blue", -30)
        annotate_point(find_speed_point(sol_super_1, int(np.max(sol_super_1.y[0])), time_limit=8)[0], int(np.max(sol_super_1.y[0])), "blue", -30)

# 第二辆车的曲线（红色）
if show_car2:
    sol_normal_2, sol_jet_2, sol_super_2 = solve_and_plot(power_values_car2, fuel_power_car2, super_power_car2, linestyle="-", label_prefix=car2_name, color="red", show_normal=True, show_jet=show_jet, show_super=show_super)
    # 标记第二辆车的特殊点
    annotate_point(find_speed_point(sol_normal_2, 180)[0], 180, "red", 15)
    annotate_point(find_speed_point(sol_normal_2, int(np.max(sol_normal_2.y[0])))[0], int(np.max(sol_normal_2.y[0])), "red", 15)
    if show_jet:
        annotate_point(find_speed_point(sol_jet_2, 250)[0], 250, "red", 45)
        annotate_point(find_speed_point(sol_jet_2, 290)[0], 290, "red", 45)
        annotate_point(find_speed_point(sol_jet_2, int(np.max(sol_jet_2.y[0])), time_limit=8)[0], int(np.max(sol_jet_2.y[0])), "red", 45)
    if show_super:
        annotate_point(find_speed_point(sol_super_2, 250)[0], 250, "red", 30)
        annotate_point(find_speed_point(sol_super_2, 290)[0], 290, "red", 30)
        annotate_point(find_speed_point(sol_super_2, int(np.max(sol_super_2.y[0])), time_limit=8)[0], int(np.max(sol_super_2.y[0])), "red", 30)

# 设置图例和标签
plt.xlabel('时间 (秒)', fontsize=24)
plt.ylabel('速度 (km/h)', fontsize=24)
plt.title('QQ飞车 平跑及大喷 速度-时间曲线图 （百度QQ飞车吧出品）', fontsize=24)
#plt.title('QQ飞车 平跑及超级喷 速度-时间曲线图 （百度QQ飞车吧出品）', fontsize=24)
plt.legend(fontsize=14)  # 设置图例字体大小
plt.grid(True)
plt.xlim(0, 16) # 设置 x 轴范围
plt.ylim(0, 350) # 设置 y 轴范围
plt.show()