#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量上传赛车图片脚本
将此脚本放在图片目录下运行即可
图片命名规则：
1. 使用赛车名称，如：风之子.jpg
2. 或使用赛车编号，如：N100001.png
"""

import os
import sys
import requests
import logging
from logging.handlers import RotatingFileHandler
from typing import List, Tu<PERSON>

def setup_logger():
    """配置日志记录器"""
    logger = logging.getLogger('upload_images')
    logger.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建并配置文件处理器
    file_handler = RotatingFileHandler('upload_images.log', maxBytes=1024*1024, backupCount=5)
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    # 创建并配置控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class CarImageUploader:
    def __init__(self):
        self.api_base_url = "http://114.132.99.197:8000"
        self.session = requests.Session()
        self.logger = setup_logger()
        
    def upload_images(self, image_files: List[Tuple[str, str]]) -> bool:
        """
        批量上传图片
        @param image_files: 图片文件列表，每个元素是 (文件路径, 赛车名称/编号) 的元组
        @return: 是否全部上传成功
        """
        try:
            # 分批处理，每批20个文件
            batch_size = 20
            success_count = 0
            total_count = len(image_files)
            
            # MIME类型映射
            mime_types = {
                '.png': 'image/png',
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg'
            }
            
            for i in range(0, total_count, batch_size):
                batch = image_files[i:i + batch_size]
                files = []
                names = []
                
                # 准备当前批次的文件
                for image_path, name_or_id in batch:
                    if not os.path.exists(image_path):
                        self.logger.error(f"图片文件不存在: {image_path}")
                        continue
                        
                    # 检查文件大小
                    file_size = os.path.getsize(image_path)
                    if file_size > 2 * 1024 * 1024:  # 2MB
                        self.logger.error(f"图片文件过大: {image_path}")
                        continue
                    
                    # 获取文件扩展名并确定MIME类型
                    ext = os.path.splitext(image_path)[1].lower()
                    if ext not in mime_types:
                        self.logger.error(f"不支持的图片格式: {image_path}")
                        continue
                        
                    mime_type = mime_types[ext]
                    
                    # 打开文件并设置MIME类型
                    file_obj = open(image_path, 'rb')
                    files.append(('images[]', (os.path.basename(image_path), file_obj, mime_type)))
                    names.append(('names[]', name_or_id))
                
                if not files:
                    continue
                
                try:
                    # 发送请求
                    self.logger.info(f"正在上传第 {i//batch_size + 1} 批图片 ({len(files)} 个文件)...")
                    response = self.session.post(
                        f"{self.api_base_url}/api/cars/upload_images_by_name/",
                        files=files,
                        data=names
                    )
                    response.raise_for_status()
                    
                    # 处理响应
                    result = response.json()
                    if result.get('success'):
                        # 输出每个图片的处理结果
                        for item in result['results']:
                            if item['success']:
                                success_count += 1
                                self.logger.info(f"成功上传图片: {item['name']} -> {item['image_id']}")
                            else:
                                self.logger.error(f"上传失败 {item['name']}: {item['message']}")
                    else:
                        self.logger.error(f"批次上传失败: {result.get('error')}")
                        
                except Exception as batch_error:
                    self.logger.error(f"处理批次时出错: {str(batch_error)}")
                    self.logger.error(f"错误详情: {response.text if 'response' in locals() else 'No response'}")
                
                finally:
                    # 关闭当前批次的所有文件
                    for _, file_tuple in files:
                        try:
                            file_tuple[1].close()
                        except:
                            pass
            
            # 输出最终结果
            self.logger.info(f"上传完成: 成功 {success_count}/{total_count}")
            return success_count > 0
                
        except Exception as e:
            self.logger.error(f"批量上传图片失败: {str(e)}")
            return False

def main():
    try:
        # 创建上传器实例
        uploader = CarImageUploader()
        
        # 处理当前目录下的图片
        current_dir = os.getcwd()
        uploader.logger.info(f"开始处理目录: {current_dir}")
        
        # 获取所有图片文件
        image_files = []
        for filename in os.listdir(current_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                # 获取不带扩展名的文件名作为车名或编号，并去除前后空格
                name_or_id = os.path.splitext(filename)[0].strip()
                image_path = os.path.join(current_dir, filename)
                image_files.append((image_path, name_or_id))
                uploader.logger.info(f"找到图片: {filename}, 名称/编号: {name_or_id}")
        
        if not image_files:
            uploader.logger.warning("当前目录下没有找到图片文件")
            return True
            
        uploader.logger.info(f"找到 {len(image_files)} 个图片文件")
        
        # 批量上传图片
        if uploader.upload_images(image_files):
            uploader.logger.info("所有图片处理完成")
            return True
        else:
            uploader.logger.error("图片处理过程中出现错误")
            return False
            
    except Exception as e:
        uploader.logger.error(f"程序执行出错: {str(e)}")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1) 