#!/usr/bin/env python
"""
测试简化后的赛车等级处理逻辑
"""
import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from django.test import Client
from cars.models import PropulsionLevelTable


def check_available_levels():
    """检查数据库中可用的赛车等级"""
    print("=== 数据库中可用的赛车等级 ===")
    
    levels = PropulsionLevelTable.objects.values_list('car_level', flat=True)
    for level in levels:
        print(f"- {level}")
    
    return list(levels)


def test_supported_levels(available_levels):
    """测试支持的赛车等级"""
    print("\n=== 测试支持的赛车等级 ===")
    
    client = Client()
    url = "/api/cars/generate-curves/"
    
    for level in available_levels:
        print(f"\n测试等级: {level}")
        
        test_data = {
            "curve_type": "power_speed",
            "cars": [
                {
                    "name": f"测试赛车-{level}",
                    "level": level,
                    "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                    "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                    "fuel_intensity": 6290,
                    "ignition_intensity": 6290,
                    "propulsion_upgrades": 40
                }
            ]
        }
        
        try:
            response = client.post(url, data=json.dumps(test_data), content_type='application/json')
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"  ✅ 成功处理等级 {level}")
                else:
                    print(f"  ❌ 处理失败: {data['message']}")
            else:
                print(f"  ❌ HTTP错误 {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 异常: {str(e)}")


def test_unsupported_levels():
    """测试不支持的赛车等级"""
    print("\n=== 测试不支持的赛车等级 ===")
    
    client = Client()
    url = "/api/cars/generate-curves/"
    
    # 测试一些不存在的等级
    unsupported_levels = ["S级", "SSS级", "T2(雷诺传奇)", "未知等级", "X级"]
    
    for level in unsupported_levels:
        print(f"\n测试不支持的等级: {level}")
        
        test_data = {
            "curve_type": "power_speed",
            "cars": [
                {
                    "name": f"测试赛车-{level}",
                    "level": level,
                    "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                    "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                    "fuel_intensity": 6290,
                    "ignition_intensity": 6290,
                    "propulsion_upgrades": 40
                }
            ]
        }
        
        try:
            response = client.post(url, data=json.dumps(test_data), content_type='application/json')
            
            if response.status_code == 400:
                data = response.json()
                print(f"  ✅ 正确返回错误: {data['message']}")
            else:
                print(f"  ❌ 意外的状态码: {response.status_code}")
                if response.status_code == 200:
                    print("  ❌ 应该返回错误但却成功了")
                
        except Exception as e:
            print(f"  ❌ 异常: {str(e)}")


def test_mixed_levels():
    """测试混合等级（一个支持，一个不支持）"""
    print("\n=== 测试混合等级 ===")
    
    client = Client()
    url = "/api/cars/generate-curves/"
    
    test_data = {
        "curve_type": "power_speed",
        "cars": [
            {
                "name": "支持的赛车",
                "level": "T2",  # 支持的等级
                "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 40
            },
            {
                "name": "不支持的赛车",
                "level": "Z级",  # 不支持的等级
                "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 40
            }
        ]
    }
    
    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        
        if response.status_code == 400:
            data = response.json()
            print(f"✅ 正确返回错误: {data['message']}")
            if "第2辆赛车" in data['message'] and "Z级" in data['message']:
                print("✅ 正确定位到第2辆赛车的问题")
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")


def main():
    """主函数"""
    print("开始测试简化后的赛车等级处理逻辑")
    print("=" * 60)
    
    # 检查可用等级
    available_levels = check_available_levels()
    
    # 测试支持的等级
    test_supported_levels(available_levels)
    
    # 测试不支持的等级
    test_unsupported_levels()
    
    # 测试混合等级
    test_mixed_levels()
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    main()
