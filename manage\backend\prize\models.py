from django.db import models

class PrizeSource(models.Model):
    """
    奖品来源模型（道具或模式）

    用于记录奖品的来源信息，可以是游戏道具或游戏模式
    """

    SOURCE_TYPE_CHOICES = [
        ('item', '道具'),
        ('mode', '模式'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField('名称', max_length=100)
    source_type = models.CharField('类型', max_length=20, choices=SOURCE_TYPE_CHOICES, default='item')
    image_url = models.URLField('图片URL', max_length=500, null=True, blank=True)
    description = models.TextField('描述', null=True, blank=True)
    is_active = models.BooleanField('是否上架', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        app_label = 'prize'
        verbose_name = '奖品来源'
        verbose_name_plural = '奖品来源'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_source_type_display()})"


class Prize(models.Model):
    """
    奖品模型

    记录游戏中可获得的奖品信息
    """

    RARITY_CHOICES = [
        ('common', '普通'),
        ('rare', '稀有'),
        ('epic', '史诗'),
        ('legendary', '传说'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField('奖品名称', max_length=100)
    quantity = models.IntegerField('奖品数量', default=1)
    image_url = models.URLField('图片URL', max_length=500, null=True, blank=True)
    rarity = models.CharField('稀有度', max_length=20, choices=RARITY_CHOICES, default='common')
    probability = models.DecimalField('获取概率', max_digits=10, decimal_places=6, null=True, blank=True, help_text='百分比，例如5.25表示5.25%')
    description = models.TextField('奖品描述', null=True, blank=True)
    source = models.ForeignKey(PrizeSource, on_delete=models.CASCADE, related_name='prizes', verbose_name='所属来源')
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        app_label = 'prize'
        verbose_name = '奖品'
        verbose_name_plural = '奖品'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.get_rarity_display()} (来源: {self.source.name})"
