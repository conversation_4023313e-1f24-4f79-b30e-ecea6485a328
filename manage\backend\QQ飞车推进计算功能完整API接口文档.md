# QQ飞车推进计算功能完整API接口文档

## 概述

本文档描述了QQ飞车图鉴小程序中推进计算功能的完整API接口，包括推进计算、图表生成、推进计算表管理和计算历史记录管理等功能。

## 基础信息

- **基础URL**: `/api/`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: 暂时允许匿名访问 (AllowAny)

## 接口分类

### 1. 推进计算与绘图功能

#### 1.1 赛车搜索接口

**接口地址**: `GET /api/cars/search/`

**功能描述**: 根据关键词搜索赛车，返回包含原装推进和引擎档位数据的赛车列表。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| q | string | 是 | 搜索关键词（赛车名称或编号） |

**请求示例**:
```
GET /api/cars/search/?q=收割者
```

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "car_id": "A001",
            "name": "收割者",
            "level": "A/M3/L3",
            "engine_levels": [40, 40, 40, 40, 40, 40],
            "propulsion_levels": [4709, 4842, 5400, 6009, 6500, 6753, 6890],
            "fuel_intensity": 6290
        }
    ]
}
```

#### 1.2 推进40计算接口

**接口地址**: `POST /api/cars/calculate-propulsion/`

**功能描述**: 根据赛车ID计算满改装推进40档位数据和动力数据。

**请求参数**:
```json
{
    "car_id": "A001",
    "user_id": "user123"  // 可选，用于记录计算历史
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "car_info": {
            "car_id": "A001",
            "name": "收割者",
            "level": "A/M3/L3"
        },
        "original_data": {
            "engine_levels": [40, 40, 40, 40, 40, 40],
            "propulsion_levels": [4709, 4842, 5400, 6009, 6500, 6753, 6890],
            "fuel_intensity": 6290
        },
        "propulsion_40_levels": [5013, 5146, 5767, 6379, 6866, 7087, 7245],
        "power_data": {
            "speed_anchors": [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5],
            "base_powers": [1200, 1350, 1500, 1650, 1800, 1950, 2100, 2250, 2400, 2550, 2700, 2850, 3000],
            "fuel_powers": [1400, 1550, 1700, 1850, 2000, 2150, 2300, 2450, 2600, 2750, 2900, 3050, 3200],
            "boost_powers": [1600, 1750, 1900, 2050, 2200, 2350, 2500, 2650, 2800, 2950, 3100, 3250, 3400]
        }
    }
}
```

#### 1.3 图表生成接口

**接口地址**: `POST /api/cars/generate-chart/`

**功能描述**: 生成动力-速度曲线图或速度-时间曲线图的配置数据。

**请求参数**:
```json
{
    "chart_type": "power_speed",  // power_speed 或 speed_time
    "cars": [
        {
            "name": "收割者",
            "power_data": {
                "speed_anchors": [...],
                "base_powers": [...],
                "fuel_powers": [...],
                "boost_powers": [...]
            }
        }
    ]
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "chart_config": {
            "title": {
                "text": "动力-速度曲线图"
            },
            "xAxis": {
                "type": "value",
                "name": "速度 (km/h)"
            },
            "yAxis": {
                "type": "value",
                "name": "动力"
            },
            "series": [
                {
                    "name": "收割者-基础动力",
                    "type": "line",
                    "data": [[0, 1200], [76.5, 1350], ...]
                }
            ]
        }
    }
}
```

### 2. 推进计算表管理接口

#### 2.1 获取推进计算表列表

**接口地址**: `GET /api/propulsion-levels/`

**功能描述**: 获取所有推进计算表数据，支持筛选。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| car_level | string | 否 | 按赛车等级筛选 |

**请求示例**:
```
GET /api/propulsion-levels/
GET /api/propulsion-levels/?car_level=A/M3/L3
```

**响应示例**:
```json
{
    "count": 6,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": 1,
            "car_level": "C/M1",
            "level_1_max": null,
            "level_2_max": 4381,
            "level_3_max": 4959,
            "level_4_max": 5462,
            "level_5_max": null,
            "level_6_max": null,
            "level_7_max": 6244,
            "level_1_diff": 304,
            "level_2_diff": 304,
            "level_3_diff": 367,
            "level_4_diff": 370,
            "level_5_diff": 366,
            "level_6_diff": 334,
            "level_7_diff": 355,
            "level_1_avg_increase": 7.6,
            "level_2_avg_increase": 7.6,
            "level_3_avg_increase": 9.175,
            "level_4_avg_increase": 9.25,
            "level_5_avg_increase": 9.15,
            "level_6_avg_increase": 8.35,
            "level_7_avg_increase": 8.875,
            "created_at": "2024-05-27T16:32:00Z",
            "updated_at": "2024-05-27T16:32:00Z"
        }
    ]
}
```

#### 2.2 创建推进计算表

**接口地址**: `POST /api/propulsion-levels/`

**功能描述**: 创建新的推进计算表数据。

**请求参数**:
```json
{
    "car_level": "新等级",
    "level_1_max": null,
    "level_2_max": 4381,
    "level_3_max": 4959,
    "level_4_max": 5462,
    "level_5_max": null,
    "level_6_max": null,
    "level_7_max": 6244,
    "level_1_diff": 304,
    "level_2_diff": 304,
    "level_3_diff": 367,
    "level_4_diff": 370,
    "level_5_diff": 366,
    "level_6_diff": 334,
    "level_7_diff": 355,
    "level_1_avg_increase": 7.6,
    "level_2_avg_increase": 7.6,
    "level_3_avg_increase": 9.175,
    "level_4_avg_increase": 9.25,
    "level_5_avg_increase": 9.15,
    "level_6_avg_increase": 8.35,
    "level_7_avg_increase": 8.875
}
```

#### 2.3 获取单个推进计算表

**接口地址**: `GET /api/propulsion-levels/{id}/`

**功能描述**: 获取指定ID的推进计算表数据。

#### 2.4 更新推进计算表

**接口地址**: `PUT /api/propulsion-levels/{id}/` 或 `PATCH /api/propulsion-levels/{id}/`

**功能描述**: 更新指定ID的推进计算表数据。PUT为完整更新，PATCH为部分更新。

#### 2.5 删除推进计算表

**接口地址**: `DELETE /api/propulsion-levels/{id}/`

**功能描述**: 删除指定ID的推进计算表数据。

#### 2.6 获取赛车等级列表

**接口地址**: `GET /api/propulsion-levels/levels/`

**功能描述**: 获取所有赛车等级列表。

**响应示例**:
```json
{
    "success": true,
    "levels": ["C/M1", "B/M2/L2/R", "T1", "A/M3/L3", "T2", "T2皮肤"]
}
```

#### 2.7 批量导入推进计算表数据

**接口地址**: `POST /api/propulsion-levels/import_data/`

**功能描述**: 批量导入或更新推进计算表数据。

**请求参数**:
```json
{
    "data": [
        {
            "car_level": "C/M1",
            "level_1_max": null,
            "level_2_max": 4381,
            // ... 其他字段
        },
        {
            "car_level": "B/M2/L2/R",
            // ... 字段数据
        }
    ]
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "导入完成：创建 2 条，更新 0 条",
    "created_count": 2,
    "updated_count": 0,
    "errors": []
}
```

#### 2.8 导出Excel

**接口地址**: `GET /api/propulsion-levels/export_excel/`

**功能描述**: 导出推进计算表数据为Excel文件。

**响应**: 直接返回Excel文件下载。

#### 2.9 导入Excel

**接口地址**: `POST /api/propulsion-levels/import_excel/`

**功能描述**: 从Excel文件导入推进计算表数据。

**请求参数**: 
- `file`: Excel文件 (multipart/form-data)

**响应示例**:
```json
{
    "success": true,
    "message": "导入完成：创建 2 条，更新 1 条",
    "created_count": 2,
    "updated_count": 1,
    "errors": []
}
```

### 3. 计算历史记录管理接口

#### 3.1 获取计算历史记录列表

**接口地址**: `GET /api/calculation-history/`

**功能描述**: 获取计算历史记录，支持按用户ID筛选。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | string | 否 | 按用户ID筛选 |

**请求示例**:
```
GET /api/calculation-history/
GET /api/calculation-history/?user_id=user123
```

#### 3.2 创建计算历史记录

**接口地址**: `POST /api/calculation-history/`

**功能描述**: 创建新的计算历史记录。

#### 3.3 获取单个计算历史记录

**接口地址**: `GET /api/calculation-history/{id}/`

**功能描述**: 获取指定ID的计算历史记录。

#### 3.4 更新计算历史记录

**接口地址**: `PUT /api/calculation-history/{id}/` 或 `PATCH /api/calculation-history/{id}/`

**功能描述**: 更新指定ID的计算历史记录。

#### 3.5 删除计算历史记录

**接口地址**: `DELETE /api/calculation-history/{id}/`

**功能描述**: 删除指定ID的计算历史记录。

#### 3.6 清空历史记录

**接口地址**: `DELETE /api/calculation-history/clear/`

**功能描述**: 清空历史记录，支持按用户ID清空。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | string | 否 | 只清空指定用户的记录 |

**响应示例**:
```json
{
    "success": true,
    "message": "已清空用户 user123 的 5 条历史记录",
    "deleted_count": 5
}
```

## 错误处理

所有接口都遵循统一的错误响应格式：

```json
{
    "success": false,
    "message": "错误描述信息"
}
```

常见HTTP状态码：
- 200: 请求成功
- 400: 请求参数错误
- 404: 资源不存在
- 500: 服务器内部错误

## 数据模型说明

### PropulsionLevelTable (推进计算表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 主键ID |
| car_level | String | 赛车等级 (C/M1, B/M2/L2/R, T1, A/M3/L3, T2, T2皮肤) |
| level_1_max ~ level_7_max | Integer/Null | 推进1-7档上限，null表示无上限 |
| level_1_diff ~ level_7_diff | Integer | 推进1-7档差值 |
| level_1_avg_increase ~ level_7_avg_increase | Float | 推进1-7档平均提升 |
| created_at | DateTime | 创建时间 |
| updated_at | DateTime | 更新时间 |

### CalculationHistory (计算历史记录)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 主键ID |
| user_id | String | 用户ID |
| car_data | JSON | 赛车数据 |
| calculation_result | JSON | 计算结果 |
| created_at | DateTime | 创建时间 |

## 使用示例

### 完整的推进计算流程

1. **搜索赛车**:
```javascript
const searchResult = await fetch('/api/cars/search/?q=收割者');
const cars = await searchResult.json();
```

2. **计算推进40**:
```javascript
const calculateResult = await fetch('/api/cars/calculate-propulsion/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        car_id: cars.data[0].car_id,
        user_id: 'user123'
    })
});
const calculation = await calculateResult.json();
```

3. **生成图表**:
```javascript
const chartResult = await fetch('/api/cars/generate-chart/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        chart_type: 'power_speed',
        cars: [{
            name: calculation.data.car_info.name,
            power_data: calculation.data.power_data
        }]
    })
});
const chartConfig = await chartResult.json();
```

### 推进计算表管理示例

1. **获取所有推进计算表**:
```javascript
const response = await fetch('/api/propulsion-levels/');
const data = await response.json();
```

2. **创建新的推进计算表**:
```javascript
const newTable = {
    car_level: "新等级",
    level_1_max: null,
    level_2_max: 4500,
    level_1_diff: 300,
    level_2_diff: 300,
    level_1_avg_increase: 7.5,
    level_2_avg_increase: 7.5
    // ... 其他字段
};

const response = await fetch('/api/propulsion-levels/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(newTable)
});
```

3. **更新推进计算表**:
```javascript
const updateData = {
    level_1_diff: 350,
    level_1_avg_increase: 8.0
};

const response = await fetch('/api/propulsion-levels/1/', {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updateData)
});
```

4. **导出Excel**:
```javascript
// 直接下载文件
window.open('/api/propulsion-levels/export_excel/', '_blank');
```

5. **导入Excel**:
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);

const response = await fetch('/api/propulsion-levels/import_excel/', {
    method: 'POST',
    body: formData
});
const result = await response.json();
```

### 计算历史记录管理示例

1. **获取用户的计算历史**:
```javascript
const response = await fetch('/api/calculation-history/?user_id=user123');
const history = await response.json();
```

2. **清空用户历史记录**:
```javascript
const response = await fetch('/api/calculation-history/clear/?user_id=user123', {
    method: 'DELETE'
});
const result = await response.json();
```

## 注意事项

1. **数据格式**: 所有接口都使用JSON格式进行数据交换
2. **文件上传**: Excel导入接口使用multipart/form-data格式
3. **空值处理**: 推进上限字段支持null值，表示无上限
4. **错误处理**: 建议在前端对所有API调用进行错误处理
5. **性能考虑**: 大量数据操作时建议使用批量接口

## 开发环境测试

可以使用以下工具测试API接口：

1. **Postman**: 导入API文档进行测试
2. **curl命令**: 命令行测试
3. **浏览器开发者工具**: 前端集成测试

### curl测试示例

```bash
# 搜索赛车
curl -X GET "http://localhost:8000/api/cars/search/?q=收割者"

# 计算推进40
curl -X POST "http://localhost:8000/api/cars/calculate-propulsion/" \
  -H "Content-Type: application/json" \
  -d '{"car_id": "A001", "user_id": "test_user"}'

# 获取推进计算表列表
curl -X GET "http://localhost:8000/api/propulsion-levels/"

# 创建推进计算表
curl -X POST "http://localhost:8000/api/propulsion-levels/" \
  -H "Content-Type: application/json" \
  -d '{"car_level": "测试等级", "level_1_diff": 300}'
```

## 总结

本API文档涵盖了QQ飞车推进计算功能的所有接口，包括：

✅ **推进计算与绘图**: 3个核心接口，支持搜索、计算、绘图
✅ **推进计算表管理**: 完整的CRUD接口，支持Excel导入导出
✅ **计算历史记录管理**: 完整的CRUD接口，支持用户筛选和批量清理
✅ **详细的使用示例**: 涵盖所有主要功能的代码示例
✅ **完善的错误处理**: 统一的错误响应格式
✅ **开发测试指南**: 提供多种测试方法和示例

该API系统已经具备了生产环境部署的条件，可以直接集成到QQ飞车图鉴小程序中使用。
