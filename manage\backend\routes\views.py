from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
import bcrypt
import jwt
from datetime import datetime, timedelta
from django.conf import settings
from django.db import connection
from django.utils import timezone
from middleware.auth import authMiddleware

@csrf_exempt
def register(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        username = data.get('username')
        password = data.get('password')
        email = data.get('email')

        # 密码加密
        salt = bcrypt.gensalt()
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt)

        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO users (
                        username, 
                        password, 
                        email, 
                        created_at,
                        status,
                        last_login,
                        is_admin,
                        has_car_permission
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    [
                        username, 
                        hashed_password.decode('utf-8'), 
                        email,
                        timezone.now(),  # created_at
                        1,              # status 默认值1
                        timezone.now(),  # last_login
                        False,          # is_admin 默认False
                        False           # has_car_permission 默认False
                    ]
                )
                
                # 查询刚插入的用户信息
                cursor.execute(
                    """
                    SELECT id, username, email FROM users 
                    WHERE username = %s
                    """,
                    [username]
                )
                user = cursor.fetchone()

            return JsonResponse({
                'success': True,
                'data': {
                    'id': user[0],
                    'username': user[1],
                    'email': user[2]
                }
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': '注册失败',
                'error': str(e)
            }, status=500)

@csrf_exempt
def login(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        username = data.get('username')
        password = data.get('password')

        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM users WHERE username = %s",
                    [username]
                )
                user = cursor.fetchone()

            if not user:
                return JsonResponse({
                    'success': False,
                    'message': '用户名或密码错误'
                }, status=401)

            # 验证密码
            if not bcrypt.checkpw(password.encode('utf-8'), user[2].encode('utf-8')):
                return JsonResponse({
                    'success': False,
                    'message': '用户名或密码错误'
                }, status=401)

            # 更新最后登录时间
            with connection.cursor() as cursor:
                cursor.execute(
                    "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = %s",
                    [user[0]]
                )

            # 生成JWT token
            token = jwt.encode(
                {
                    'id': user[0],
                    'username': user[1],
                    'is_admin': user[8],
                    'exp': datetime.utcnow() + timedelta(days=1)
                },
                settings.SECRET_KEY,
                algorithm='HS256'
            )

            return JsonResponse({
                'success': True,
                'token': token,
                'user': {
                    'id': user[0],
                    'username': user[1],
                    'email': user[3],
                    'is_admin': user[8],
                    'has_car_permission': user[7]
                }
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': '登录失败',
                'error': str(e)
            }, status=500) 

# 添加测试路由
@authMiddleware
def test_protected(request):
    """
    @description 测试受保护的路由
    """
    return JsonResponse({
        'success': True,
        'message': '认证成功',
        'user': {
            'id': request.user['id'],
            'username': request.user['username']
        }
    }) 

# 添加用户列表接口
@authMiddleware
def user_list(request):
    """
    @description 获取用户列表
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, username, email, created_at, last_login, status, 
                       is_admin, has_car_permission 
                FROM users
                ORDER BY created_at DESC
                """
            )
            columns = [col[0] for col in cursor.description]
            users = [
                dict(zip(columns, row))
                for row in cursor.fetchall()
            ]

        return JsonResponse({
            'success': True,
            'data': users
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': '获取用户列表失败',
            'error': str(e)
        }, status=500)

# 添加更新用户权限接口
@csrf_exempt
@authMiddleware
def update_user_permission(request, user_id):
    """
    @description 更新用户权限
    """
    if request.method != 'PUT':
        return JsonResponse({
            'success': False,
            'message': '不支持的请求方法'
        }, status=405)

    # 检查当前用户是否是管理员
    if not request.user.get('is_admin'):
        return JsonResponse({
            'success': False,
            'message': '没有权限执行此操作'
        }, status=403)

    try:
        data = json.loads(request.body)
        has_car_permission = data.get('has_car_permission')

        with connection.cursor() as cursor:
            # 先更新权限
            cursor.execute(
                """
                UPDATE users 
                SET has_car_permission = %s
                WHERE id = %s
                """,
                [has_car_permission, user_id]
            )
            
            # 再查询更新后的用户信息
            cursor.execute(
                """
                SELECT id, username, has_car_permission 
                FROM users 
                WHERE id = %s
                """,
                [user_id]
            )
            user = cursor.fetchone()

            if not user:
                return JsonResponse({
                    'success': False,
                    'message': '用户不存在'
                }, status=404)

        return JsonResponse({
            'success': True,
            'data': {
                'id': user[0],
                'username': user[1],
                'has_car_permission': user[2]
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': '更新权限失败',
            'error': str(e)
        }, status=500) 

@csrf_exempt
@authMiddleware
def update_admin_status(request, user_id):
    """
    @description 更新用户管理员权限
    """
    if request.method != 'PUT':
        return JsonResponse({
            'success': False,
            'message': '不支持的请求方法'
        }, status=405)

    # 检查当前用户是否是管理员
    if not request.user.get('is_admin'):
        return JsonResponse({
            'success': False,
            'message': '没有权限执行此操作'
        }, status=403)

    try:
        data = json.loads(request.body)
        is_admin = data.get('is_admin')

        with connection.cursor() as cursor:
            # 先更新权限
            cursor.execute(
                """
                UPDATE users 
                SET is_admin = %s
                WHERE id = %s
                """,
                [is_admin, user_id]
            )
            
            # 再查询更新后的用户信息
            cursor.execute(
                """
                SELECT id, username, is_admin 
                FROM users 
                WHERE id = %s
                """,
                [user_id]
            )
            user = cursor.fetchone()

            if not user:
                return JsonResponse({
                    'success': False,
                    'message': '用户不存在'
                }, status=404)

        return JsonResponse({
            'success': True,
            'data': {
                'id': user[0],
                'username': user[1],
                'is_admin': user[2]
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': '更新管理员权限失败',
            'error': str(e)
        }, status=500) 

@csrf_exempt
@authMiddleware
def update_password(request, user_id):
    """
    @description 修改/重置密码
    """
    if request.method != 'PUT':
        return JsonResponse({
            'success': False,
            'message': '不支持的请求方法'
        }, status=405)

    try:
        data = json.loads(request.body)
        new_password = data.get('new_password')
        old_password = data.get('old_password')  # 非管理员修改密码时需要
        
        # 检查权限：只能修改自己的密码，除非是管理员
        is_admin = request.user.get('is_admin')
        is_self = str(request.user.get('id')) == str(user_id)
        
        if not (is_admin or is_self):
            return JsonResponse({
                'success': False,
                'message': '没有权限执行此操作'
            }, status=403)

        with connection.cursor() as cursor:
            # 如果是普通用户修改自己的密码，需要验证旧密码
            if not is_admin and is_self:
                if not old_password:
                    return JsonResponse({
                        'success': False,
                        'message': '需要提供原密码'
                    }, status=400)
                
                # 验证旧密码
                cursor.execute(
                    "SELECT password FROM users WHERE id = %s",
                    [user_id]
                )
                current_password = cursor.fetchone()[0]
                if not bcrypt.checkpw(old_password.encode('utf-8'), current_password.encode('utf-8')):
                    return JsonResponse({
                        'success': False,
                        'message': '原密码错误'
                    }, status=401)

            # 加密新密码
            salt = bcrypt.gensalt()
            hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), salt)

            # 更新密码
            cursor.execute(
                """
                UPDATE users 
                SET password = %s
                WHERE id = %s
                """,
                [hashed_password.decode('utf-8'), user_id]
            )
            
            # 查询用户信息
            cursor.execute(
                """
                SELECT id, username 
                FROM users 
                WHERE id = %s
                """,
                [user_id]
            )
            user = cursor.fetchone()

            if not user:
                return JsonResponse({
                    'success': False,
                    'message': '用户不存在'
                }, status=404)

        return JsonResponse({
            'success': True,
            'message': '密码修改成功',
            'data': {
                'id': user[0],
                'username': user[1]
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': '修改密码失败',
            'error': str(e)
        }, status=500) 