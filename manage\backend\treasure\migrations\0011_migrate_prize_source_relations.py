from django.db import migrations

def migrate_prize_source_relations(apps, schema_editor):
    """
    将现有的奖品与道具关系迁移到新的多对多关系中
    
    在修改模型关系之前，奖品与道具是一对多关系，通过Prize.source字段关联
    修改后，奖品与道具是多对多关系，通过PrizeSourceRelation中间表关联
    
    这个迁移脚本会查找所有奖品，并为每个奖品创建一个与其原来source字段对应的PrizeSourceRelation记录
    """
    # 获取模型类
    Prize = apps.get_model('treasure', 'Prize')
    PrizeSource = apps.get_model('treasure', 'PrizeSource')
    PrizeSourceRelation = apps.get_model('treasure', 'PrizeSourceRelation')
    
    # 获取所有奖品
    prizes = Prize.objects.all()
    
    # 创建关联记录
    for prize in prizes:
        # 检查奖品是否有source_id字段（旧关系）
        source_id = getattr(prize, 'source_id', None)
        if source_id:
            try:
                # 获取对应的道具
                source = PrizeSource.objects.get(id=source_id)
                
                # 创建关联记录
                PrizeSourceRelation.objects.create(
                    prize=prize,
                    source=source,
                    probability=prize.probability
                )
                
                print(f"为奖品 {prize.name} (ID: {prize.id}) 创建了与道具 {source.name} (ID: {source.id}) 的关联")
            except PrizeSource.DoesNotExist:
                print(f"找不到ID为 {source_id} 的道具，无法为奖品 {prize.name} (ID: {prize.id}) 创建关联")
            except Exception as e:
                print(f"为奖品 {prize.name} (ID: {prize.id}) 创建关联时出错: {str(e)}")


class Migration(migrations.Migration):

    dependencies = [
        ('treasure', '0010_auto_20250507_1150'),
    ]

    operations = [
        migrations.RunPython(migrate_prize_source_relations),
    ]
