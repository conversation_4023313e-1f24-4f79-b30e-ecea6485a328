import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

# 设置 Matplotlib 支持中文
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 阻力函数
def resistance(v):
    if v < 45.9:
        return 10.9 * v + 0.14 * v ** 2
    else:
        return 500.81703297 - 0.00308486 * v + 0.14017491 * v ** 2

# 速度锚点（两辆车共用）
speed_anchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]

# 动力值和燃料动力值（两辆车分别设置）
power_values_car1 = [4600, 4700, 5300, 5800, 6100, 6600, 6900, 7800, 7920, 7810, 7750, 7750, 7800]
power_values_car2 = [4600, 4600, 4800, 5300, 5900, 6500, 6500, 7810, 7815, 7895, 7600, 7600, 7650]

fuel_power_car1 = 6290 / 1.2
fuel_power_car2 = 6340 / 1.2

# 动力函数（分段函数）
def power(v, power_values):
    return np.interp(v, speed_anchors, power_values)

# 微分方程：dv/dt = (power(v) - resistance(v)) / mass
def dv_dt(t, v, power_values, fuel_power=0):
    p = power(v, power_values) + fuel_power
    return (p - resistance(v)) / 54.15

# 时间范围
t_span = (0, 20)
t_eval = np.linspace(0, 20, 10000)  # 增加时间点的密度以提高精度

# 提升积分精度
atol = 1e-10  # 绝对误差容限
rtol = 1e-8  # 相对误差容限

# 解微分方程并绘制曲线
def solve_and_plot(power_values, fuel_power, linestyle="-", label_prefix="", color="blue"):
    sol_normal = solve_ivp(lambda t, v: dv_dt(t, v, power_values), t_span, [0], t_eval=t_eval, atol=atol, rtol=rtol)
    sol_jet = solve_ivp(lambda t, v: dv_dt(t, v, power_values, fuel_power), t_span, [0], t_eval=t_eval, atol=atol, rtol=rtol)
    
    plt.plot(sol_normal.t, sol_normal.y[0], label=f'{label_prefix}平跑速度线', linestyle=linestyle, color=color)
    plt.plot(sol_jet.t, sol_jet.y[0], label=f'{label_prefix}燃料速度线', linestyle="--", color=color)
    
    return sol_normal, sol_jet

# 标记点
def annotate_point(t, v, color, offset):
    if t is not None:
        plt.annotate(f'({t:.2f}, {v:.2f})', 
                     xy=(t, v), 
                     xytext=(t, v + offset),
                     arrowprops=dict(facecolor=color, shrink=0.05),
                     color=color)

# 动态判断曲线的上下关系并标记特殊点
def mark_special_points(sol_normal_1, sol_normal_2, sol_jet_1, sol_jet_2, color1, color2):
    # 比较平跑曲线
    for sol, color, offset in [(sol_normal_1, color1, 30), (sol_normal_2, color2, -30)]:
        speed_180_t, speed_180_v = find_speed_point(sol, 180)
        speed_floor_t, speed_floor_v = find_speed_point(sol, int(np.max(sol.y[0])))
        annotate_point(speed_180_t, speed_180_v, color, offset)
        annotate_point(speed_floor_t, speed_floor_v, color, offset)
    
    # 比较燃料曲线
    for sol, color, offset in [(sol_jet_1, color1, 30), (sol_jet_2, color2, -30)]:
        speed_290_t, speed_290_v = find_speed_point(sol, 290)
        jet_floor_t, jet_floor_v = find_speed_point(sol, int(np.max(sol.y[0])), time_limit=8)
        annotate_point(speed_290_t, speed_290_v, color, offset)
        annotate_point(jet_floor_t, jet_floor_v, color, offset)

# 找到特定速度点的函数（使用插值提高精度）
def find_speed_point(sol, target_speed, time_limit=None):
    idx = np.where((sol.y[0] >= target_speed) & (sol.t <= time_limit))[0] if time_limit else np.where(sol.y[0] >= target_speed)[0]
    if len(idx) > 0:
        idx = idx[0]  # 第一个满足条件的点
        if idx > 0:  # 使用插值计算更精确的时间
            t1, t2 = sol.t[idx - 1], sol.t[idx]
            v1, v2 = sol.y[0][idx - 1], sol.y[0][idx]
            t_target = t1 + (target_speed - v1) * (t2 - t1) / (v2 - v1)
            return t_target, target_speed
        else:
            return sol.t[idx], sol.y[0][idx]
    else:
        return None, None

# 绘图
plt.figure(figsize=(10, 6))

# 第一辆车的曲线（蓝色）
sol_normal_1, sol_jet_1 = solve_and_plot(power_values_car1, fuel_power_car1, linestyle="-", label_prefix="车1 ", color="blue")

# 第二辆车的曲线（红色）
sol_normal_2, sol_jet_2 = solve_and_plot(power_values_car2, fuel_power_car2, linestyle="-", label_prefix="车2 ", color="red")

# 动态判断曲线的上下关系并标记特殊点
mark_special_points(sol_normal_1, sol_normal_2, sol_jet_1, sol_jet_2, color1="blue", color2="red")

# 设置图例和标签
plt.xlabel('时间 (秒)')
plt.ylabel('速度 (km/h)')
plt.title('两辆赛车的速度与时间曲线图')
plt.legend()
plt.grid(True)
# 设置 y 轴范围
plt.ylim(0, 350)
plt.show()