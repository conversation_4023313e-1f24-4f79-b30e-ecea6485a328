from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAdminUser
from rest_framework.pagination import PageNumberPagination
from .models import Feedback
from .serializers import FeedbackCreateSerializer, FeedbackListSerializer

class CustomPagination(PageNumberPagination):
    """
    自定义分页类
    支持通过page_size参数自定义每页数量
    """
    page_size = 24
    page_size_query_param = 'page_size'
    max_page_size = 100

class FeedbackViewSet(viewsets.ModelViewSet):
    """
    反馈管理视图集
    
    接口列表:
    - 提交反馈 (POST /api/feedback/)
    - 获取反馈列表 (GET /api/feedback/)
    - 更新反馈状态 (PUT /api/feedback/{id}/)
    - 回复反馈 (POST /api/feedback/{id}/reply/)
    - 删除反馈 (DELETE /api/feedback/{id}/)
    """
    queryset = Feedback.objects.all()
    permission_classes = [AllowAny]  # 允许所有用户访问
    pagination_class = CustomPagination
    
    def get_serializer_class(self):
        if self.action == 'create':
            return FeedbackCreateSerializer
        return FeedbackListSerializer
    
    @action(detail=True, methods=['post'])
    def reply(self, request, pk=None):
        """管理员回复反馈"""
        feedback = self.get_object()
        reply = request.data.get('reply')
        if not reply:
            return Response({'error': '回复内容不能为空'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        feedback.reply = reply
        feedback.status = 'completed'
        feedback.save()
        
        serializer = self.get_serializer(feedback)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        删除反馈
        
        删除成功返回204状态码
        """
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response({
            'message': '反馈已成功删除'
        }, status=status.HTTP_204_NO_CONTENT) 