from django.db import models
from users.models import WechatUser

class LotteryRecord(models.Model):
    """
    抽奖记录模型，存储用户的抽奖记录
    """
    ACTIVITY_TYPES = (
        ('treasure-hunting', '赛车夺宝'),
        ('supertreasure', '至尊夺宝'),
        ('luckytree', '幸运摇钱树'),
    )
    
    id = models.AutoField(primary_key=True)
    user = models.ForeignKey(WechatUser, on_delete=models.CASCADE, related_name='lottery_records')
    activity_type = models.CharField(max_length=50, choices=ACTIVITY_TYPES, db_index=True)
    rarity_score = models.FloatField(default=0, help_text='稀有度得分')
    draw_count = models.IntegerField(default=0, help_text='总抽奖次数')
    has_legendary_items = models.BooleanField(default=False, help_text='是否有传说级物品')
    has_gold_items = models.BooleanField(default=False, help_text='是否有金色物品')
    timestamp = models.DateTimeField(auto_now=True, help_text='记录更新时间')
    
    class Meta:
        app_label = 'lottery'
        unique_together = ('user', 'activity_type')
        indexes = [
            models.Index(fields=['activity_type', 'rarity_score']),
            models.Index(fields=['activity_type', 'draw_count']),
        ]
        
    def __str__(self):
        return f"{self.user.nickname}的{self.get_activity_type_display()}记录"


class LotteryItem(models.Model):
    """
    抽奖物品模型，记录用户抽到的物品
    """
    BACKGROUND_TYPES = (
        ('legendary', '传说'),
        ('gold', '金色'),
        ('purple', '紫色'),
        ('normal', '普通'),
    )
    
    record = models.ForeignKey(LotteryRecord, on_delete=models.CASCADE, related_name='items')
    item_id = models.IntegerField(help_text='物品ID')
    name = models.CharField(max_length=100, help_text='物品名称')
    background = models.CharField(max_length=20, choices=BACKGROUND_TYPES, help_text='物品背景色')
    probability = models.FloatField(help_text='物品抽取概率')
    count = models.IntegerField(default=1, help_text='抽到的数量')
    
    class Meta:
        app_label = 'lottery'
    
    def __str__(self):
        return f"{self.name} (x{self.count})"
