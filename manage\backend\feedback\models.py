from django.db import models

class Feedback(models.Model):
    """
    用户反馈模型
    
    @property {str} type - 反馈类型：功能建议/Bug反馈/其他
    @property {str} content - 反馈内容
    @property {str} device_info - 用户设备信息（针对bug反馈）
    @property {str} contact - 联系方式
    @property {str} status - 处理状态：待处理/处理中/已处理
    @property {str} reply - 管理员回复内容
    @property {datetime} created_at - 创建时间
    @property {datetime} updated_at - 更新时间
    """
    
    FEEDBACK_TYPES = (
        ('feature', '功能建议'),
        ('bug', 'Bug反馈'),
        ('other', '其他')
    )
    
    STATUS_CHOICES = (
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已处理')
    )
    
    type = models.CharField('反馈类型', max_length=20, choices=FEEDBACK_TYPES)
    content = models.TextField('反馈内容')
    device_info = models.CharField('设备信息', max_length=200, blank=True, help_text='用户设备信息，主要用于bug反馈')
    contact = models.CharField('联系方式', max_length=100, blank=True)
    status = models.CharField('处理状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    reply = models.TextField('回复内容', blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        app_label = 'feedback'
        db_table = 'feedback'
        verbose_name = '用户反馈'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_type_display()} - {self.content[:20]}" 