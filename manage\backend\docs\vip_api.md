# VIP接口文档

## 目录
- [设置VIP状态](#设置vip状态)
- [获取VIP信息](#获取vip信息)

## 设置VIP状态

### 接口说明
设置用户的VIP状态，包括VIP标识和到期时间。

### 请求信息
- **接口URL**: `/api/user/vip/set/`
- **请求方式**: POST
- **需要认证**: 是

### 请求头
```
Authorization: Bearer <token>
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openid | string | 是 | 用户openid |
| is_vip | boolean | 是 | 是否为VIP用户 |
| vip_expire_at | string | 当is_vip为true时必填 | VIP到期时间，ISO 8601格式 |

### 响应参数
```json
{
    "success": true,
    "data": {
        "is_vip": true,
        "is_valid_vip": true,
        "vip_expire_at": "2024-12-31T23:59:59Z"
    }
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误信息"
}
```

### 错误码说明
- 400: 请求参数错误
- 401: 未认证或认证失败
- 404: 用户不存在
- 500: 服务器内部错误

## 获取VIP信息

### 接口说明
获取用户的VIP相关信息。

### 请求信息
- **接口URL**: `/api/user/vip/info/`
- **请求方式**: GET
- **需要认证**: 是

### 请求头
```
Authorization: Bearer <token>
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openid | string | 是 | 用户openid |

### 响应参数
```json
{
    "success": true,
    "data": {
        "is_vip": true,
        "is_valid_vip": true,
        "vip_expire_at": "2024-12-31T23:59:59Z"
    }
}
```

### 错误响应
```json
{
    "success": false,
    "message": "错误信息"
}
```

### 错误码说明
- 400: 请求参数错误
- 401: 未认证或认证失败
- 404: 用户不存在
- 500: 服务器内部错误

## 注意事项
1. 所有接口都需要在请求头中携带有效的JWT token
2. token中的openid必须与请求参数中的openid一致
3. VIP到期时间格式必须符合ISO 8601标准
4. 设置VIP状态时，如果is_vip为true，必须提供vip_expire_at参数 