# Generated by Django 3.2.23 on 2025-01-05 08:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0004_auto_20250101_2317'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='car',
            name='car_id',
            field=models.CharField(max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='car',
            name='drift_factor',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='car',
            name='friction_factor',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='car',
            name='high_speed_steering',
            field=models.Char<PERSON>ield(max_length=50),
        ),
        migrations.AlterField(
            model_name='car',
            name='image_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='car',
            name='level',
            field=models.Char<PERSON><PERSON>(choices=[('S', 'S级'), ('A', 'A级'), ('B', 'B级'), ('C', 'C级'), ('D', 'D级')], max_length=1),
        ),
        migrations.AlterField(
            model_name='car',
            name='low_speed_steering',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='car',
            name='name',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='car',
            name='nitro_speed',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='car',
            name='normal_speed',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='car',
            name='weight',
            field=models.CharField(max_length=50),
        ),
    ]
