from django.shortcuts import render
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
import logging

from .models import PrizeSource, Prize
from .serializers import PrizeSourceSerializer, PrizeSerializer, PrizeSourceDetailSerializer

# 配置日志
logger = logging.getLogger(__name__)

class PrizeSourceViewSet(viewsets.ModelViewSet):
    """
    奖品来源视图集，提供增删改查功能
    """
    queryset = PrizeSource.objects.all()
    serializer_class = PrizeSourceSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['source_type', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['id', 'name', 'created_at']

    def get_queryset(self):
        """
        获取奖品来源列表，支持搜索功能
        """
        queryset = PrizeSource.objects.all()

        # 获取查询参数
        search = self.request.query_params.get('search', '')
        source_type = self.request.query_params.get('source_type', '')
        active_only = self.request.query_params.get('active_only', 'false').lower() == 'true'

        # 应用过滤条件
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        if source_type:
            queryset = queryset.filter(source_type=source_type)

        if active_only:
            queryset = queryset.filter(is_active=True)

        return queryset

    def get_serializer_class(self):
        """根据操作选择合适的序列化器"""
        if self.action == 'retrieve' or self.action == 'prizes':
            return PrizeSourceDetailSerializer
        return PrizeSourceSerializer

    @action(detail=True, methods=['get'])
    def prizes(self, request, pk=None):
        """
        获取指定来源的所有奖品
        """
        try:
            source = self.get_object()
            serializer = PrizeSourceDetailSerializer(source)
            return Response({
                'success': True,
                'message': '获取奖品列表成功',
                'data': serializer.data
            })
        except Exception as e:
            logger.error(f"获取来源奖品列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取奖品列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def source_types(self, request):
        """
        获取所有来源类型选项
        """
        try:
            types = [
                {'value': choice[0], 'label': choice[1]}
                for choice in PrizeSource.SOURCE_TYPE_CHOICES
            ]

            return Response({
                'success': True,
                'message': '获取类型列表成功',
                'data': types
            })
        except Exception as e:
            logger.error(f"获取来源类型列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取类型列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PrizeViewSet(viewsets.ModelViewSet):
    """
    奖品视图集，提供增删改查功能
    """
    queryset = Prize.objects.all()
    serializer_class = PrizeSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['rarity', 'source', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['id', 'name', 'rarity', 'probability', 'created_at']

    def get_queryset(self):
        """
        获取奖品列表，支持搜索功能
        """
        queryset = Prize.objects.all()

        # 获取查询参数
        search = self.request.query_params.get('search', '')
        rarity = self.request.query_params.get('rarity', '')
        source_id = self.request.query_params.get('source_id', '')
        source_type = self.request.query_params.get('source_type', '')
        active_only = self.request.query_params.get('active_only', 'false').lower() == 'true'

        # 应用过滤条件
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        if rarity:
            queryset = queryset.filter(rarity=rarity)

        if source_id:
            queryset = queryset.filter(source_id=source_id)

        if source_type:
            queryset = queryset.filter(source__source_type=source_type)

        if active_only:
            queryset = queryset.filter(is_active=True)

        return queryset

    @action(detail=False, methods=['get'])
    def rarity_types(self, request):
        """
        获取所有稀有度类型选项
        """
        try:
            types = [
                {'value': choice[0], 'label': choice[1]}
                for choice in Prize.RARITY_CHOICES
            ]

            return Response({
                'success': True,
                'message': '获取稀有度类型列表成功',
                'data': types
            })
        except Exception as e:
            logger.error(f"获取稀有度类型列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'获取类型列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
