from rest_framework.routers import DefaultRouter
from django.urls import path, include
from .views import (
    CarViewSet, PropulsionLevelTableViewSet, CalculationHistoryViewSet,
    submit_rating, get_car_ratings, get_user_rating,
    create_comment, delete_comment, get_car_comments, admin_get_comments,
    admin_delete_comment, get_user_comments, edit_comment, report_comment,
    admin_get_reports, admin_handle_report, check_sensitive_words,
    add_sensitive_word, delete_sensitive_word, get_sensitive_words,
    batch_add_sensitive_words, enable_sensitive_word, export_sensitive_words_template
)
from .propulsion_views import search_cars, CalculatePropulsionView, GenerateChartView
from .curve_views import CurveGenerationView
from django.views.generic import TemplateView

# 注意：由于在主 urls.py 中已经注册了 CarViewSet，这里不需要重复注册
app_name = 'cars'  # 添加应用命名空间

router = DefaultRouter()
# router.register(r'cars', CarViewSet)  # 注释掉，避免与主URL冲突
router.register(r'propulsion-levels', PropulsionLevelTableViewSet)
router.register(r'calculation-history', CalculationHistoryViewSet)

urlpatterns = [
    path('', include(router.urls)),
    # 评分相关接口
    path('cars/ratings/submit/', submit_rating, name='submit-rating'),
    path('cars/ratings/user/', get_user_rating, name='get-user-rating'),
    path('cars/ratings/<str:car_id>/', get_car_ratings, name='get-car-ratings'),
    # 评论相关接口
    path('comments/create/', create_comment, name='create_comment'),
    path('comments/<int:comment_id>/delete/', delete_comment, name='delete_comment'),
    path('comments/<int:comment_id>/edit/', edit_comment, name='edit_comment'),
    path('comments/<int:comment_id>/report/', report_comment, name='report_comment'),
    path('cars/<str:car_id>/comments/', get_car_comments, name='get_car_comments'),
    path('user/comments/', get_user_comments, name='get_user_comments'),
    # 管理员接口
    path('admin/comments/', admin_get_comments, name='admin_get_comments'),
    path('admin/comments/<int:comment_id>/delete/', admin_delete_comment, name='admin_delete_comment'),
    path('admin/reports/', admin_get_reports, name='admin_get_reports'),
    path('admin/reports/<int:report_id>/handle/', admin_handle_report, name='admin_handle_report'),
    path('admin/reports/<int:report_id>/handle', admin_handle_report, name='admin_handle_report_no_slash'),
    # 敏感词相关接口
    path('sensitive/check/', check_sensitive_words, name='check_sensitive_words'),
    path('sensitive/add/', add_sensitive_word, name='add_sensitive_word'),
    path('sensitive/batch_add/', batch_add_sensitive_words, name='batch_add_sensitive_words'),
    path('sensitive/<int:word_id>/delete/', delete_sensitive_word, name='delete_sensitive_word'),
    path('sensitive/<int:word_id>/enable/', enable_sensitive_word, name='enable_sensitive_word'),
    path('sensitive/list/', get_sensitive_words, name='get_sensitive_words'),
    path('sensitive/template/', export_sensitive_words_template, name='export_sensitive_words_template'),

    # 推进计算与绘图功能接口
    path('cars/search/', search_cars, name='search_cars'),
    path('cars/calculate-propulsion/', CalculatePropulsionView.as_view(), name='calculate_propulsion'),
    path('cars/generate-chart/', GenerateChartView.as_view(), name='generate_chart'),

    # 新的曲线绘制接口
    path('cars/generate-curves/', CurveGenerationView.as_view(), name='generate_curves'),

    # 测试页面
    path('propulsion-test/', TemplateView.as_view(template_name='propulsion_test.html'), name='propulsion_test'),
]
