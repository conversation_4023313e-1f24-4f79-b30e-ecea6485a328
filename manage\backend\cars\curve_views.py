"""
赛车推进计算与曲线绘制接口
基于speed_power-view.py和speed_time-view.py的逻辑实现
"""
import numpy as np
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import PropulsionLevelTable
from scipy.integrate import solve_ivp


@method_decorator(csrf_exempt, name='dispatch')
class CurveGenerationView(APIView):
    """
    赛车推进计算与曲线绘制接口
    
    支持生成两种类型的曲线：
    1. power_speed: 动力-速度曲线
    2. speed_time: 速度-时间曲线
    
    支持最多两辆赛车对比
    """
    
    def post(self, request):
        """
        POST /api/cars/generate-curves/
        
        请求参数：
        {
            "curve_type": "power_speed" | "speed_time",
            "cars": [
                {
                    "name": "赛车名称",
                    "level": "赛车等级",
                    "propulsion_levels": [推进1-7档原装数据],
                    "engine_levels": [引擎1-6档数据],
                    "fuel_intensity": 燃料强度,
                    "ignition_intensity": 点火强度,
                    "propulsion_upgrades": 推进改装次数(0-40)
                }
            ]
        }
        """
        try:
            # 验证请求参数
            curve_type = request.data.get('curve_type')
            cars = request.data.get('cars', [])
            
            if not curve_type or curve_type not in ['power_speed', 'speed_time']:
                return Response({
                    'success': False,
                    'message': '无效的曲线类型，支持: power_speed, speed_time'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not cars or len(cars) == 0:
                return Response({
                    'success': False,
                    'message': '至少需要提供一辆赛车的数据'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if len(cars) > 2:
                return Response({
                    'success': False,
                    'message': '最多支持两辆赛车对比'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证并处理每辆赛车的数据
            processed_cars = []
            for i, car_data in enumerate(cars):
                try:
                    processed_car = self.process_car_data(car_data)
                    processed_cars.append(processed_car)
                except ValueError as e:
                    return Response({
                        'success': False,
                        'message': f'第{i+1}辆赛车数据错误: {str(e)}'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # 根据曲线类型生成数据
            if curve_type == 'power_speed':
                result_data = self.generate_power_speed_curves(processed_cars)
            else:  # speed_time
                result_data = self.generate_speed_time_curves(processed_cars)
            
            return Response({
                'success': True,
                'data': result_data
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'生成曲线失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def process_car_data(self, car_data):
        """
        处理和验证单辆赛车的数据
        """
        # 验证必要字段
        required_fields = ['name', 'level', 'propulsion_levels', 'engine_levels', 'fuel_intensity', 'ignition_intensity']
        for field in required_fields:
            if field not in car_data:
                raise ValueError(f'缺少必要字段: {field}')
        
        name = car_data['name']
        level = car_data['level']
        propulsion_levels = car_data['propulsion_levels']
        engine_levels = car_data['engine_levels']
        fuel_intensity = car_data['fuel_intensity']
        ignition_intensity = car_data['ignition_intensity']
        propulsion_upgrades = car_data.get('propulsion_upgrades', 40)  # 默认40次改装
        
        # 验证数据格式
        if not isinstance(propulsion_levels, list) or len(propulsion_levels) != 7:
            raise ValueError('推进档位数据必须是包含7个数值的数组')
        
        if not isinstance(engine_levels, list) or len(engine_levels) != 6:
            raise ValueError('引擎档位数据必须是包含6个数值的数组')
        
        if not isinstance(propulsion_upgrades, int) or propulsion_upgrades < 0 or propulsion_upgrades > 40:
            raise ValueError('推进改装次数必须是0-40之间的整数')
        
        # 先将原装数据乘以1000
        propulsion_levels_scaled = [level * 1000 for level in propulsion_levels]
        engine_levels_scaled = [level * 1000 for level in engine_levels]

        # 计算推进40档位数据
        propulsion_40_levels = self.calculate_propulsion_40(level, propulsion_levels_scaled, propulsion_upgrades)

        # 动力数据已经是正确的数值
        propulsion_powers = propulsion_40_levels
        engine_powers = engine_levels_scaled
        
        return {
            'name': name,
            'level': level,
            'propulsion_powers': propulsion_powers,
            'engine_powers': engine_powers,
            'fuel_intensity': fuel_intensity,
            'ignition_intensity': ignition_intensity,
            'propulsion_upgrades': propulsion_upgrades
        }
    
    def calculate_propulsion_40(self, car_level, original_levels, upgrade_count):
        """
        根据赛车等级和改装次数计算推进档位数据
        """
        try:
            # 直接根据传入的赛车等级查询推进计算表
            table_data = PropulsionLevelTable.objects.get(car_level=car_level)
        except PropulsionLevelTable.DoesNotExist:
            # 如果没有找到计算表数据，返回错误信息
            raise ValueError(f"该级别赛车不支持改装推进：{car_level}")
        
        propulsion_40_levels = []
        
        for i, original_level in enumerate(original_levels):
            level_num = i + 1
            
            # 获取差值和上限
            diff_value = getattr(table_data, f'level_{level_num}_diff')
            max_value = getattr(table_data, f'level_{level_num}_max')
            
            # 计算实际改装提升（按比例）
            actual_diff = int(diff_value * upgrade_count / 40)
            calculated_40 = original_level + actual_diff
            
            if max_value is None:
                # 无上限（带+号）
                final_40 = calculated_40
            else:
                # 有上限，取最小值
                final_40 = min(calculated_40, max_value)
            
            propulsion_40_levels.append(int(final_40))
        
        return propulsion_40_levels
    
    def generate_power_speed_curves(self, cars):
        """
        生成动力-速度曲线数据
        基于speed_power-view.py的逻辑
        """
        # 速度锚点（基于speed_power-view.py）
        speed_anchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]
        
        # 生成速度动力平衡线数据
        speed_range = np.linspace(0, 382, 1000)
        balance_powers = []
        for speed in speed_range:
            if speed < 45.9:
                power = 10.9 * speed + 0.14 * speed * speed
            else:
                power = 500.81703297 - 0.00308486 * speed + 0.14017491 * speed * speed
            balance_powers.append(power)
        
        balance_curve = {
            'name': '速度动力平衡线',
            'data': [[float(speed), float(power)] for speed, power in zip(speed_range, balance_powers)]
        }
        
        # 处理每辆赛车的数据
        car_curves = []
        for car in cars:
            # 构建速度-动力数据点（基于speed_power-view.py的逻辑）
            speed_power_data = []
            
            # 前7个点使用推进档位数据
            for i in range(7):
                speed = speed_anchors[i]
                power = car['propulsion_powers'][i]
                speed_power_data.append([speed, power])
            
            # 后6个点使用引擎档位数据
            for i in range(6):
                speed = speed_anchors[7 + i]
                power = car['engine_powers'][i]
                speed_power_data.append([speed, power])
            
            # 计算大喷动力线（燃料强度）
            fuel_power_data = []
            for speed, base_power in speed_power_data:
                fuel_power = base_power + (car['fuel_intensity'] / 1.2)
                fuel_power_data.append([speed, fuel_power])
            
            # 计算cww动力线（燃料强度 + 点火强度）
            boost_power_data = []
            for speed, base_power in speed_power_data:
                boost_power = base_power + ((car['fuel_intensity'] + car['ignition_intensity']) / 1.2)
                boost_power_data.append([speed, boost_power])
            
            car_curves.append({
                'name': car['name'],
                'base_power': speed_power_data,
                'fuel_power': fuel_power_data,
                'boost_power': boost_power_data
            })
        
        return {
            'curve_type': 'power_speed',
            'balance_curve': balance_curve,
            'car_curves': car_curves
        }

    def generate_speed_time_curves(self, cars):
        """
        生成速度-时间曲线数据
        基于speed_time-view.py的逻辑
        """
        # 速度锚点（与power_speed相同）
        speed_anchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]

        # 阻力函数（基于speed_time-view.py）
        def resistance(v):
            if v < 45.9:
                return 10.9 * v + 0.14 * v ** 2
            else:
                return 500.81703297 - 0.00308486 * v + 0.14017491 * v ** 2

        # 动力函数（分段函数）
        def power(v, power_values):
            return np.interp(v, speed_anchors, power_values)

        # 微分方程：dv/dt = (power(v) - resistance(v)) / mass
        def dv_dt(t, v, power_values, fuel_power=0):
            p = power(v, power_values) + fuel_power
            return (p - resistance(v)) / 54.15

        # 时间范围和积分参数
        t_span = (0, 16)
        t_eval = np.linspace(0, 16, 200)  # 减少到200个数据点，足够绘制平滑曲线
        atol = 1e-8   # 适当降低精度要求
        rtol = 1e-6   # 适当降低精度要求

        # 处理每辆赛车的数据
        car_curves = []
        for car in cars:
            # 构建完整的动力值数组（推进+引擎）
            power_values = []

            # 前7个点使用推进档位数据
            for i in range(7):
                power_values.append(car['propulsion_powers'][i])

            # 后6个点使用引擎档位数据
            for i in range(6):
                power_values.append(car['engine_powers'][i])

            # 计算平跑速度线
            sol_normal = solve_ivp(
                lambda t, v: dv_dt(t, v, power_values),
                t_span, [0],
                t_eval=t_eval,
                atol=atol,
                rtol=rtol
            )

            # 计算大喷速度线
            fuel_power = car['fuel_intensity'] / 1.2
            sol_fuel = solve_ivp(
                lambda t, v: dv_dt(t, v, power_values, fuel_power),
                t_span, [0],
                t_eval=t_eval,
                atol=atol,
                rtol=rtol
            )

            # 计算超级喷速度线（燃料强度 + 点火强度）
            super_power = (car['fuel_intensity'] + car['ignition_intensity']) / 1.2
            sol_super = solve_ivp(
                lambda t, v: dv_dt(t, v, power_values, super_power),
                t_span, [0],
                t_eval=t_eval,
                atol=atol,
                rtol=rtol
            )

            # 转换为前端可用的数据格式
            normal_data = [[float(t), float(v)] for t, v in zip(sol_normal.t, sol_normal.y[0])]
            fuel_data = [[float(t), float(v)] for t, v in zip(sol_fuel.t, sol_fuel.y[0])]
            super_data = [[float(t), float(v)] for t, v in zip(sol_super.t, sol_super.y[0])]

            car_curves.append({
                'name': car['name'],
                'normal_speed': normal_data,
                'fuel_speed': fuel_data,
                'super_speed': super_data
            })

        return {
            'curve_type': 'speed_time',
            'car_curves': car_curves
        }
