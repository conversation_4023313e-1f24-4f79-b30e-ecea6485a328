# Generated by Django 3.2.23 on 2025-05-07 03:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('treasure', '0009_alter_prize_quantity'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='prize',
            name='source',
        ),
        migrations.CreateModel(
            name='PrizeSourceRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('probability', models.DecimalField(blank=True, decimal_places=6, help_text='在特定来源中的获取概率，百分比', max_digits=10, null=True, verbose_name='获取概率')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('prize', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_relations', to='treasure.prize', verbose_name='奖品')),
                ('source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prize_relations', to='treasure.prizesource', verbose_name='来源')),
            ],
            options={
                'verbose_name': '奖品来源关系',
                'verbose_name_plural': '奖品来源关系',
                'ordering': ['-created_at'],
                'unique_together': {('prize', 'source')},
            },
        ),
        migrations.AddField(
            model_name='prize',
            name='sources',
            field=models.ManyToManyField(related_name='related_prizes', through='treasure.PrizeSourceRelation', to='treasure.PrizeSource', verbose_name='所属来源'),
        ),
    ]
