# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name pikario.site www.pikario.site;  # 添加 www 子域名
    # 媒体文件不需要重定向
    location /media/ {
    	alias /home/<USER>/site/QQspeed-guide/manage/backend/media/;
    	autoindex on;  # 临时开启目录列表，用于调试
    	expires 7d;
    	add_header Cache-Control "public, no-transform";
    }

    # web 目录不需要重定向
    location /web/ {
        alias /var/www/web/;
        autoindex on;  # 临时开启目录列表，用于调试
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }

    # 其他所有请求重定向到 HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS 服务器配置
server {
    listen 443 ssl;
    http2 on;        # 使用新的 http2 指令
    server_name pikario.site;

    # 前端静态文件目录
    root /var/www/qq-speed-car/dist;
    index index.html;

    # SSL 配置
    ssl_certificate    /www/server/panel/vhost/cert/QQspeedGuide/pikario.site_bundle.crt;
    ssl_certificate_key    /www/server/panel/vhost/cert/QQspeedGuide/pikario.site.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 开启 gzip 压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    # 添加 /web 路径的配置
    location /web/ {
        alias /var/www/web/;
        autoindex on;  # 临时开启目录列表，用于调试

        # 添加基本的安全头
        add_header X-Content-Type-Options "nosniff";
        add_header X-Frame-Options "SAMEORIGIN";

        # 缓存设置
        expires 7d;
        add_header Cache-Control "public, no-transform";

        # 确保正确的文件类型
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        # 允许所有文件类型
        location ~* \.(jpg|jpeg|png|gif|ttf|woff2|ico|css|js|pdf|txt|mp4|webp|html|htm)$ {
            try_files $uri =404;
        }
    }

    # API接口代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;

        # 简单的 CORS 配置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' '*' always;
        add_header 'Access-Control-Allow-Headers' '*' always;

        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # 媒体文件配置
    location /media/ {
        alias /home/<USER>/site/QQspeed-guide/manage/backend/media/;
        autoindex off;

        # 添加基本的安全头
        add_header X-Content-Type-Options "nosniff";
        add_header X-Frame-Options "SAMEORIGIN";

        # 缓存设置
        expires 7d;
        add_header Cache-Control "public, no-transform";

        # 确保正确的文件类型
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        # 允许的文件类型
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|mp4|webp)$ {
            try_files $uri =404;
        }
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }

    # 前端路由支持 - 使用更安全的方法
    # 首先尝试找到实际文件，如果找不到则尝试index.html
    location / {
        # 检查请求的路径是否是已知的无效路径
        # 如果是，直接返回404
        if ($request_uri ~* "^/(prizing|nonexistent-path|another-fake-path)") {
            return 404;
        }

        # 否则尝试找到实际文件，如果找不到则使用index.html
        try_files $uri $uri/ /index.html;

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
    }

    # 安全性配置
    add_header Strict-Transport-Security "max-age=31536000" always;

    # 禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README.md|package.json|package-lock.json|\.env) {
        return 404;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    # 404页面配置
    location = /404.html {
        root /var/www/web;
        internal;
    }
}

