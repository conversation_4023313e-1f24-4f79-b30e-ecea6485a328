"""
推进计算与绘图功能相关视图
"""
import numpy as np
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view
from django.db.models import Q
from .models import Car, PropulsionLevelTable, CalculationHistory
from .serializers import CarSerializer, PropulsionLevelTableSerializer, CalculationHistorySerializer
from .propulsion_utils import (
    extract_numeric_value, parse_car_level, calculate_propulsion_40_single,
    calculate_fuel_power, calculate_boost_power, get_speed_anchors,
    generate_balance_curve_data, get_chart_colors, create_echarts_series,
    simulate_acceleration
)


@api_view(['GET'])
def search_cars(request):
    """
    赛车搜索接口
    GET /api/cars/search/?q=关键词
    """
    try:
        query = request.GET.get('q', '').strip()
        if not query:
            return Response({
                'success': False,
                'message': '请输入搜索关键词'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 搜索赛车名称或编号
        cars = Car.objects.filter(
            Q(name__icontains=query) | Q(car_id__icontains=query)
        )[:10]  # 限制返回10条结果

        # 构建返回数据
        results = []
        for car in cars:
            # 提取引擎档位数据
            engine_levels = []
            for i in range(1, 7):
                gear_value = getattr(car, f'engine_gear_{i}', None)
                engine_levels.append(extract_numeric_value(gear_value))

            # 提取推进档位数据
            propulsion_levels = []
            for i in range(1, 7):
                prop_value = getattr(car, f'original_propulsion_{i}', None)
                propulsion_levels.append(extract_numeric_value(prop_value))
            # 添加推进7档
            propulsion_levels.append(extract_numeric_value(car.original_propulsion_7))

            # 提取燃料强度
            fuel_intensity = extract_numeric_value(car.fuel_intensity) or 6290  # 默认值

            results.append({
                'car_id': car.car_id,
                'name': car.name,
                'level': car.level,
                'engine_levels': engine_levels,
                'propulsion_levels': propulsion_levels,
                'fuel_intensity': fuel_intensity
            })

        return Response({
            'success': True,
            'data': results
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'搜索失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CalculatePropulsionView(APIView):
    """推进40计算接口"""

    def post(self, request):
        try:
            car_id = request.data.get('car_id')
            if not car_id:
                return Response({
                    'success': False,
                    'message': '缺少赛车ID参数'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取赛车数据
            try:
                car = Car.objects.get(car_id=car_id)
            except Car.DoesNotExist:
                return Response({
                    'success': False,
                    'message': '未找到指定赛车'
                }, status=status.HTTP_404_NOT_FOUND)

            # 提取原装推进档位数据
            original_propulsion_levels = []
            for i in range(1, 7):
                prop_value = getattr(car, f'original_propulsion_{i}', None)
                original_propulsion_levels.append(extract_numeric_value(prop_value))
            # 添加推进7档
            original_propulsion_levels.append(extract_numeric_value(car.original_propulsion_7))

            # 提取引擎档位数据
            engine_levels = []
            for i in range(1, 7):
                gear_value = getattr(car, f'engine_gear_{i}', None)
                engine_levels.append(extract_numeric_value(gear_value))

            # 提取燃料强度
            fuel_intensity = extract_numeric_value(car.fuel_intensity) or 6290

            # 计算推进40档位
            propulsion_40_levels = self.calculate_propulsion_40(car.level, original_propulsion_levels)

            # 计算动力数据
            power_data = self.calculate_power_data(propulsion_40_levels, engine_levels, fuel_intensity)

            # 构建返回数据
            result_data = {
                'car_info': {
                    'car_id': car.car_id,
                    'name': car.name,
                    'level': car.level
                },
                'original_data': {
                    'propulsion_levels': original_propulsion_levels,
                    'engine_levels': engine_levels,
                    'fuel_intensity': fuel_intensity
                },
                'propulsion_40_levels': propulsion_40_levels,
                'power_data': power_data
            }

            # 保存计算历史（可选）
            try:
                user_id = request.data.get('user_id', 'anonymous')
                history = CalculationHistory(user_id=user_id)
                history.set_car_data({
                    'car_id': car.car_id,
                    'name': car.name,
                    'level': car.level,
                    'original_propulsion_levels': original_propulsion_levels,
                    'engine_levels': engine_levels,
                    'fuel_intensity': fuel_intensity
                })
                history.set_calculation_result(result_data)
                history.save()
            except Exception:
                pass  # 历史记录保存失败不影响主要功能

            return Response({
                'success': True,
                'data': result_data
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'计算失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def calculate_propulsion_40(self, car_level, original_levels):
        """计算推进40档位"""
        try:
            # 处理T2皮肤等级
            lookup_level = parse_car_level(car_level)

            # 获取对应等级的计算表数据
            table_data = PropulsionLevelTable.objects.get(car_level=lookup_level)
        except PropulsionLevelTable.DoesNotExist:
            # 如果没有找到计算表数据，使用默认计算方式
            return [int(level + 300) for level in original_levels]  # 简单的默认计算

        propulsion_40_levels = []

        for i, original_level in enumerate(original_levels):
            level_num = i + 1

            # 获取差值和上限
            diff_value = getattr(table_data, f'level_{level_num}_diff')
            max_value = getattr(table_data, f'level_{level_num}_max')

            # 使用工具函数计算推进40
            final_40 = calculate_propulsion_40_single(original_level, diff_value, max_value)
            propulsion_40_levels.append(final_40)

        return propulsion_40_levels

    def calculate_power_data(self, propulsion_levels, engine_levels, fuel_intensity):
        """计算动力数据"""
        # 获取标准速度锚点
        speed_anchors = get_speed_anchors()

        # 基础动力计算
        base_powers = self.calculate_base_powers(propulsion_levels, engine_levels, speed_anchors)

        # 使用工具函数计算大喷和cww动力
        fuel_powers = [calculate_fuel_power(power, fuel_intensity) for power in base_powers]
        boost_powers = [calculate_boost_power(power, fuel_intensity) for power in base_powers]

        return {
            'speed_anchors': speed_anchors,
            'base_powers': base_powers,
            'fuel_powers': fuel_powers,
            'boost_powers': boost_powers
        }

    def calculate_base_powers(self, propulsion_levels, engine_levels, speed_anchors):
        """计算基础动力值（根据设计文档的算法实现）"""
        base_powers = []

        # 根据设计文档，基础动力计算需要考虑推进档位和引擎档位的组合
        # 速度锚点对应不同的推进档位区间
        speed_ranges = [
            (0, 76.5),      # 推进1档区间
            (76.5, 87.21),  # 推进2档区间
            (87.21, 137.7), # 推进3档区间
            (137.7, 142.29),# 推进4档区间
            (142.29, 168.3),# 推进5档区间
            (168.3, 180.54),# 推进6档区间
            (180.54, 229.5),# 推进7档区间
            (229.5, 244.8), # 继续使用推进7档
            (244.8, 306),   # 继续使用推进7档
            (306, 382.5),   # 继续使用推进7档
            (382.5, 459),   # 继续使用推进7档
            (459, 535.5),   # 继续使用推进7档
            (535.5, float('inf'))  # 继续使用推进7档
        ]

        for i, speed in enumerate(speed_anchors):
            if i < 7:  # 前7个速度锚点对应推进1-7档
                # 直接使用对应的推进档位值
                power = propulsion_levels[i] if i < len(propulsion_levels) else propulsion_levels[-1]
            else:
                # 超过推进7档的速度锚点，使用推进7档的值
                power = propulsion_levels[6] if len(propulsion_levels) > 6 else propulsion_levels[-1]

            # 考虑引擎档位的影响（简化处理）
            if i < len(engine_levels):
                engine_factor = 1 + (engine_levels[i] - 40) * 0.01  # 引擎档位影响系数
                power *= engine_factor

            base_powers.append(int(power))  # 转换为整数

        return base_powers


class GenerateChartView(APIView):
    """图表生成接口"""

    def post(self, request):
        try:
            chart_type = request.data.get('chart_type')
            cars = request.data.get('cars', [])

            if not chart_type or not cars:
                return Response({
                    'success': False,
                    'message': '缺少必要参数'
                }, status=status.HTTP_400_BAD_REQUEST)

            if chart_type == 'power_speed':
                chart_config = self.generate_power_speed_chart(cars)
            elif chart_type == 'speed_time':
                chart_config = self.generate_speed_time_chart(cars)
            else:
                return Response({
                    'success': False,
                    'message': '不支持的图表类型'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'success': True,
                'data': {
                    'chart_config': chart_config
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'图表生成失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def generate_power_speed_chart(self, cars):
        """生成动力-速度图表配置"""
        # 生成速度动力平衡线数据
        balance_speeds, balance_powers = generate_balance_curve_data()

        # 创建平衡线系列
        series = [create_echarts_series(
            name='速度动力平衡线',
            data=[[x, y] for x, y in zip(balance_speeds, balance_powers)],
            color='#000000',
            line_type='solid',
            symbol='none'
        )]

        colors = get_chart_colors()

        for i, car in enumerate(cars):
            power_data = car.get('power_data', {})
            speed_anchors = power_data.get('speed_anchors', [])
            base_powers = power_data.get('base_powers', [])
            fuel_powers = power_data.get('fuel_powers', [])
            boost_powers = power_data.get('boost_powers', [])

            color = colors[i % len(colors)]

            # 使用工具函数创建系列数据
            # 基础动力线
            series.append(create_echarts_series(
                name=f"{car['name']} 基础动力",
                data=[[x, y] for x, y in zip(speed_anchors, base_powers)],
                color=color,
                line_type='solid',
                symbol='circle'
            ))

            # 大喷动力线
            series.append(create_echarts_series(
                name=f"{car['name']} 大喷动力",
                data=[[x, y] for x, y in zip(speed_anchors, fuel_powers)],
                color=color,
                line_type='dashed',
                symbol='circle'
            ))

            # cww动力线
            series.append(create_echarts_series(
                name=f"{car['name']} cww动力",
                data=[[x, y] for x, y in zip(speed_anchors, boost_powers)],
                color=color,
                line_type='dotted',
                symbol='circle'
            ))

        return {
            'title': {
                'text': 'QQ飞车赛车动力-速度曲线图',
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'axis'
            },
            'legend': {
                'data': [s['name'] for s in series],
                'bottom': 0
            },
            'xAxis': {
                'type': 'value',
                'name': '速度(km/h)',
                'min': 0,
                'max': 400
            },
            'yAxis': {
                'type': 'value',
                'name': '动力(N)'
            },
            'series': series
        }

    def generate_speed_time_chart(self, cars):
        """生成速度-时间图表配置"""
        series = []
        colors = get_chart_colors()

        for i, car in enumerate(cars):
            # 使用工具函数计算速度-时间数据
            power_data = car.get('power_data', {})
            time_data, speed_data = simulate_acceleration(power_data)

            color = colors[i % len(colors)]

            # 使用工具函数创建系列
            series.append(create_echarts_series(
                name=f"{car['name']} 平跑",
                data=[[t, v] for t, v in zip(time_data, speed_data)],
                color=color,
                line_type='solid',
                symbol='none'
            ))

        return {
            'title': {
                'text': 'QQ飞车赛车速度-时间曲线图',
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'axis'
            },
            'legend': {
                'data': [s['name'] for s in series],
                'bottom': 0
            },
            'xAxis': {
                'type': 'value',
                'name': '时间(秒)',
                'min': 0,
                'max': 16
            },
            'yAxis': {
                'type': 'value',
                'name': '速度(km/h)',
                'min': 0,
                'max': 350
            },
            'series': series
        }


