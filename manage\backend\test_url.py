#!/usr/bin/env python
"""
测试URL路由
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from django.test import Client

def test_url_routing():
    """测试URL路由"""
    client = Client()
    
    # 测试GET请求
    print("测试GET请求...")
    response = client.get("/api/cars/generate-curves/")
    print(f"GET状态码: {response.status_code}")
    print(f"GET响应: {response.content.decode('utf-8')}")
    
    # 测试POST请求
    print("\n测试POST请求...")
    response = client.post("/api/cars/generate-curves/", data='{}', content_type='application/json')
    print(f"POST状态码: {response.status_code}")
    print(f"POST响应: {response.content.decode('utf-8')}")
    
    # 测试其他已知的URL
    print("\n测试其他URL...")
    response = client.get("/api/cars/search/")
    print(f"search GET状态码: {response.status_code}")
    
    response = client.post("/api/cars/calculate-propulsion/", data='{}', content_type='application/json')
    print(f"calculate-propulsion POST状态码: {response.status_code}")

if __name__ == "__main__":
    test_url_routing()
