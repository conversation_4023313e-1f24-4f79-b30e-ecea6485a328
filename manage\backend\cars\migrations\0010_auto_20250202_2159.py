# Generated by Django 3.2.23 on 2025-02-02 13:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0009_carrating'),
    ]

    operations = [
        migrations.CreateModel(
            name='CarComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('car_id', models.CharField(max_length=50, verbose_name='赛车编号')),
                ('openid', models.CharField(max_length=100, verbose_name='用户OpenID')),
                ('content', models.TextField(verbose_name='评论内容')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
            ],
            options={
                'verbose_name': '赛车评论',
                'verbose_name_plural': '赛车评论',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommentReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reporter_openid', models.CharField(max_length=100, verbose_name='举报人OpenID')),
                ('reason', models.TextField(verbose_name='举报原因')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('resolved', '已处理'), ('rejected', '已驳回')], default='pending', max_length=20, verbose_name='处理状态')),
                ('handler_note', models.TextField(blank=True, null=True, verbose_name='处理备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='举报时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('handled_at', models.DateTimeField(blank=True, null=True, verbose_name='处理时间')),
                ('comment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cars.carcomment', verbose_name='被举报评论')),
            ],
            options={
                'verbose_name': '评论举报',
                'verbose_name_plural': '评论举报',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='carcomment',
            index=models.Index(fields=['car_id'], name='cars_carcom_car_id_a8f980_idx'),
        ),
        migrations.AddIndex(
            model_name='carcomment',
            index=models.Index(fields=['openid'], name='cars_carcom_openid_4b7df6_idx'),
        ),
        migrations.AddIndex(
            model_name='carcomment',
            index=models.Index(fields=['is_deleted'], name='cars_carcom_is_dele_79547c_idx'),
        ),
        migrations.AddIndex(
            model_name='commentreport',
            index=models.Index(fields=['comment'], name='cars_commen_comment_6d3f23_idx'),
        ),
        migrations.AddIndex(
            model_name='commentreport',
            index=models.Index(fields=['reporter_openid'], name='cars_commen_reporte_41d49c_idx'),
        ),
        migrations.AddIndex(
            model_name='commentreport',
            index=models.Index(fields=['status'], name='cars_commen_status_f594a4_idx'),
        ),
    ]
