"""
清空奖品和道具数据的管理命令
"""
import os
import django
from django.core.management.base import BaseCommand
from django.db import transaction
from treasure.models import Prize, PrizeSource, PrizeSourceRelation

class Command(BaseCommand):
    help = '清空奖品和道具数据，包括所有关联关系'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='确认执行删除操作',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(self.style.WARNING('警告: 此操作将删除所有奖品和道具数据!'))
            self.stdout.write(self.style.WARNING('请添加 --confirm 参数以确认执行此操作'))
            return

        try:
            with transaction.atomic():
                # 首先删除所有关联关系
                relation_count = PrizeSourceRelation.objects.count()
                PrizeSourceRelation.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'成功删除 {relation_count} 条奖品与道具的关联记录'))

                # 然后删除所有奖品
                prize_count = Prize.objects.count()
                Prize.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'成功删除 {prize_count} 条奖品记录'))

                # 最后删除所有道具
                source_count = PrizeSource.objects.count()
                PrizeSource.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'成功删除 {source_count} 条道具记录'))

                self.stdout.write(self.style.SUCCESS('所有数据已成功清空，现在可以重新导入数据了'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'删除数据时出错: {str(e)}'))
