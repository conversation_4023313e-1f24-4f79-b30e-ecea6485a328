<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赛车推进计算与曲线绘制API测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .control-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            margin-left: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .chart-container {
            width: 100%;
            height: 600px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ飞车赛车推进计算与曲线绘制API测试</h1>
        
        <div class="controls">
            <div class="control-group">
                <label>曲线类型:</label>
                <select id="curveType">
                    <option value="power_speed">动力-速度曲线</option>
                    <option value="speed_time">速度-时间曲线</option>
                </select>
                <button onclick="generateCurve()">生成曲线</button>
                <button onclick="loadTestData()">加载测试数据</button>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div id="chartContainer" class="chart-container"></div>
        
        <div style="margin-top: 20px;">
            <h3>测试说明</h3>
            <ul>
                <li><strong>动力-速度曲线</strong>：显示赛车在不同速度下的动力输出，包括基础动力、大喷动力和cww动力</li>
                <li><strong>速度-时间曲线</strong>：显示赛车在16秒内的速度变化，包括平跑、大喷和超级喷三种模式</li>
                <li>测试数据基于收割者（推进40）和收割者（推进0）的对比</li>
                <li>数据来源于speed_power-view.py和speed_time-view.py的演示文件</li>
            </ul>
        </div>
    </div>

    <script>
        let chart = null;

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        function initChart() {
            const chartContainer = document.getElementById('chartContainer');
            chart = echarts.init(chartContainer);
        }

        function loadTestData() {
            showStatus('使用预设的测试数据...', 'loading');
            generateCurve();
        }

        async function generateCurve() {
            const curveType = document.getElementById('curveType').value;
            
            showStatus('正在生成曲线数据...', 'loading');

            // 测试数据
            const testData = {
                curve_type: curveType,
                cars: [
                    {
                        name: "收割者（推进40）",
                        level: "A/M3/L3",
                        propulsion_levels: [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                        engine_levels: [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                        fuel_intensity: 6290,
                        ignition_intensity: 6290,
                        propulsion_upgrades: 40
                    },
                    {
                        name: "收割者（推进0）",
                        level: "A/M3/L3",
                        propulsion_levels: [4.2, 4.4, 5.0, 5.35, 6.05, 6.25, 6.4],
                        engine_levels: [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                        fuel_intensity: 6290,
                        ignition_intensity: 6290,
                        propulsion_upgrades: 0
                    }
                ]
            };

            try {
                const response = await fetch('/api/cars/generate-curves/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();

                if (result.success) {
                    showStatus('曲线生成成功！', 'success');
                    renderChart(result.data, curveType);
                } else {
                    showStatus(`生成失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        function renderChart(data, curveType) {
            if (!chart) {
                initChart();
            }

            let option;

            if (curveType === 'power_speed') {
                option = createPowerSpeedOption(data);
            } else {
                option = createSpeedTimeOption(data);
            }

            chart.setOption(option, true);
        }

        function createPowerSpeedOption(data) {
            const series = [];
            const colors = ['#4a90e2', '#e74c3c', '#2ecc71', '#f39c12'];

            // 添加平衡线
            series.push({
                name: data.balance_curve.name,
                type: 'line',
                data: data.balance_curve.data,
                lineStyle: { color: '#000000', width: 2 },
                symbol: 'none',
                smooth: true
            });

            // 添加赛车曲线
            data.car_curves.forEach((car, index) => {
                const color = colors[index % colors.length];
                
                // 基础动力
                series.push({
                    name: `${car.name} 基础动力`,
                    type: 'line',
                    data: car.base_power,
                    lineStyle: { color: color, width: 2 },
                    symbol: 'circle',
                    symbolSize: 6
                });

                // 大喷动力
                series.push({
                    name: `${car.name} 大喷动力`,
                    type: 'line',
                    data: car.fuel_power,
                    lineStyle: { color: color, width: 2, type: 'dashed' },
                    symbol: 'circle',
                    symbolSize: 6
                });

                // cww动力
                series.push({
                    name: `${car.name} cww动力`,
                    type: 'line',
                    data: car.boost_power,
                    lineStyle: { color: color, width: 2, type: 'dotted' },
                    symbol: 'circle',
                    symbolSize: 6
                });
            });

            return {
                title: {
                    text: 'QQ飞车赛车动力-速度曲线图',
                    left: 'center',
                    textStyle: { fontSize: 18 }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `速度: ${params[0].value[0]} km/h<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1].toFixed(2)} N<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    bottom: 0,
                    type: 'scroll'
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%'
                },
                xAxis: {
                    type: 'value',
                    name: '速度 (km/h)',
                    nameLocation: 'middle',
                    nameGap: 30,
                    min: 0,
                    max: 400
                },
                yAxis: {
                    type: 'value',
                    name: '动力 (N)',
                    nameLocation: 'middle',
                    nameGap: 50
                },
                series: series
            };
        }

        function createSpeedTimeOption(data) {
            const series = [];
            const colors = ['#4a90e2', '#e74c3c', '#2ecc71', '#f39c12'];

            data.car_curves.forEach((car, index) => {
                const color = colors[index % colors.length];
                
                // 平跑速度
                series.push({
                    name: `${car.name} 平跑`,
                    type: 'line',
                    data: car.normal_speed,
                    lineStyle: { color: color, width: 2 },
                    symbol: 'none',
                    smooth: true
                });

                // 大喷速度
                series.push({
                    name: `${car.name} 大喷`,
                    type: 'line',
                    data: car.fuel_speed,
                    lineStyle: { color: color, width: 2, type: 'dashed' },
                    symbol: 'none',
                    smooth: true
                });

                // 超级喷速度
                series.push({
                    name: `${car.name} 超级喷`,
                    type: 'line',
                    data: car.super_speed,
                    lineStyle: { color: color, width: 2, type: 'dotted' },
                    symbol: 'none',
                    smooth: true
                });
            });

            return {
                title: {
                    text: 'QQ飞车赛车速度-时间曲线图',
                    left: 'center',
                    textStyle: { fontSize: 18 }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `时间: ${params[0].value[0].toFixed(2)} 秒<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1].toFixed(2)} km/h<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    bottom: 0,
                    type: 'scroll'
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%'
                },
                xAxis: {
                    type: 'value',
                    name: '时间 (秒)',
                    nameLocation: 'middle',
                    nameGap: 30,
                    min: 0,
                    max: 16
                },
                yAxis: {
                    type: 'value',
                    name: '速度 (km/h)',
                    nameLocation: 'middle',
                    nameGap: 50,
                    min: 0,
                    max: 350
                },
                series: series
            };
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initChart();
            loadTestData(); // 自动加载测试数据
        };

        // 窗口大小改变时重新调整图表
        window.onresize = function() {
            if (chart) {
                chart.resize();
            }
        };
    </script>
</body>
</html>
