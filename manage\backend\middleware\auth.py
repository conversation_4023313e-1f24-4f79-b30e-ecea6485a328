from django.http import JsonResponse
import jwt
from django.conf import settings
from functools import wraps

def authMiddleware(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        if not token:
            return JsonResponse({
                'success': False,
                'message': '未提供认证token'
            }, status=401)
        
        try:
            decoded = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            request.user = decoded
            return view_func(request, *args, **kwargs)
        except jwt.ExpiredSignatureError:
            return JsonResponse({
                'success': False,
                'message': 'token已过期'
            }, status=401)
        except jwt.InvalidTokenError:
            return JsonResponse({
                'success': False,
                'message': 'token无效'
            }, status=401)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': '认证失败',
                'error': str(e)
            }, status=401)
    
    return wrapper 