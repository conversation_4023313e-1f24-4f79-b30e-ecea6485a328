"""car_wiki URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import routers
from cars.views import CarViewSet
from routes.views import test_protected, user_list, update_user_permission, update_admin_status, update_password
from feedback.views import FeedbackViewSet

# API路由
router = routers.DefaultRouter()
router.register(r'cars', CarViewSet)
router.register(r'feedback', FeedbackViewSet)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('pets.urls')),  # 确保pets.urls被正确包含
    path('api/', include('cars.urls')),  # cars应用的URL
    path('api/', include(router.urls)),  # DRF路由
    path('api/auth/', include('routes.auth')),
    path('api/test/protected/', test_protected),
    path('api/users/', user_list, name='user-list'),
    path('api/users/<int:user_id>/permission/', update_user_permission, name='update-user-permission'),
    path('api/users/<int:user_id>/admin/', update_admin_status, name='update-admin-status'),
    path('api/users/<int:user_id>/password/', update_password, name='update-password'),
    path('api/admin/', include('sql_tool.urls')),
    path('api/', include('users.urls')),
    path('api/treasure/', include('treasure.urls')),  # 添加夺宝功能模块URL
    path('api/lottery/', include('lottery.urls')),  # 添加抽奖模块URL
    path('api/prize/', include('prize.urls')),  # 添加抽奖道具查询模块URL
]

# 添加媒体文件的URL配置
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
