#!/usr/bin/env python
"""
测试不支持的赛车等级错误处理
"""
import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from django.test import Client


def test_unsupported_car_level():
    """测试不支持的赛车等级"""
    print("=== 测试不支持的赛车等级错误处理 ===")
    
    client = Client()
    url = "/api/cars/generate-curves/"
    
    # 测试数据 - 使用不存在的赛车等级
    test_data = {
        "curve_type": "power_speed",
        "cars": [
            {
                "name": "测试赛车",
                "level": "S级",  # 这个等级在推进计算表中不存在
                "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 40
            }
        ]
    }
    
    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 400:
            data = response.json()
            print(f"✅ 正确返回错误: {data['message']}")
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            print(f"响应内容: {response.content.decode('utf-8')}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_supported_car_level():
    """测试支持的赛车等级"""
    print("\n=== 测试支持的赛车等级 ===")
    
    client = Client()
    url = "/api/cars/generate-curves/"
    
    # 测试数据 - 使用存在的赛车等级
    test_data = {
        "curve_type": "power_speed",
        "cars": [
            {
                "name": "测试赛车",
                "level": "T2",  # 这个等级在推进计算表中存在
                "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 40
            }
        ]
    }
    
    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 正确处理支持的等级: {data['success']}")
            print(f"赛车名称: {data['data']['car_curves'][0]['name']}")
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            print(f"响应内容: {response.content.decode('utf-8')}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_multiple_unsupported_levels():
    """测试多个不支持的赛车等级"""
    print("\n=== 测试多个不支持的赛车等级 ===")
    
    client = Client()
    url = "/api/cars/generate-curves/"
    
    # 测试数据 - 第一辆车支持，第二辆车不支持
    test_data = {
        "curve_type": "power_speed",
        "cars": [
            {
                "name": "支持的赛车",
                "level": "T2",  # 支持的等级
                "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 40
            },
            {
                "name": "不支持的赛车",
                "level": "SSS级",  # 不支持的等级
                "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 40
            }
        ]
    }
    
    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 400:
            data = response.json()
            print(f"✅ 正确返回错误: {data['message']}")
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            print(f"响应内容: {response.content.decode('utf-8')}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def main():
    """主函数"""
    print("开始测试不支持赛车等级的错误处理")
    print("=" * 50)
    
    # 测试不支持的等级
    test_unsupported_car_level()
    
    # 测试支持的等级
    test_supported_car_level()
    
    # 测试多个等级混合
    test_multiple_unsupported_levels()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
