from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.core.files.storage import default_storage
import uuid
import logging
import pandas as pd
from .models import Pet
from .serializers import PetSerializer
import os
from django.db import models, transaction
from django.http import HttpResponse
from django.conf import settings
import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
from django.db.models.functions import Cast
from django.db.models import IntegerField

logger = logging.getLogger(__name__)

class PetViewSet(viewsets.ModelViewSet):
    """
    宠物管理视图集
    
    提供标准的CRUD接口和以下扩展接口:
    - 导入Excel (POST /api/pets/import_excel/)
    - 导出Excel (GET /api/pets/export_excel/)
    - 上传图片 (POST /api/pets/upload_image/)
    - 批量上传图片 (POST /api/pets/upload_images_by_name/)
    """
    queryset = Pet.objects.all()
    serializer_class = PetSerializer
    permission_classes = [AllowAny]
    
    def get_queryset(self):
        """
        支持按以下字段筛选:
        - pet_id: 代码
        - name: 名称
        - form: 宠物形态
        - combat_power_min: 最小战斗力
        - combat_power_max: 最大战斗力
        - skill_keyword: 技能关键词（搜索基本技能和强化技能）
        - main_attribute: 主属性
        """
        # 使用 Cast 函数将 pet_id 转换为整数进行排序
        queryset = Pet.objects.all().annotate(
            pet_id_int=Cast('pet_id', IntegerField())
        ).order_by('-pet_id_int')  # 按转换后的整数降序排序
        
        # 获取查询参数
        pet_id = self.request.query_params.get('pet_id', '')
        name = self.request.query_params.get('name', '')
        form = self.request.query_params.get('form', '')
        combat_power_min = self.request.query_params.get('combat_power_min')
        combat_power_max = self.request.query_params.get('combat_power_max')
        skill_keyword = self.request.query_params.get('skill_keyword', '')
        main_attribute = self.request.query_params.get('main_attribute', '')
        
        # 应用筛选
        if pet_id:
            queryset = queryset.filter(pet_id__icontains=pet_id)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if form:
            queryset = queryset.filter(form=form)
        if combat_power_min:
            queryset = queryset.filter(combat_power__gte=combat_power_min)
        if combat_power_max:
            queryset = queryset.filter(combat_power__lte=combat_power_max)
        if skill_keyword:
            queryset = queryset.filter(
                models.Q(basic_skill__icontains=skill_keyword) |
                models.Q(enhanced_skill__icontains=skill_keyword)
            )
        if main_attribute:
            queryset = queryset.filter(main_attribute__icontains=main_attribute)
            
        return queryset

    @action(detail=False, methods=['post'])
    def import_excel(self, request):
        """
        导入Excel数据到宠物数据库
        
        请求参数:
        - file: Excel文件（必需）
        
        返回:
        - success: 是否成功
        - message: 处理结果信息
        - errors: 错误记录列表（如果有）
        
        说明：
        - 只更新Excel中非空的字段
        - 空单元格的字段将保持原值不变
        """
        try:
            excel_file = request.FILES.get('file')
            if not excel_file:
                return Response({
                    'error': '请上传Excel文件'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查文件类型
            if not excel_file.name.endswith(('.xlsx', '.xls')):
                return Response({
                    'error': '只支持.xlsx或.xls格式的Excel文件'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            
            # 验证必需的列是否存在
            required_columns = ['代码', '名称']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return Response({
                    'error': f'Excel文件缺少必需的列: {", ".join(missing_columns)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 列名映射
            column_mapping = {
                '代码': 'pet_id',
                '名称': 'name',
                '基本技能(满级数值)': 'basic_skill',
                '强化技能(满级数值)': 'enhanced_skill',
                '宠物形态': 'form',
                '战斗力': 'combat_power',
                '资质': 'aptitude',
                '主属性': 'main_attribute',
                '普通技能': 'normal_skill',
                '怒气技能': 'rage_skill'
            }
            
            success_count = 0
            error_records = []
            
            # 使用事务处理批量导入
            with transaction.atomic():
                for index, row in df.iterrows():
                    try:
                        # 准备数据，只包含非空值
                        pet_data = {}
                        for cn, en in column_mapping.items():
                            if cn in df.columns:
                                value = row[cn]
                                # 只处理非空值
                                if not pd.isna(value):
                                    # 处理数字类型
                                    if isinstance(value, (int, float)):
                                        value = str(int(value))
                                    else:
                                        value = str(value).strip()
                                        # 如果处理后的字符串为空，跳过该字段
                                        if not value:
                                            continue
                                    pet_data[en] = value
                        
                        # 检查必填字段
                        if 'pet_id' not in pet_data:
                            error_records.append({
                                'row': index + 2,
                                'error': '代码为必填项'
                            })
                            continue
                        
                        # 获取现有记录
                        try:
                            pet = Pet.objects.get(pet_id=pet_data['pet_id'])
                            # 只更新提供的字段
                            for field, value in pet_data.items():
                                if field != 'pet_id':  # 不更新pet_id
                                    setattr(pet, field, value)
                            pet.save()
                            success_count += 1
                        except Pet.DoesNotExist:
                            # 如果是新记录，确保包含必填字段
                            if 'name' not in pet_data:
                                error_records.append({
                                    'row': index + 2,
                                    'error': '新记录的名称为必填项'
                                })
                                continue
                            # 创建新记录
                            Pet.objects.create(**pet_data)
                            success_count += 1
                        
                    except Exception as e:
                        error_records.append({
                            'row': index + 2,
                            'error': str(e)
                        })
                        logger.error(f"导入第{index + 2}行数据失败: {str(e)}")
                        continue
            
            # 返回处理结果
            return Response({
                'success': True,
                'message': f'成功导入 {success_count} 条记录',
                'errors': error_records if error_records else None
            })
            
        except Exception as e:
            logger.error(f"Excel导入失败: {str(e)}")
            return Response({
                'error': '导入失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='export')
    def export_excel(self, request):
        """导出Excel文件"""
        try:
            # 获取模板文件路径
            template_path = os.path.join(settings.BASE_DIR, 'templates', 'pets_template.xlsx')
            if not os.path.exists(template_path):
                return Response({
                    'error': '模板文件不存在'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 获取所有宠物数据
            pets = self.get_queryset()
            
            # 转换为DataFrame
            data = []
            for pet in pets:
                data.append({
                    '代码': pet.pet_id,
                    '名称': pet.name,
                    '基本技能(满级数值)': pet.basic_skill,
                    '强化技能(满级数值)': pet.enhanced_skill,
                    '宠物形态': pet.form,
                    '战斗力': pet.combat_power,
                    '资质': pet.aptitude,
                    '主属性': pet.main_attribute,
                    '普通技能': pet.normal_skill,
                    '怒气技能': pet.rage_skill
                })
            
            df = pd.DataFrame(data)
            
            # 加载模板文件
            wb = openpyxl.load_workbook(template_path)
            ws = wb.active  # 获取第一个工作表
            
            # 清除示例数据（保留表头）
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.value = None
            
            # 写入实际数据
            for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=False), 2):
                for c_idx, value in enumerate(row, 1):
                    ws.cell(row=r_idx, column=c_idx, value=value)
            
            # 创建响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="pets.xlsx"'
            
            # 保存到响应
            wb.save(response)
            return response
            
        except Exception as e:
            logger.error(f"导出Excel失败: {str(e)}")
            return Response({
                'error': '导出失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'], url_path='upload')
    def upload_image(self, request, pk=None):
        """
        上传单个宠物图片
        
        @param request.FILES['image'] - 上传的图片文件
        @return 包含image_id的响应
        """
        try:
            pet = self.get_object()
            image_file = request.FILES.get('image')
            
            if not image_file:
                return Response({
                    'error': '未上传图片文件'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查文件大小
            if image_file.size > 2 * 1024 * 1024:  # 2MB
                return Response({
                    'error': '图片大小不能超过2MB'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查文件类型
            allowed_types = ['image/jpeg', 'image/png']
            if image_file.content_type not in allowed_types:
                return Response({
                    'error': '只支持JPG和PNG格式的图片'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 生成唯一的图片ID和文件名
            image_id = str(uuid.uuid4())
            ext = os.path.splitext(image_file.name)[1].lower()
            filename = f'{image_id}{ext}'
            filepath = os.path.join('pet_images', filename)
            
            # 保存图片文件
            path = default_storage.save(filepath, image_file)
            
            # 更新宠物记录的图片ID
            pet.image_id = image_id
            pet.save()
            
            return Response({
                'success': True,
                'image_id': image_id,
                'message': '图片上传成功'
            })
            
        except Exception as e:
            logger.error(f"上传图片失败: {str(e)}")
            return Response({
                'error': '上传失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def upload_images_by_name(self, request):
        """
        批量上传宠物图片
        
        图片文件名需要与名称或代码匹配
        @param request.FILES - 上传的多个图片文件
        @return 上传结果统计
        """
        try:
            # 检查请求中的文件
            logger.info(f"收到的请求: {request.FILES}")
            files = request.FILES.getlist('images')
            logger.info(f"获取到的文件列表: {files}")
            
            if not files:
                return Response({
                    'error': '未上传图片文件'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            success_count = 0
            error_records = []
            
            # 确保上传目录存在
            upload_dir = os.path.join(settings.MEDIA_ROOT, 'pet_images')
            os.makedirs(upload_dir, exist_ok=True)
            
            for image_file in files:
                try:
                    logger.info(f"处理文件: {image_file.name}")
                    
                    # 从文件名中提取宠物标识(去除扩展名和空格)
                    name_or_id = os.path.splitext(image_file.name)[0].strip()
                    logger.info(f"提取的标识: {name_or_id}")
                    
                    # 查找匹配的宠物记录
                    pet = Pet.objects.filter(
                        models.Q(name=name_or_id) | 
                        models.Q(pet_id=name_or_id)
                    ).first()
                    
                    if not pet:
                        error_records.append({
                            'file': image_file.name,
                            'error': f'找不到匹配的宠物记录: {name_or_id}'
                        })
                        continue
                    
                    logger.info(f"找到匹配的宠物: {pet.name} ({pet.pet_id})")
                    
                    # 检查文件大小和类型
                    if image_file.size > 2 * 1024 * 1024:
                        error_records.append({
                            'file': image_file.name,
                            'error': '图片大小超过2MB'
                        })
                        continue
                    
                    if image_file.content_type not in ['image/jpeg', 'image/png']:
                        error_records.append({
                            'file': image_file.name,
                            'error': f'不支持的图片格式: {image_file.content_type}'
                        })
                        continue
                    
                    # 生成图片ID和保存文件
                    image_id = str(uuid.uuid4())
                    ext = os.path.splitext(image_file.name)[1].lower()
                    filename = f'{image_id}{ext}'
                    filepath = os.path.join('pet_images', filename)
                    
                    try:
                        # 保存文件
                        saved_path = default_storage.save(filepath, image_file)
                        logger.info(f"文件保存到: {saved_path}")
                        
                        # 更新宠物记录
                        pet.image_id = image_id
                        pet.save()
                        
                        success_count += 1
                        logger.info(f"成功处理文件: {image_file.name}")
                        
                    except Exception as save_error:
                        logger.error(f"保存文件失败: {str(save_error)}")
                        error_records.append({
                            'file': image_file.name,
                            'error': f'保存文件失败: {str(save_error)}'
                        })
                    
                except Exception as e:
                    logger.error(f"处理文件 {image_file.name} 失败: {str(e)}")
                    error_records.append({
                        'file': image_file.name,
                        'error': str(e)
                    })
            
            response_data = {
                'success': True,
                'message': f'成功上传 {success_count} 张图片',
                'success_count': success_count,
                'errors': error_records
            }
            logger.info(f"上传完成，响应数据: {response_data}")
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"批量上传图片失败: {str(e)}", exc_info=True)
            return Response({
                'error': '上传失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_template(self, request):
        """获取Excel导入模板"""
        try:
            template_path = os.path.join(settings.BASE_DIR, 'templates', 'pets_template.xlsx')
            if not os.path.exists(template_path):
                return Response({
                    'error': '模板文件不存在'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 读取文件
            with open(template_path, 'rb') as f:
                response = HttpResponse(
                    f.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = 'attachment; filename=pets_template.xlsx'
                return response
            
        except Exception as e:
            logger.error(f"获取模板失败: {str(e)}")
            return Response({
                'error': '获取模板失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 