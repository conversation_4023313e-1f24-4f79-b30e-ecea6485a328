from django.http import JsonResponse
from django.shortcuts import render
from django.db import connection
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from datetime import datetime
import time
import logging

logger = logging.getLogger(__name__)

@csrf_exempt
def sql_tool(request):
    """SQL执行工具页面"""
    return render(request, 'sql_tool/index.html')

@csrf_exempt
@api_view(['POST'])
def execute_sql(request):
    """执行SQL的API接口"""
    try:
        sql = request.data.get('sql', '').strip()
        # 获取分页参数
        page_size = int(request.data.get('page_size', 10))  # 默认每页10条
        page_num = int(request.data.get('page_num', 1))    # 默认第1页
        
        if not sql:
            return JsonResponse({'error': 'SQL语句不能为空'}, status=400)
        
        if page_size < 1 or page_num < 1:
            return JsonResponse({'error': '分页参数不正确'}, status=400)
            
        # 记录详细的执行信息
        execution_log = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'user': 'anonymous',
            'sql': sql,
            'status': 'pending'
        }
        
        with connection.cursor() as cursor:
            start_time = time.time()
            cursor.execute(sql)
            execution_time = round(time.time() - start_time, 3)
            
            if sql.lower().startswith('select'):
                # 计算分页偏移量
                offset = (page_num - 1) * page_size
                # 修改SQL添加分页
                paginated_sql = f"{sql} LIMIT {page_size} OFFSET {offset}"
                
                # 先获取总记录数
                cursor.execute(f"SELECT COUNT(*) FROM ({sql}) as total_count")
                total_count = cursor.fetchone()[0]
                
                # 执行分页查询
                cursor.execute(paginated_sql)
                rows = cursor.fetchall()
                headers = [col[0] for col in cursor.description]
                
                # 计算总页数
                total_pages = (total_count + page_size - 1) // page_size
                
                execution_log.update({
                    'status': 'success',
                    'execution_time': f'{execution_time}s',
                    'affected_rows': total_count
                })
                
                logger.info(f"SQL执行成功 - 用户: anonymous, SQL: {sql}, "
                           f"耗时: {execution_time}s, 总记录数: {total_count}, "
                           f"当前页: {page_num}, 每页数量: {page_size}")
                
                return JsonResponse({
                    'success': True,
                    'data': {
                        'headers': headers,
                        'rows': rows,
                        'total_count': total_count,
                        'current_page': page_num,
                        'page_size': page_size,
                        'total_pages': total_pages,
                        'message': f'查询成功，共 {total_count} 条记录，当前第 {page_num}/{total_pages} 页'
                    },
                    'log': execution_log
                })
            else:
                affected = cursor.rowcount
                execution_log.update({
                    'status': 'success',
                    'execution_time': f'{execution_time}s',
                    'affected_rows': affected
                })
                
                logger.info(f"SQL执行成功 - 用户: anonymous, SQL: {sql}, "
                           f"耗时: {execution_time}s, 影响行数: {affected}")
                
                return JsonResponse({
                    'success': True,
                    'data': {
                        'message': f'执行成功，影响 {affected} 条记录'
                    },
                    'log': execution_log
                })
                
    except Exception as e:
        error_msg = str(e)
        execution_log = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'user': 'anonymous',
            'sql': sql,
            'status': 'error',
            'error_message': error_msg
        }
        
        logger.error(f"SQL执行失败 - 用户: anonymous, SQL: {sql}, 错误: {error_msg}")
        
        return JsonResponse({
            'error': f'执行失败：{error_msg}',
            'log': execution_log
        }, status=500) 