from .models import LotteryRecord, LotteryItem
from django.db.models import F, Sum, Count, Case, When, Value
from django.db import transaction

class RarityScoreCalculator:
    """
    稀有度得分计算器

    稀有度得分计算规则：
    1. 物品得分 = 1 / 物品抽取概率
    2. 总物品得分 = 所有传说级和金色物品的 (物品得分 × 获得数量) 之和
    3. 稀有率系数 = 1 / 总抽奖次数
    4. 稀有度得分 = 总物品得分 × 稀有率系数
    """

    @staticmethod
    def calculate_item_score(probability):
        """计算单个物品的得分"""
        return 1 / probability if probability > 0 else 0

    @staticmethod
    def calculate_total_score(items):
        """计算总物品得分"""
        total_score = 0
        for item in items:
            # 只计算传说级和金色物品
            if item['item']['background'] in ['legendary', 'gold']:
                item_score = RarityScoreCalculator.calculate_item_score(item['item']['probability'])
                total_score += item_score * item['count']
        return total_score

    @staticmethod
    def calculate_rarity_score(total_score, total_draws):
        """计算稀有度得分"""
        if total_draws <= 0:
            return 0
        rarity_coefficient = 1 / total_draws
        return total_score * rarity_coefficient


class LotteryManager:
    """抽奖数据管理器"""

    @staticmethod
    def get_rankings(activity_type, rank_type, limit=20, page=1, order='desc'):
        """
        获取排行榜数据

        参数:
            activity_type: 活动类型
            rank_type: 排行榜类型 ('rarity' 或 'draws')
            limit: 每页记录数
            page: 页码
            order: 排序方式 ('asc' 升序, 'desc' 降序)
        """
        offset = (page - 1) * limit

        # 获取排行榜数据
        queryset = LotteryRecord.objects.filter(activity_type=activity_type)
        total_count = queryset.count()

        # 根据排行榜类型和排序方式选择排序字段
        if rank_type == 'rarity':
            field_name = 'rarity_score'
        elif rank_type == 'draws':
            field_name = 'draw_count'
        else:
            raise ValueError("Invalid rank type")

        # 确定排序方向
        if order.lower() == 'asc':
            order_field = field_name  # 升序
            rank_start = total_count - offset  # 从总数开始倒数
        else:  # 默认降序
            order_field = f'-{field_name}'  # 降序
            rank_start = offset + 1  # 从1开始正数

        # 获取排名列表
        rank_list = list(queryset.order_by(order_field)[offset:offset+limit])

        # 为每个记录添加排名属性
        for i, record in enumerate(rank_list):
            if order.lower() == 'asc':
                # 升序排名：总数 - 偏移量 - 当前索引
                record.rank = total_count - offset - i
            else:
                # 降序排名：偏移量 + 当前索引 + 1
                record.rank = offset + i + 1

        return {
            'rank_list': rank_list,
            'total_count': total_count,
            'order': order,
            'last_update': queryset.order_by('-timestamp').first().timestamp if total_count > 0 else None
        }

    @staticmethod
    def get_user_rank(user, activity_type, rank_type, order='desc'):
        """
        获取用户排名

        参数:
            user: 用户对象
            activity_type: 活动类型
            rank_type: 排行榜类型 ('rarity' 或 'draws')
            order: 排序方式 ('asc' 升序, 'desc' 降序)
        """
        if rank_type == 'rarity':
            field_name = 'rarity_score'
        elif rank_type == 'draws':
            field_name = 'draw_count'
        else:
            raise ValueError("Invalid rank type")

        try:
            user_record = LotteryRecord.objects.get(user=user, activity_type=activity_type)
            value = getattr(user_record, field_name)

            # 根据排序方式计算排名
            if order.lower() == 'asc':
                # 升序排名：小于当前用户值的记录数 + 1
                filter_kwargs = {f"{field_name}__lt": value}
            else:
                # 降序排名：大于当前用户值的记录数 + 1
                filter_kwargs = {f"{field_name}__gt": value}

            rank = LotteryRecord.objects.filter(
                activity_type=activity_type,
                **filter_kwargs
            ).count() + 1

            user_record.rank = rank
            return user_record
        except LotteryRecord.DoesNotExist:
            return None

    @staticmethod
    @transaction.atomic
    def create_or_update_record(user, data):
        """创建或更新抽奖记录"""
        activity_type = data.get('activityType')
        statistics = data.get('statistics', [])
        total_draws = data.get('totalDraws', 0)
        has_legendary = data.get('hasLegendaryItems', False)
        has_gold = data.get('hasTotalGoldItems', False)

        # 计算稀有度得分
        total_score = RarityScoreCalculator.calculate_total_score(statistics)
        rarity_score = RarityScoreCalculator.calculate_rarity_score(total_score, total_draws)

        # 创建或更新记录
        record, created = LotteryRecord.objects.update_or_create(
            user=user,
            activity_type=activity_type,
            defaults={
                'rarity_score': rarity_score,
                'draw_count': total_draws,
                'has_legendary_items': has_legendary,
                'has_gold_items': has_gold
            }
        )

        # 清除旧物品记录
        if not created:
            LotteryItem.objects.filter(record=record).delete()

        # 创建新物品记录
        items = []
        for stat in statistics:
            item_data = stat['item']
            items.append(LotteryItem(
                record=record,
                item_id=item_data['id'],
                name=item_data['name'],
                background=item_data['background'],
                probability=item_data['probability'],
                count=stat['count']
            ))

        if items:
            LotteryItem.objects.bulk_create(items)

        return record

    @staticmethod
    @transaction.atomic
    def delete_record(user, activity_type):
        """删除用户的抽奖记录"""
        try:
            record = LotteryRecord.objects.get(user=user, activity_type=activity_type)

            # 先删除关联的物品记录
            LotteryItem.objects.filter(record=record).delete()

            # 再删除抽奖记录
            record.delete()
            return True
        except LotteryRecord.DoesNotExist:
            return False

    @staticmethod
    def get_record_detail(user, activity_type):
        """获取用户抽奖记录详情"""
        try:
            return LotteryRecord.objects.get(user=user, activity_type=activity_type)
        except LotteryRecord.DoesNotExist:
            return None