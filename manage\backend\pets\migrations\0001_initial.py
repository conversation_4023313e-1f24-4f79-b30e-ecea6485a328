# Generated by Django 3.2.23 on 2025-02-10 14:42

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Pet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pet_id', models.Char<PERSON>ield(max_length=50, unique=True, verbose_name='宠物代码')),
                ('name', models.CharField(max_length=100, verbose_name='宠物名称')),
                ('basic_skill', models.CharField(max_length=200, verbose_name='基本技能')),
                ('enhanced_skill', models.Char<PERSON><PERSON>(max_length=200, verbose_name='强化技能')),
                ('form', models.CharField(max_length=50, verbose_name='宠物形态')),
                ('combat_power', models.IntegerField(verbose_name='战斗力')),
                ('aptitude', models.Char<PERSON><PERSON>(max_length=50, verbose_name='资质')),
                ('main_attribute', models.CharField(max_length=50, verbose_name='主属性')),
                ('normal_skill', models.TextField(verbose_name='普通技能')),
                ('rage_skill', models.TextField(verbose_name='怒气技能')),
                ('image_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='图片ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '宠物',
                'verbose_name_plural': '宠物',
                'ordering': ['pet_id'],
            },
        ),
        migrations.AddIndex(
            model_name='pet',
            index=models.Index(fields=['pet_id'], name='pets_pet_pet_id_87a6b8_idx'),
        ),
        migrations.AddIndex(
            model_name='pet',
            index=models.Index(fields=['name'], name='pets_pet_name_7b5b83_idx'),
        ),
        migrations.AddIndex(
            model_name='pet',
            index=models.Index(fields=['form'], name='pets_pet_form_c1a133_idx'),
        ),
        migrations.AddIndex(
            model_name='pet',
            index=models.Index(fields=['combat_power'], name='pets_pet_combat__779ec2_idx'),
        ),
    ]
