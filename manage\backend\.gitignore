# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# 图片上传工具相关
upload_images.log
upload_images.log.*
*.spec
scripts/icon.ico
scripts/dist/
scripts/build/

# 临时文件
*.tmp
temp/
.DS_Store