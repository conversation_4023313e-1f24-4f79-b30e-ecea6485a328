# Generated by Django 3.2.23 on 2025-02-02 16:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0010_auto_20250202_2159'),
    ]

    operations = [
        migrations.CreateModel(
            name='SensitiveWord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('word', models.CharField(max_length=100, unique=True, verbose_name='敏感词')),
                ('source', models.CharField(choices=[('manual', '手动添加'), ('tencent', '腾讯云检测')], default='manual', max_length=20, verbose_name='来源')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True, verbose_name='是否启用')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '敏感词',
                'verbose_name_plural': '敏感词',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='sensitiveword',
            index=models.Index(fields=['word'], name='cars_sensit_word_a5531f_idx'),
        ),
        migrations.AddIndex(
            model_name='sensitiveword',
            index=models.Index(fields=['source'], name='cars_sensit_source_820ab5_idx'),
        ),
        migrations.AddIndex(
            model_name='sensitiveword',
            index=models.Index(fields=['is_active'], name='cars_sensit_is_acti_79ace3_idx'),
        ),
    ]
