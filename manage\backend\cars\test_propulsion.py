"""
推进计算与绘图功能测试用例
"""
from django.test import TestCase
from rest_framework.test import APITestCase
from rest_framework import status
from django.urls import reverse
from .models import Car, PropulsionLevelTable, CalculationHistory
import json


class PropulsionCalculationAPITest(APITestCase):
    """推进计算API测试"""

    def setUp(self):
        """测试前创建初始数据"""
        # 创建测试赛车
        self.car_data = {
            'car_id': 'A001',
            'name': '收割者',
            'level': 'A/M3/L3',
            'normal_speed': '120',
            'nitro_speed': '150',
            'drift_factor': '1.2',
            'friction_factor': '0.8',
            'weight': '1000',
            'low_speed_steering': '0.9',
            'high_speed_steering': '0.7',
            'engine_gear_1': '40',
            'engine_gear_2': '40',
            'engine_gear_3': '40',
            'engine_gear_4': '40',
            'engine_gear_5': '40',
            'engine_gear_6': '40',
            'original_propulsion_1': '4200',
            'original_propulsion_2': '4400',
            'original_propulsion_3': '5000',
            'original_propulsion_4': '5350',
            'original_propulsion_5': '6050',
            'original_propulsion_6': '6250',
            'original_propulsion_7': '6400',
            'fuel_intensity': '6290'
        }
        self.car = Car.objects.create(**self.car_data)

        # 创建推进计算表数据
        PropulsionLevelTable.objects.create(
            car_level='A/M3/L3',
            level_1_max=4709, level_2_max=None, level_3_max=None, level_4_max=6009,
            level_5_max=6500, level_6_max=6753, level_7_max=6890,
            level_1_diff=327, level_2_diff=342, level_3_diff=400, level_4_diff=407,
            level_5_diff=400, level_6_diff=362, level_7_diff=392,
            level_1_avg_increase=8.175, level_2_avg_increase=8.55, level_3_avg_increase=10,
            level_4_avg_increase=10.175, level_5_avg_increase=10, level_6_avg_increase=9.05,
            level_7_avg_increase=9.8
        )

    def test_search_cars(self):
        """测试赛车搜索接口"""
        url = '/api/cars/search/'

        # 测试搜索赛车名称
        response = self.client.get(url, {'q': '收割者'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['name'], '收割者')

        # 测试搜索赛车编号
        response = self.client.get(url, {'q': 'A001'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)

        # 测试空搜索关键词
        response = self.client.get(url, {'q': ''})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_calculate_propulsion_40(self):
        """测试推进40计算接口"""
        url = '/api/cars/calculate-propulsion/'

        # 测试正常计算
        data = {
            'car_id': 'A001',
            'user_id': 'test_user'
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        result_data = response.data['data']
        self.assertIn('car_info', result_data)
        self.assertIn('original_data', result_data)
        self.assertIn('propulsion_40_levels', result_data)
        self.assertIn('power_data', result_data)

        # 验证赛车信息
        self.assertEqual(result_data['car_info']['car_id'], 'A001')
        self.assertEqual(result_data['car_info']['name'], '收割者')
        self.assertEqual(result_data['car_info']['level'], 'A/M3/L3')

        # 验证推进40计算结果
        propulsion_40 = result_data['propulsion_40_levels']
        self.assertEqual(len(propulsion_40), 7)  # 7个推进档位

        # 验证动力数据
        power_data = result_data['power_data']
        self.assertIn('speed_anchors', power_data)
        self.assertIn('base_powers', power_data)
        self.assertIn('fuel_powers', power_data)
        self.assertIn('boost_powers', power_data)

        # 测试不存在的赛车
        data = {'car_id': 'NONEXISTENT'}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])

        # 测试缺少参数
        response = self.client.post(url, {}, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_generate_power_speed_chart(self):
        """测试动力-速度图表生成接口"""
        url = '/api/cars/generate-chart/'

        # 先计算推进40数据
        calc_url = '/api/cars/calculate-propulsion/'
        calc_data = {'car_id': 'A001'}
        calc_response = self.client.post(calc_url, calc_data, format='json')
        power_data = calc_response.data['data']['power_data']

        # 测试动力-速度图表生成
        data = {
            'chart_type': 'power_speed',
            'cars': [{
                'name': '收割者',
                'power_data': power_data
            }]
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        chart_config = response.data['data']['chart_config']
        self.assertIn('title', chart_config)
        self.assertIn('series', chart_config)
        self.assertIn('xAxis', chart_config)
        self.assertIn('yAxis', chart_config)

        # 验证图表配置
        self.assertEqual(chart_config['title']['text'], 'QQ飞车赛车动力-速度曲线图')
        self.assertGreater(len(chart_config['series']), 0)

    def test_generate_speed_time_chart(self):
        """测试速度-时间图表生成接口"""
        url = '/api/cars/generate-chart/'

        data = {
            'chart_type': 'speed_time',
            'cars': [{
                'name': '收割者',
                'power_data': {}
            }]
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        chart_config = response.data['data']['chart_config']
        self.assertIn('title', chart_config)
        self.assertIn('series', chart_config)
        self.assertEqual(chart_config['title']['text'], 'QQ飞车赛车速度-时间曲线图')

    def test_invalid_chart_type(self):
        """测试无效图表类型"""
        url = '/api/cars/generate-chart/'

        data = {
            'chart_type': 'invalid_type',
            'cars': []
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])


class PropulsionCalculationLogicTest(TestCase):
    """推进计算逻辑测试"""

    def setUp(self):
        """测试前创建数据"""
        PropulsionLevelTable.objects.create(
            car_level='A/M3/L3',
            level_1_max=4709, level_2_max=None, level_3_max=None, level_4_max=6009,
            level_5_max=6500, level_6_max=6753, level_7_max=6890,
            level_1_diff=327, level_2_diff=342, level_3_diff=400, level_4_diff=407,
            level_5_diff=400, level_6_diff=362, level_7_diff=392,
            level_1_avg_increase=8.175, level_2_avg_increase=8.55, level_3_avg_increase=10,
            level_4_avg_increase=10.175, level_5_avg_increase=10, level_6_avg_increase=9.05,
            level_7_avg_increase=9.8
        )

    def test_propulsion_40_calculation(self):
        """测试推进40计算逻辑"""
        from .propulsion_views import CalculatePropulsionView

        view = CalculatePropulsionView()
        original_levels = [4200, 4400, 5000, 5350, 6050, 6250, 6400]

        result = view.calculate_propulsion_40('A/M3/L3', original_levels)

        # 验证计算结果
        expected = [
            min(4200 + 327, 4709),  # 推进1: 有上限
            4400 + 342,             # 推进2: 无上限
            5000 + 400,             # 推进3: 无上限
            min(5350 + 407, 6009),  # 推进4: 有上限
            min(6050 + 400, 6500),  # 推进5: 有上限
            min(6250 + 362, 6753),  # 推进6: 有上限
            min(6400 + 392, 6890)   # 推进7: 有上限
        ]

        self.assertEqual(result, expected)

    def test_t2_skin_level_handling(self):
        """测试T2皮肤等级处理"""
        # 创建T2皮肤数据
        PropulsionLevelTable.objects.create(
            car_level='T2皮肤',
            level_1_max=4764, level_2_max=None, level_3_max=None, level_4_max=5900,
            level_5_max=None, level_6_max=6753, level_7_max=6890,
            level_1_diff=331, level_2_diff=350, level_3_diff=400, level_4_diff=400,
            level_5_diff=400, level_6_diff=362, level_7_diff=392,
            level_1_avg_increase=8.275, level_2_avg_increase=8.75, level_3_avg_increase=10,
            level_4_avg_increase=10, level_5_avg_increase=10, level_6_avg_increase=9.05,
            level_7_avg_increase=9.8
        )

        from .propulsion_views import CalculatePropulsionView

        view = CalculatePropulsionView()
        original_levels = [4200, 4400, 5000, 5350, 6050, 6250, 6400]

        # 测试T2(xxx)格式的等级
        result = view.calculate_propulsion_40('T2(雷诺传奇)', original_levels)

        # 应该使用T2皮肤的计算数据
        expected = [
            min(4200 + 331, 4764),  # 使用T2皮肤的差值和上限
            4400 + 350,
            5000 + 400,
            min(5350 + 400, 5900),
            6050 + 400,
            min(6250 + 362, 6753),
            min(6400 + 392, 6890)
        ]

        self.assertEqual(result, expected)


class CalculationHistoryTest(TestCase):
    """计算历史记录测试"""

    def test_json_handling(self):
        """测试JSON数据处理"""
        history = CalculationHistory(user_id='test_user')

        # 测试设置和获取赛车数据
        car_data = {
            'car_id': 'A001',
            'name': '收割者',
            'level': 'A/M3/L3'
        }
        history.set_car_data(car_data)
        retrieved_data = history.get_car_data()
        self.assertEqual(retrieved_data, car_data)

        # 测试设置和获取计算结果
        result_data = {
            'propulsion_40_levels': [4527, 4742, 5400, 5757, 6450, 6612, 6792],
            'power_data': {'speed_anchors': [0, 76.5, 87.21]}
        }
        history.set_calculation_result(result_data)
        retrieved_result = history.get_calculation_result()
        self.assertEqual(retrieved_result, result_data)

        # 测试空数据处理
        empty_history = CalculationHistory()
        self.assertEqual(empty_history.get_car_data(), {})
        self.assertEqual(empty_history.get_calculation_result(), {})
