# Generated by Django 3.2.23 on 2025-01-19 08:06

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Feedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('feature', '功能建议'), ('bug', 'Bug反馈'), ('other', '其他')], max_length=20, verbose_name='反馈类型')),
                ('content', models.TextField(verbose_name='反馈内容')),
                ('device_info', models.CharField(blank=True, help_text='用户设备信息，主要用于bug反馈', max_length=200, verbose_name='设备信息')),
                ('contact', models.CharField(blank=True, max_length=100, verbose_name='联系方式')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已处理')], default='pending', max_length=20, verbose_name='处理状态')),
                ('reply', models.TextField(blank=True, verbose_name='回复内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '用户反馈',
                'verbose_name_plural': '用户反馈',
                'db_table': 'feedback',
                'ordering': ['-created_at'],
            },
        ),
    ]
