# Generated by Django 3.2.23 on 2025-01-01 12:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Car',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('car_id', models.CharField(max_length=50, unique=True, verbose_name='赛车编号')),
                ('name', models.CharField(max_length=100, verbose_name='赛车名称')),
                ('level', models.CharField(choices=[('A', 'A级'), ('S', 'S级'), ('SS', 'SS级')], max_length=2, verbose_name='赛车级别')),
                ('normal_speed', models.FloatField(verbose_name='平跑极速')),
                ('nitro_speed', models.FloatField(verbose_name='氮气极速')),
                ('drift_factor', models.FloatField(verbose_name='飘逸系数')),
                ('friction_factor', models.FloatField(verbose_name='摩擦系数')),
                ('weight', models.FloatField(verbose_name='车重')),
                ('low_speed_steering', models.FloatField(verbose_name='低速转向')),
                ('high_speed_steering', models.FloatField(verbose_name='高速转向')),
                ('image', models.ImageField(blank=True, null=True, upload_to='car_images/', verbose_name='赛车图片')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '赛车',
                'verbose_name_plural': '赛车',
                'ordering': ['car_id'],
            },
        ),
    ]
