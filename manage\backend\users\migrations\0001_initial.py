# Generated by Django 3.2.23 on 2025-01-28 17:11

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='WechatUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('openid', models.CharField(max_length=100, unique=True, verbose_name='OpenID')),
                ('nickname', models.CharField(blank=True, max_length=100, null=True, verbose_name='昵称')),
                ('avatar_url', models.URLField(blank=True, max_length=500, null=True, verbose_name='头像')),
                ('gender', models.IntegerField(blank=True, null=True, verbose_name='性别')),
                ('country', models.CharField(blank=True, max_length=100, null=True, verbose_name='国家')),
                ('province', models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='省份')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='城市')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否有效')),
                ('last_login', models.DateTimeField(default=django.utils.timezone.now, verbose_name='最后登录时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '微信用户',
                'verbose_name_plural': '微信用户',
                'ordering': ['-last_login'],
            },
        ),
    ]
