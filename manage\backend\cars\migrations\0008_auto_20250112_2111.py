# Generated by Django 3.2.23 on 2025-01-12 13:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0007_auto_20250108_2303'),
    ]

    operations = [
        migrations.AddField(
            model_name='car',
            name='engine_gear_1',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='引擎1挡'),
        ),
        migrations.AddField(
            model_name='car',
            name='engine_gear_2',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='引擎2挡'),
        ),
        migrations.AddField(
            model_name='car',
            name='engine_gear_3',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='引擎3挡'),
        ),
        migrations.AddField(
            model_name='car',
            name='engine_gear_4',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True, verbose_name='引擎4挡'),
        ),
        migrations.AddField(
            model_name='car',
            name='engine_gear_5',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='引擎5挡'),
        ),
        migrations.AddField(
            model_name='car',
            name='engine_gear_6',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='引擎6挡'),
        ),
        migrations.AddField(
            model_name='car',
            name='original_gear_7',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进7挡'),
        ),
        migrations.AddField(
            model_name='car',
            name='suspension',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='悬挂'),
        ),
        migrations.AlterField(
            model_name='car',
            name='drift_factor',
            field=models.CharField(max_length=50, verbose_name='漂移速率'),
        ),
        migrations.AlterField(
            model_name='car',
            name='high_speed_steering',
            field=models.CharField(max_length=50, verbose_name='最小转向'),
        ),
        migrations.AlterField(
            model_name='car',
            name='level',
            field=models.CharField(max_length=20, verbose_name='赛车级别'),
        ),
        migrations.AlterField(
            model_name='car',
            name='low_speed_steering',
            field=models.CharField(max_length=50, verbose_name='最大转向'),
        ),
    ]
