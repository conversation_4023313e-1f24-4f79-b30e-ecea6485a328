from rest_framework import serializers
from .models import Pet

class PetSerializer(serializers.ModelSerializer):
    """宠物信息序列化器"""
    
    def validate_combat_power(self, value):
        """
        验证战斗力字段
        - 如果是数字字符串，确保不为负数
        - 如果是文本描述（如"暂无数据"），直接通过
        """
        if value is None:
            return value
            
        # 尝试转换为数字
        try:
            num_value = int(value)
            if num_value < 0:
                raise serializers.ValidationError("战斗力不能为负数")
            return str(num_value)  # 转回字符串
        except ValueError:
            # 如果无法转换为数字，说明是文本描述，直接返回
            return value

    class Meta:
        model = Pet
        fields = [
            'id', 'pet_id', 'name', 'basic_skill', 'enhanced_skill',
            'form', 'combat_power', 'aptitude', 'main_attribute',
            'normal_skill', 'rage_skill', 'image_id',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at'] 