"""
初始化推进档位计算表数据的管理命令

使用方法:
python manage.py init_propulsion_data
python manage.py init_propulsion_data --force  # 强制更新已存在的数据
"""
from django.core.management.base import BaseCommand
from cars.models import PropulsionLevelTable


class Command(BaseCommand):
    help = '初始化推进档位计算表数据 (基于官方数据)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制更新已存在的数据',
        )

    def handle(self, *args, **options):
        """执行初始化数据"""
        self.stdout.write('🚀 开始初始化推进档位计算表数据...')
        self.stdout.write('📋 数据来源: 官方推进档位上限和平均提升数值')

        # 根据官方最新数据进行初始化
        propulsion_data = [
            {
                'car_level': 'C/M1',
                'level_1_max': None, 'level_2_max': 4381, 'level_3_max': 4959, 'level_4_max': 5462,
                'level_5_max': None, 'level_6_max': None, 'level_7_max': 6244,
                'level_1_diff': 304, 'level_2_diff': 304, 'level_3_diff': 367, 'level_4_diff': 370,
                'level_5_diff': 366, 'level_6_diff': 334, 'level_7_diff': 355,
                'level_1_avg_increase': 7.6, 'level_2_avg_increase': 7.6, 'level_3_avg_increase': 9.175,
                'level_4_avg_increase': 9.25, 'level_5_avg_increase': 9.15, 'level_6_avg_increase': 8.35,
                'level_7_avg_increase': 8.875
            },
            {
                'car_level': 'B/M2/L2/R',
                'level_1_max': 4599, 'level_2_max': 4671, 'level_3_max': 5179, 'level_4_max': 5752,
                'level_5_max': None, 'level_6_max': None, 'level_7_max': 6621,
                'level_1_diff': 319, 'level_2_diff': 324, 'level_3_diff': 383, 'level_4_diff': 389,
                'level_5_diff': 383, 'level_6_diff': 342, 'level_7_diff': 377,
                'level_1_avg_increase': 7.975, 'level_2_avg_increase': 8.1, 'level_3_avg_increase': 9.575,
                'level_4_avg_increase': 9.725, 'level_5_avg_increase': 9.575, 'level_6_avg_increase': 8.55,
                'level_7_avg_increase': 9.425
            },
            {
                'car_level': 'T1',
                'level_1_max': 4682, 'level_2_max': 4736, 'level_3_max': None, 'level_4_max': 5900,
                'level_5_max': None, 'level_6_max': None, 'level_7_max': 6809,
                'level_1_diff': 325, 'level_2_diff': 329, 'level_3_diff': 397, 'level_4_diff': 400,
                'level_5_diff': 396, 'level_6_diff': 360, 'level_7_diff': 388,
                'level_1_avg_increase': 8.125, 'level_2_avg_increase': 8.225, 'level_3_avg_increase': 9.925,
                'level_4_avg_increase': 10, 'level_5_avg_increase': 9.9, 'level_6_avg_increase': 9,
                'level_7_avg_increase': 9.7
            },
            {
                'car_level': 'A/M3/L3',
                'level_1_max': 4709, 'level_2_max': None, 'level_3_max': None, 'level_4_max': 6009,
                'level_5_max': 6500, 'level_6_max': 6753, 'level_7_max': 6890,
                'level_1_diff': 327, 'level_2_diff': 342, 'level_3_diff': 400, 'level_4_diff': 407,
                'level_5_diff': 400, 'level_6_diff': 362, 'level_7_diff': 392,
                'level_1_avg_increase': 8.175, 'level_2_avg_increase': 8.55, 'level_3_avg_increase': 10,
                'level_4_avg_increase': 10.175, 'level_5_avg_increase': 10, 'level_6_avg_increase': 9.05,
                'level_7_avg_increase': 9.8
            },
            {
                'car_level': 'T2',
                'level_1_max': None, 'level_2_max': 5038, 'level_3_max': None, 'level_4_max': None,
                'level_5_max': None, 'level_6_max': 6753, 'level_7_max': 6890,
                'level_1_diff': 331, 'level_2_diff': 350, 'level_3_diff': 400, 'level_4_diff': 400,
                'level_5_diff': 400, 'level_6_diff': 362, 'level_7_diff': 392,
                'level_1_avg_increase': 8.275, 'level_2_avg_increase': 8.75, 'level_3_avg_increase': 10,
                'level_4_avg_increase': 10, 'level_5_avg_increase': 10, 'level_6_avg_increase': 9.05,
                'level_7_avg_increase': 9.8
            },
            {
                'car_level': 'T2皮肤',
                'level_1_max': 4764, 'level_2_max': None, 'level_3_max': None, 'level_4_max': 5900,
                'level_5_max': None, 'level_6_max': 6753, 'level_7_max': 6890,
                'level_1_diff': 331, 'level_2_diff': 350, 'level_3_diff': 400, 'level_4_diff': 400,
                'level_5_diff': 400, 'level_6_diff': 362, 'level_7_diff': 392,
                'level_1_avg_increase': 8.275, 'level_2_avg_increase': 8.75, 'level_3_avg_increase': 10,
                'level_4_avg_increase': 10, 'level_5_avg_increase': 10, 'level_6_avg_increase': 9.05,
                'level_7_avg_increase': 9.8
            }
        ]
        
        created_count = 0
        updated_count = 0

        for data in propulsion_data:
            car_level = data['car_level']
            try:
                if options['force']:
                    # 强制更新模式
                    obj, created = PropulsionLevelTable.objects.update_or_create(
                        car_level=car_level,
                        defaults=data
                    )
                else:
                    # 只创建不存在的记录
                    obj, created = PropulsionLevelTable.objects.get_or_create(
                        car_level=car_level,
                        defaults=data
                    )

                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ 创建 {car_level} 等级数据')
                    )
                else:
                    if options['force']:
                        updated_count += 1
                        self.stdout.write(
                            self.style.WARNING(f'🔄 更新 {car_level} 等级数据')
                        )
                    else:
                        self.stdout.write(
                            self.style.NOTICE(f'⏭️  跳过 {car_level} 等级数据 (已存在)')
                        )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ 处理 {car_level} 等级时出错: {str(e)}')
                )

        self.stdout.write(f"\n📊 初始化完成:")
        self.stdout.write(f"   - 创建: {created_count} 条记录")
        if options['force']:
            self.stdout.write(f"   - 更新: {updated_count} 条记录")
        self.stdout.write(f"   - 总计: {created_count + updated_count} 条记录")

        # 验证数据
        self.stdout.write(f"\n🔍 验证数据:")
        total_records = PropulsionLevelTable.objects.count()
        self.stdout.write(f"   - 数据库中共有 {total_records} 条推进计算表记录")

        for level in ['C/M1', 'B/M2/L2/R', 'T1', 'A/M3/L3', 'T2', 'T2皮肤']:
            exists = PropulsionLevelTable.objects.filter(car_level=level).exists()
            status = "✅" if exists else "❌"
            self.stdout.write(f"   - {level}: {status}")

        # 显示数据详情
        self.stdout.write(f"\n📋 数据详情:")
        for record in PropulsionLevelTable.objects.all().order_by('id'):
            self.stdout.write(f"   - {record.car_level}:")
            max_values = [getattr(record, f'level_{i}_max') for i in range(1, 8)]
            max_display = [str(v) if v is not None else '无上限' for v in max_values]
            self.stdout.write(f"     推进上限: {max_display}")
            avg_values = [getattr(record, f'level_{i}_avg_increase') for i in range(1, 8)]
            self.stdout.write(f"     平均提升: {avg_values}")

        self.stdout.write(
            self.style.SUCCESS('\n🎉 推进计算表数据初始化完成!')
        )

        if not options['force']:
            self.stdout.write(
                self.style.NOTICE('\n💡 提示: 使用 --force 参数可以强制更新已存在的数据')
            )
