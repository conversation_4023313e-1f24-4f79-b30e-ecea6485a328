from django.contrib import admin
from .models import LotteryRecord, LotteryItem

class LotteryItemInline(admin.TabularInline):
    """
    内联显示抽奖物品记录
    """
    model = LotteryItem
    extra = 0
    fields = ('item_id', 'name', 'background', 'probability', 'count')
    readonly_fields = ('item_id', 'name', 'background', 'probability', 'count')
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False

@admin.register(LotteryRecord)
class LotteryRecordAdmin(admin.ModelAdmin):
    """
    抽奖记录管理界面
    """
    list_display = ('id', 'user', 'activity_type', 'rarity_score', 'draw_count', 
                    'has_legendary_items', 'has_gold_items', 'timestamp')
    list_filter = ('activity_type', 'has_legendary_items', 'has_gold_items')
    search_fields = ('user__nickname', 'user__openid')
    readonly_fields = ('rarity_score', 'draw_count', 'timestamp')
    inlines = [LotteryItemInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'activity_type', 'timestamp')
        }),
        ('统计数据', {
            'fields': ('rarity_score', 'draw_count', 'has_legendary_items', 'has_gold_items')
        }),
    )
    
    def has_add_permission(self, request):
        return False  # 禁止手动添加记录，只能通过API创建
