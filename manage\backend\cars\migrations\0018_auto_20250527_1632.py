# Generated by Django 3.2.23 on 2025-05-27 08:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0017_car_gem_slots'),
    ]

    operations = [
        migrations.CreateModel(
            name='CalculationHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=100, verbose_name='用户ID')),
                ('car_data', models.TextField(verbose_name='赛车数据')),
                ('calculation_result', models.TextField(verbose_name='计算结果')),
                ('chart_type', models.CharField(blank=True, choices=[('power_speed', '动力-速度曲线'), ('speed_time', '速度-时间曲线')], max_length=20, null=True, verbose_name='图表类型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '计算历史记录',
                'verbose_name_plural': '计算历史记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PropulsionLevelTable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('car_level', models.CharField(max_length=20, unique=True, verbose_name='赛车等级')),
                ('level_1_max', models.IntegerField(blank=True, null=True, verbose_name='推进1上限')),
                ('level_2_max', models.IntegerField(blank=True, null=True, verbose_name='推进2上限')),
                ('level_3_max', models.IntegerField(blank=True, null=True, verbose_name='推进3上限')),
                ('level_4_max', models.IntegerField(blank=True, null=True, verbose_name='推进4上限')),
                ('level_5_max', models.IntegerField(blank=True, null=True, verbose_name='推进5上限')),
                ('level_6_max', models.IntegerField(blank=True, null=True, verbose_name='推进6上限')),
                ('level_7_max', models.IntegerField(blank=True, null=True, verbose_name='推进7上限')),
                ('level_1_diff', models.IntegerField(verbose_name='推进1差值')),
                ('level_2_diff', models.IntegerField(verbose_name='推进2差值')),
                ('level_3_diff', models.IntegerField(verbose_name='推进3差值')),
                ('level_4_diff', models.IntegerField(verbose_name='推进4差值')),
                ('level_5_diff', models.IntegerField(verbose_name='推进5差值')),
                ('level_6_diff', models.IntegerField(verbose_name='推进6差值')),
                ('level_7_diff', models.IntegerField(verbose_name='推进7差值')),
                ('level_1_avg_increase', models.FloatField(verbose_name='推进1平均提升')),
                ('level_2_avg_increase', models.FloatField(verbose_name='推进2平均提升')),
                ('level_3_avg_increase', models.FloatField(verbose_name='推进3平均提升')),
                ('level_4_avg_increase', models.FloatField(verbose_name='推进4平均提升')),
                ('level_5_avg_increase', models.FloatField(verbose_name='推进5平均提升')),
                ('level_6_avg_increase', models.FloatField(verbose_name='推进6平均提升')),
                ('level_7_avg_increase', models.FloatField(verbose_name='推进7平均提升')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '推进档位计算表',
                'verbose_name_plural': '推进档位计算表',
                'ordering': ['car_level'],
            },
        ),
        migrations.AddField(
            model_name='car',
            name='original_propulsion_1',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进1档'),
        ),
        migrations.AddField(
            model_name='car',
            name='original_propulsion_2',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进2档'),
        ),
        migrations.AddField(
            model_name='car',
            name='original_propulsion_3',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进3档'),
        ),
        migrations.AddField(
            model_name='car',
            name='original_propulsion_4',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进4档'),
        ),
        migrations.AddField(
            model_name='car',
            name='original_propulsion_5',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进5档'),
        ),
        migrations.AddField(
            model_name='car',
            name='original_propulsion_6',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装推进6档'),
        ),
    ]
