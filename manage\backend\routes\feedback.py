from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser, AllowAny
from feedback.models import Feedback
from feedback.serializers import FeedbackCreateSerializer, FeedbackListSerializer

class FeedbackViewSet(viewsets.ModelViewSet):
    """
    反馈管理视图集
    
    普通用户接口:
    - 提交反馈 (POST /api/feedback/)
    
    管理员接口:
    - 获取所有反馈列表 (GET /api/feedback/)
    - 更新反馈状态 (PUT /api/feedback/{id}/)
    - 回复反馈 (POST /api/feedback/{id}/reply/)
    """
    queryset = Feedback.objects.all()
    
    def get_serializer_class(self):
        if self.action == 'create':
            return FeedbackCreateSerializer
        return FeedbackListSerializer
    
    def get_permissions(self):
        """
        创建反馈时允许所有用户访问
        其他操作需要管理员权限
        """
        if self.action == 'create':
            return [AllowAny()]
        return [IsAdminUser()]
    
    @action(detail=True, methods=['post'])
    def reply(self, request, pk=None):
        """管理员回复反馈"""
        feedback = self.get_object()
        reply = request.data.get('reply')
        if not reply:
            return Response({'error': '回复内容不能为空'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        feedback.reply = reply
        feedback.status = 'completed'
        feedback.save()
        
        serializer = self.get_serializer(feedback)
        return Response(serializer.data) 