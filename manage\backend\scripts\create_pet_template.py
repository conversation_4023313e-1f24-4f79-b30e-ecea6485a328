import pandas as pd
import os

def create_pet_template():
    # 创建示例数据
    data = {
        '宠物代码': ['P001', 'P002'],
        '宠物名称': ['示例宠物1', '示例宠物2'],
        '基本技能': ['基础攻击+100', '基础防御+100'],
        '强化技能': ['强化攻击+200', '强化防御+200'],
        '宠物形态': ['攻击型', '防御型'],
        '战斗力': [1000, 1200],
        '资质': ['S', 'A'],
        '主属性': ['攻击', '防御'],
        '普通技能': ['普通技能描述1', '普通技能描述2'],
        '怒气技能': ['怒气技能描述1', '怒气技能描述2']
    }
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 确保templates目录存在
    os.makedirs('templates', exist_ok=True)
    
    # 保存为Excel模板
    template_path = 'templates/pets_template.xlsx'
    
    # 使用ExcelWriter以便于设置格式
    with pd.ExcelWriter(template_path, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='宠物信息')
        
        # 获取workbook和worksheet对象
        workbook = writer.book
        worksheet = writer.sheets['宠物信息']
        
        # 设置列宽
        worksheet.set_column('A:B', 15)  # 宠物代码和名称列
        worksheet.set_column('C:D', 20)  # 技能列
        worksheet.set_column('E:H', 12)  # 形态、战斗力、资质、主属性列
        worksheet.set_column('I:J', 30)  # 普通技能和怒气技能列
        
        # 创建格式
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D9E1F2',
            'border': 1
        })
        
        # 设置表头格式
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # 添加说明sheet
        instruction_sheet = workbook.add_worksheet('填写说明')
        instructions = [
            ['字段', '说明', '格式要求'],
            ['宠物代码', '宠物的唯一标识符', '必填，字符串，如：P001'],
            ['宠物名称', '宠物的名称', '必填，字符串'],
            ['基本技能', '宠物的基本技能(满级数值)', '必填，字符串'],
            ['强化技能', '宠物的强化技能(满级数值)', '必填，字符串'],
            ['宠物形态', '宠物的形态类型', '必填，字符串'],
            ['战斗力', '宠物的战斗力数值', '必填，正整数'],
            ['资质', '宠物的资质等级', '必填，字符串'],
            ['主属性', '宠物的主要属性', '必填，字符串'],
            ['普通技能', '宠物的普通技能描述', '必填，文本'],
            ['怒气技能', '宠物的怒气技能描述', '必填，文本'],
        ]
        
        # 设置说明sheet的格式
        instruction_format = workbook.add_format({
            'text_wrap': True,
            'valign': 'top',
            'border': 1
        })
        
        # 写入说明内容
        for row_num, row_data in enumerate(instructions):
            for col_num, value in enumerate(row_data):
                instruction_sheet.write(row_num, col_num, value, 
                    header_format if row_num == 0 else instruction_format)
        
        # 设置说明sheet的列宽
        instruction_sheet.set_column('A:A', 15)
        instruction_sheet.set_column('B:B', 30)
        instruction_sheet.set_column('C:C', 20)

    print(f"模板文件已创建: {template_path}")

if __name__ == '__main__':
    create_pet_template() 