from django.shortcuts import render
import jwt
import requests
from datetime import datetime, timedelta
from django.conf import settings
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from .models import WechatUser
import logging
from django.utils import timezone
import os
import base64
import hashlib
import json
import time
import urllib.parse
import urllib.request
import warnings

logger = logging.getLogger(__name__)

def generate_token(openid):
    """生成JWT token"""
    payload = {
        'openid': openid,
        'exp': datetime.utcnow() + timedelta(days=30),  # token 30天过期
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """
    微信小程序登录
    
    @body {
        code: "wx.login获取的code"
    }
    @return {
        success: true,
        openid: "用户openid",
        token: "JWT token"
    }
    """
    try:
        code = request.data.get('code')
        if not code:
            logger.warning("请求中缺少code参数")
            return Response({
                'success': False,
                'message': '缺少登录凭证'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 调用微信登录凭证校验接口
        wx_api_url = 'https://api.weixin.qq.com/sns/jscode2session'
        params = {
            'appid': 'wx5f17c4ba385491af',  # 直接使用AppID
            'secret': 'fa7f887758bff060eeb2797891bbe208',  # 直接使用AppSecret
            'js_code': code,
            'grant_type': 'authorization_code'
        }
        
        logger.info(f"正在请求微信接口，参数: {params}")
        response = requests.get(wx_api_url, params=params)
        wx_data = response.json()
        
        logger.info(f"微信接口返回数据: {wx_data}")  # 添加日志
        
        if 'errcode' in wx_data:
            logger.error(f"微信接口返回错误: {wx_data}")
            return Response({
                'success': False,
                'message': '微信登录失败',
                'error': wx_data
            }, status=status.HTTP_400_BAD_REQUEST)
            
        openid = wx_data.get('openid')
        if not openid:
            logger.error(f"微信接口返回数据缺少openid: {wx_data}")
            return Response({
                'success': False,
                'message': '获取openid失败'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 获取或创建用户
        user, created = WechatUser.objects.get_or_create(openid=openid)
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])
        
        # 生成JWT token
        token = generate_token(openid)
        
        logger.info(f"用户登录成功: {openid}")
        return Response({
            'success': True,
            'openid': openid,
            'token': token,
            'is_vip': user.is_vip,
            'is_valid_vip': user.is_valid_vip,
            'vip_expire_at': user.vip_expire_at
        })
        
    except Exception as e:
        logger.error(f"登录过程发生错误: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': '登录失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def update_user_info(request):
    """
    更新用户信息 (已弃用)
    
    该接口已标记为弃用状态，请使用get_wechat_user_info接口替代。
    
    @header {
        Authorization: "Bearer token"
    }
    @body {
        openid: "用户openid",
        nickname: "用户昵称",
        avatar_url: "头像URL",
        gender: 性别,
        country: "国家",
        province: "省份",
        city: "城市"
    }
    @return {
        success: true
    }
    """
    warnings.warn(
        "The update_user_info endpoint is deprecated and will be removed in the future. "
        "Please use get_wechat_user_info instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    try:
        # 验证token中的openid与请求中的openid是否一致
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return Response({
                'success': False,
                'message': '无效的认证头'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            token_openid = payload['openid']
        except jwt.ExpiredSignatureError:
            return Response({
                'success': False,
                'message': '登录已过期'
            }, status=status.HTTP_401_UNAUTHORIZED)
        except jwt.InvalidTokenError:
            return Response({
                'success': False,
                'message': '无效的token'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        request_openid = request.data.get('openid')
        if not request_openid or request_openid != token_openid:
            return Response({
                'success': False,
                'message': '无效的openid'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            user = WechatUser.objects.get(openid=request_openid)
        except WechatUser.DoesNotExist:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
            
        # 更新用户信息
        update_fields = ['updated_at']
        for field in ['nickname', 'avatar_url', 'gender', 'country', 'province', 'city']:
            if field in request.data:
                setattr(user, field, request.data[field])
                update_fields.append(field)
                
        user.save(update_fields=update_fields)
        
        return Response({
            'success': True
        })
        
    except Exception as e:
        logger.error(f"Error updating user info: {str(e)}")
        return Response({
            'success': False,
            'message': '更新失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def set_vip_status(request):
    """
    设置用户VIP状态
    
    @header {
        Authorization: "Bearer token"
    }
    @body {
        openid: "用户openid",
        is_vip: true/false,
        vip_expire_at: "VIP到期时间"(可选，如果is_vip为true则必填)
    }
    @return {
        success: true,
        data: {
            is_vip: true/false,
            is_valid_vip: true/false,
            vip_expire_at: "VIP到期时间"
        }
    }
    """
    try:
        # 验证token
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return Response({
                'success': False,
                'message': '无效的认证头'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            token_openid = payload['openid']
        except jwt.ExpiredSignatureError:
            return Response({
                'success': False,
                'message': '登录已过期'
            }, status=status.HTTP_401_UNAUTHORIZED)
        except jwt.InvalidTokenError:
            return Response({
                'success': False,
                'message': '无效的token'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # 验证请求数据
        request_openid = request.data.get('openid')
        is_vip = request.data.get('is_vip')
        vip_expire_at = request.data.get('vip_expire_at')
        
        if not request_openid:
            return Response({
                'success': False,
                'message': '缺少openid参数'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        if is_vip is None:
            return Response({
                'success': False,
                'message': '缺少is_vip参数'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        if is_vip and not vip_expire_at:
            return Response({
                'success': False,
                'message': '设置为VIP时，必须提供到期时间'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # 获取用户
        try:
            user = WechatUser.objects.get(openid=request_openid)
        except WechatUser.DoesNotExist:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
            
        # 更新VIP状态
        user.is_vip = is_vip
        if is_vip:
            try:
                user.vip_expire_at = datetime.fromisoformat(vip_expire_at.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                return Response({
                    'success': False,
                    'message': '无效的时间格式'
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            user.vip_expire_at = None
            
        user.save(update_fields=['is_vip', 'vip_expire_at', 'updated_at'])
        
        return Response({
            'success': True,
            'data': {
                'is_vip': user.is_vip,
                'is_valid_vip': user.is_valid_vip,
                'vip_expire_at': user.vip_expire_at
            }
        })
        
    except Exception as e:
        logger.error(f"设置VIP状态失败: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': '设置VIP状态失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_vip_info(request):
    """
    获取用户VIP信息
    
    @header {
        Authorization: "Bearer token"
    }
    @query_param {
        openid: "用户openid"
    }
    @return {
        success: true,
        data: {
            is_vip: true/false,
            is_valid_vip: true/false,
            vip_expire_at: "VIP到期时间"
        }
    }
    """
    try:
        # 验证token
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return Response({
                'success': False,
                'message': '无效的认证头'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            token_openid = payload['openid']
        except jwt.ExpiredSignatureError:
            return Response({
                'success': False,
                'message': '登录已过期'
            }, status=status.HTTP_401_UNAUTHORIZED)
        except jwt.InvalidTokenError:
            return Response({
                'success': False,
                'message': '无效的token'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        # 获取请求参数
        request_openid = request.query_params.get('openid')
        if not request_openid:
            return Response({
                'success': False,
                'message': '缺少openid参数'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # 获取用户
        try:
            user = WechatUser.objects.get(openid=request_openid)
        except WechatUser.DoesNotExist:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
            
        return Response({
            'success': True,
            'data': {
                'is_vip': user.is_vip,
                'is_valid_vip': user.is_valid_vip,
                'vip_expire_at': user.vip_expire_at
            }
        })
        
    except Exception as e:
        logger.error(f"获取VIP信息失败: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': '获取VIP信息失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def get_wechat_user_info(request):
    """
    通过微信接口获取用户信息(需用户授权)
    
    @header {
        Authorization: "Bearer token"
    }
    @body {
        code: "前端通过wx.getUserProfile获取的code",
        encryptedData: "加密的用户信息",
        iv: "加密算法的初始向量"
    }
    @return {
        success: true,
        userInfo: {
            openid: "用户openid",
            nickname: "用户昵称",
            avatar_url: "用户头像",
            gender: "性别",
            country: "国家",
            province: "省份",
            city: "城市"
        }
    }
    """
    try:
        # 验证token
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return Response({
                'success': False,
                'message': '无效的认证头'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
            token_openid = payload['openid']
        except jwt.ExpiredSignatureError:
            return Response({
                'success': False,
                'message': '登录已过期'
            }, status=status.HTTP_401_UNAUTHORIZED)
        except jwt.InvalidTokenError:
            return Response({
                'success': False,
                'message': '无效的token'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 获取参数
        code = request.data.get('code')
        encrypted_data = request.data.get('encryptedData')
        iv = request.data.get('iv')
        
        if not code or not encrypted_data or not iv:
            return Response({
                'success': False,
                'message': '缺少必要参数'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 获取session_key
        wx_api_url = 'https://api.weixin.qq.com/sns/jscode2session'
        params = {
            'appid': settings.WECHAT_APP_ID,
            'secret': settings.WECHAT_APP_SECRET,
            'js_code': code,
            'grant_type': 'authorization_code'
        }
        
        logger.info(f"正在请求微信接口获取session_key，参数: {params}")
        response = requests.get(wx_api_url, params=params)
        wx_data = response.json()
        
        if 'errcode' in wx_data:
            logger.error(f"微信接口返回错误: {wx_data}")
            return Response({
                'success': False,
                'message': '微信接口调用失败',
                'error': wx_data
            }, status=status.HTTP_400_BAD_REQUEST)
        
        session_key = wx_data.get('session_key')
        openid = wx_data.get('openid')
        
        if not session_key or not openid:
            logger.error(f"微信接口返回数据缺少session_key或openid: {wx_data}")
            return Response({
                'success': False,
                'message': '获取会话密钥失败'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # 解密用户信息
        try:
            from Crypto.Cipher import AES
            
            # Base64解码
            session_key = base64.b64decode(session_key)
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)
            
            # 解密
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除补位符
            padding_len = decrypted[-1]
            if padding_len < 1 or padding_len > 32:
                padding_len = 0
            user_info_json = decrypted[:-padding_len].decode('utf-8')
            user_info = json.loads(user_info_json)
            
            if user_info.get('watermark', {}).get('appid') != settings.WECHAT_APP_ID:
                return Response({
                    'success': False,
                    'message': '数据签名验证失败'
                }, status=status.HTTP_400_BAD_REQUEST)
                
            # 获取或创建用户
            user, created = WechatUser.objects.get_or_create(openid=openid)
            
            # 更新用户信息
            user.nickname = user_info.get('nickName', '')
            user.avatar_url = user_info.get('avatarUrl', '')
            user.gender = user_info.get('gender', 0)
            user.country = user_info.get('country', '')
            user.province = user_info.get('province', '')
            user.city = user_info.get('city', '')
            user.save()
            
            # 构建返回结果
            result = {
                'openid': openid,
                'nickname': user.nickname,
                'avatar_url': user.avatar_url,
                'gender': user.gender,
                'country': user.country,
                'province': user.province,
                'city': user.city
            }
            
            return Response({
                'success': True,
                'userInfo': result
            })
            
        except Exception as e:
            logger.error(f"解密用户信息失败: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': '解密用户信息失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': '获取用户信息失败',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
