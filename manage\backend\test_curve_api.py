#!/usr/bin/env python
"""
赛车推进计算与曲线绘制API测试脚本
"""
import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from django.test import Client
from django.urls import reverse


def test_power_speed_curve():
    """测试动力-速度曲线接口"""
    print("=== 测试动力-速度曲线接口 ===")

    client = Client()
    url = "/api/cars/generate-curves/"
    
    # 测试数据（基于speed_power-view.py中的数据）
    test_data = {
        "curve_type": "power_speed",
        "cars": [
            {
                "name": "收割者（推进40）",
                "level": "A/M3/L3",
                "propulsion_levels": [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 40
            },
            {
                "name": "收割者（推进0）",
                "level": "A/M3/L3", 
                "propulsion_levels": [4.2, 4.4, 5.0, 5.35, 6.05, 6.25, 6.4],
                "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                "fuel_intensity": 6290,
                "ignition_intensity": 6290,
                "propulsion_upgrades": 0
            }
        ]
    }
    
    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("✅ 接口调用成功")
                print(f"曲线类型: {data['data']['curve_type']}")
                print(f"平衡线数据点数量: {len(data['data']['balance_curve']['data'])}")
                print(f"赛车数量: {len(data['data']['car_curves'])}")
                
                for i, car in enumerate(data['data']['car_curves']):
                    print(f"赛车{i+1}: {car['name']}")
                    print(f"  基础动力数据点: {len(car['base_power'])}")
                    print(f"  大喷动力数据点: {len(car['fuel_power'])}")
                    print(f"  cww动力数据点: {len(car['boost_power'])}")
                    
                    # 显示前几个数据点
                    print(f"  基础动力前3个点: {car['base_power'][:3]}")
                    print(f"  大喷动力前3个点: {car['fuel_power'][:3]}")
                
            else:
                print(f"❌ 接口返回错误: {data['message']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(response.content.decode('utf-8'))
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_speed_time_curve():
    """测试速度-时间曲线接口"""
    print("\n=== 测试速度-时间曲线接口 ===")

    client = Client()
    url = "/api/cars/generate-curves/"
    
    # 测试数据（基于speed_time-view.py中的数据）
    test_data = {
        "curve_type": "speed_time",
        "cars": [
            {
                "name": "青龙偃月刀-关羽",
                "level": "T2",
                "propulsion_levels": [4.4, 4.6, 5.0, 5.3, 5.9, 6.5, 6.9],
                "engine_levels": [7.77, 7.895, 7.75, 7.6, 7.6, 7.7],
                "fuel_intensity": 6380,
                "ignition_intensity": 7800,
                "propulsion_upgrades": 40
            },
            {
                "name": "至尊-麦凯伦",
                "level": "T2",
                "propulsion_levels": [4.6, 4.7, 5.25, 5.8, 6.1, 6.6, 6.95],
                "engine_levels": [7.8, 7.85, 7.75, 7.75, 7.75, 7.8],
                "fuel_intensity": 6290,
                "ignition_intensity": 6900,
                "propulsion_upgrades": 40
            }
        ]
    }
    
    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print("✅ 接口调用成功")
                print(f"曲线类型: {data['data']['curve_type']}")
                print(f"赛车数量: {len(data['data']['car_curves'])}")
                
                for i, car in enumerate(data['data']['car_curves']):
                    print(f"赛车{i+1}: {car['name']}")
                    print(f"  平跑速度数据点: {len(car['normal_speed'])}")
                    print(f"  大喷速度数据点: {len(car['fuel_speed'])}")
                    print(f"  超级喷速度数据点: {len(car['super_speed'])}")
                    
                    # 显示时间范围和最大速度
                    if car['normal_speed']:
                        max_time = car['normal_speed'][-1][0]
                        max_speed = max(point[1] for point in car['normal_speed'])
                        print(f"  平跑最大时间: {max_time:.2f}秒, 最大速度: {max_speed:.2f}km/h")
                        
                    if car['fuel_speed']:
                        max_speed_fuel = max(point[1] for point in car['fuel_speed'])
                        print(f"  大喷最大速度: {max_speed_fuel:.2f}km/h")
                
            else:
                print(f"❌ 接口返回错误: {data['message']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(response.content.decode('utf-8'))
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def test_error_cases():
    """测试错误情况"""
    print("\n=== 测试错误情况 ===")

    client = Client()
    url = "/api/cars/generate-curves/"
    
    # 测试1: 无效的曲线类型
    print("测试1: 无效的曲线类型")
    test_data = {
        "curve_type": "invalid_type",
        "cars": []
    }
    
    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        data = response.json()
        print(f"  结果: {data['message']}")
    except Exception as e:
        print(f"  异常: {str(e)}")

    # 测试2: 空的赛车数据
    print("测试2: 空的赛车数据")
    test_data = {
        "curve_type": "power_speed",
        "cars": []
    }

    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        data = response.json()
        print(f"  结果: {data['message']}")
    except Exception as e:
        print(f"  异常: {str(e)}")

    # 测试3: 超过两辆赛车
    print("测试3: 超过两辆赛车")
    test_data = {
        "curve_type": "power_speed",
        "cars": [{"name": "car1"}, {"name": "car2"}, {"name": "car3"}]
    }

    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        data = response.json()
        print(f"  结果: {data['message']}")
    except Exception as e:
        print(f"  异常: {str(e)}")

    # 测试4: 推进档位数据不足
    print("测试4: 推进档位数据不足")
    test_data = {
        "curve_type": "power_speed",
        "cars": [{
            "name": "测试车",
            "level": "A",
            "propulsion_levels": [1, 2, 3],  # 只有3个，应该是7个
            "engine_levels": [1, 2, 3, 4, 5, 6],
            "fuel_intensity": 6290,
            "ignition_intensity": 6290
        }]
    }

    try:
        response = client.post(url, data=json.dumps(test_data), content_type='application/json')
        data = response.json()
        print(f"  结果: {data['message']}")
    except Exception as e:
        print(f"  异常: {str(e)}")


def main():
    """主函数"""
    print("开始测试赛车推进计算与曲线绘制API")
    print("=" * 50)
    
    # 测试动力-速度曲线
    test_power_speed_curve()
    
    # 测试速度-时间曲线
    test_speed_time_curve()
    
    # 测试错误情况
    test_error_cases()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
