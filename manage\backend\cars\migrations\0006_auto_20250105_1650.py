# Generated by Django 3.2.23 on 2025-01-05 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0005_auto_20250105_1642'),
    ]

    operations = [
        migrations.AlterField(
            model_name='car',
            name='car_id',
            field=models.CharField(max_length=50, unique=True, verbose_name='赛车编号'),
        ),
        migrations.AlterField(
            model_name='car',
            name='drift_factor',
            field=models.CharField(max_length=50, verbose_name='飘逸系数'),
        ),
        migrations.AlterField(
            model_name='car',
            name='friction_factor',
            field=models.CharField(max_length=50, verbose_name='摩擦系数'),
        ),
        migrations.AlterField(
            model_name='car',
            name='high_speed_steering',
            field=models.CharField(max_length=50, verbose_name='高速转向'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='car',
            name='image_id',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='图片ID'),
        ),
        migrations.AlterField(
            model_name='car',
            name='level',
            field=models.CharField(choices=[('S', 'S级'), ('A', 'A级'), ('B', 'B级'), ('C', 'C级'), ('D', 'D级')], max_length=2, verbose_name='赛车级别'),
        ),
        migrations.AlterField(
            model_name='car',
            name='low_speed_steering',
            field=models.CharField(max_length=50, verbose_name='低速转向'),
        ),
        migrations.AlterField(
            model_name='car',
            name='name',
            field=models.CharField(max_length=100, verbose_name='赛车名称'),
        ),
        migrations.AlterField(
            model_name='car',
            name='nitro_speed',
            field=models.CharField(max_length=50, verbose_name='氮气极速'),
        ),
        migrations.AlterField(
            model_name='car',
            name='normal_speed',
            field=models.CharField(max_length=50, verbose_name='平跑极速'),
        ),
        migrations.AlterField(
            model_name='car',
            name='weight',
            field=models.CharField(max_length=50, verbose_name='车重'),
        ),
    ]
