# QQ飞车推进计算与绘图功能API文档

## 概述

本文档描述了QQ飞车图鉴小程序中推进计算与绘图功能的后端API接口。该功能允许用户搜索赛车、计算满改装推进40档位数据，并生成动力-速度曲线图和速度-时间曲线图。

## 基础信息

- **基础URL**: `/api/cars/`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 接口列表

### 1. 赛车搜索接口

**接口地址**: `GET /api/cars/search/`

**功能描述**: 根据关键词搜索赛车，返回包含原装推进和引擎档位数据的赛车列表。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| q | string | 是 | 搜索关键词（赛车名称或编号） |

**请求示例**:
```
GET /api/cars/search/?q=收割者
```

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "car_id": "A001",
            "name": "收割者",
            "level": "A/M3/L3",
            "engine_levels": [40, 40, 40, 40, 40, 40],
            "propulsion_levels": [4709, 4842, 5400, 6009, 6500, 6753, 6890],
            "fuel_intensity": 6290
        }
    ]
}
```

**响应字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| data | array | 搜索结果列表 |
| car_id | string | 赛车编号 |
| name | string | 赛车名称 |
| level | string | 赛车级别 |
| engine_levels | array | 引擎1-6档数值 |
| propulsion_levels | array | 原装推进1-7档数值 |
| fuel_intensity | number | 燃料强度 |

### 2. 推进40计算接口

**接口地址**: `POST /api/cars/calculate-propulsion/`

**功能描述**: 根据赛车原装推进档位数据计算满改装推进40档位，并生成动力数据。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| car_id | string | 是 | 赛车编号 |
| user_id | string | 否 | 用户ID（用于保存历史记录） |

**请求示例**:
```json
{
    "car_id": "A001",
    "user_id": "user123"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "car_info": {
            "car_id": "A001",
            "name": "收割者",
            "level": "A/M3/L3"
        },
        "original_data": {
            "propulsion_levels": [4709, 4842, 5400, 6009, 6500, 6753, 6890],
            "engine_levels": [40, 40, 40, 40, 40, 40],
            "fuel_intensity": 6290
        },
        "propulsion_40_levels": [5036, 5184, 5800, 6416, 6900, 7115, 7282],
        "power_data": {
            "speed_anchors": [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5],
            "base_powers": [4709, 4842, 5400, 6009, 6500, 6753, 6890, 6890, 6890, 6890, 6890, 6890, 6890],
            "fuel_powers": [9951.67, 10084.67, 10642.67, 11251.67, 11742.67, 11995.67, 12132.67, 12132.67, 12132.67, 12132.67, 12132.67, 12132.67, 12132.67],
            "boost_powers": [15194.33, 15327.33, 15885.33, 16494.33, 16985.33, 17238.33, 17375.33, 17375.33, 17375.33, 17375.33, 17375.33, 17375.33, 17375.33]
        }
    }
}
```

**响应字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| car_info | object | 赛车基本信息 |
| original_data | object | 原装数据 |
| propulsion_40_levels | array | 推进40档位数值 |
| power_data | object | 动力数据 |
| speed_anchors | array | 速度锚点 |
| base_powers | array | 基础动力值 |
| fuel_powers | array | 大喷动力值 |
| boost_powers | array | cww动力值 |

### 3. 图表生成接口

**接口地址**: `POST /api/cars/generate-chart/`

**功能描述**: 生成动力-速度曲线图或速度-时间曲线图的配置数据。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| chart_type | string | 是 | 图表类型（power_speed 或 speed_time） |
| cars | array | 是 | 赛车数据列表 |

**请求示例**:
```json
{
    "chart_type": "power_speed",
    "cars": [
        {
            "name": "收割者",
            "power_data": {
                "speed_anchors": [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5],
                "base_powers": [4709, 4842, 5400, 6009, 6500, 6753, 6890, 6890, 6890, 6890, 6890, 6890, 6890],
                "fuel_powers": [9951.67, 10084.67, 10642.67, 11251.67, 11742.67, 11995.67, 12132.67, 12132.67, 12132.67, 12132.67, 12132.67, 12132.67, 12132.67],
                "boost_powers": [15194.33, 15327.33, 15885.33, 16494.33, 16985.33, 17238.33, 17375.33, 17375.33, 17375.33, 17375.33, 17375.33, 17375.33, 17375.33]
            }
        }
    ]
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "chart_config": {
            "title": {
                "text": "QQ飞车赛车动力-速度曲线图",
                "left": "center"
            },
            "tooltip": {
                "trigger": "axis"
            },
            "legend": {
                "data": ["速度动力平衡线", "收割者 基础动力", "收割者 大喷动力", "收割者 cww动力"],
                "bottom": 0
            },
            "xAxis": {
                "type": "value",
                "name": "速度(km/h)",
                "min": 0,
                "max": 400
            },
            "yAxis": {
                "type": "value",
                "name": "动力(N)"
            },
            "series": [
                {
                    "name": "速度动力平衡线",
                    "type": "line",
                    "data": [[0, 0], [76.5, 1659.825], ...],
                    "lineStyle": {"color": "#000000"},
                    "symbol": "none"
                },
                {
                    "name": "收割者 基础动力",
                    "type": "line",
                    "data": [[0, 4709], [76.5, 4842], ...],
                    "lineStyle": {"color": "#4a90e2"},
                    "symbol": "circle"
                }
            ]
        }
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 错误响应格式

```json
{
    "success": false,
    "message": "错误描述信息"
}
```

## 特殊说明

### T2皮肤等级处理
- 对于以"T2("开头的赛车等级（如"T2(雷诺传奇)"），系统会自动使用"T2皮肤"的计算数据
- 这确保了T2皮肤赛车使用正确的推进计算参数

### 推进档位上限处理
- 当推进档位计算表中的上限值为null时，表示该档位无上限（带+号）
- 计算推进40时，无上限档位直接使用"原装值+差值"的结果
- 有上限档位取"原装值+差值"与"上限值"的最小值

### 数据提取规则
- 所有数值字段支持从包含文本的字符串中提取数字
- 例如："6290强度"会提取出6290这个数值
- 空值或无法提取数字的字段使用默认值

## 使用示例

### 完整的计算流程

1. **搜索赛车**:
```javascript
// 搜索收割者
fetch('/api/cars/search/?q=收割者')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const car = data.data[0];
      console.log('找到赛车:', car.name);
    }
  });
```

2. **计算推进40**:
```javascript
// 计算推进40
fetch('/api/cars/calculate-propulsion/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    car_id: 'A001',
    user_id: 'user123'
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('推进40结果:', data.data.propulsion_40_levels);
    console.log('动力数据:', data.data.power_data);
  }
});
```

3. **生成图表**:
```javascript
// 生成动力-速度图表
fetch('/api/cars/generate-chart/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    chart_type: 'power_speed',
    cars: [{
      name: '收割者',
      power_data: powerData // 从步骤2获取的power_data
    }]
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    // 使用ECharts渲染图表
    const chart = echarts.init(document.getElementById('chart'));
    chart.setOption(data.data.chart_config);
  }
});
```

## 注意事项

1. **数据完整性**: 确保赛车数据中包含完整的推进档位和引擎档位信息
2. **计算精度**: 推进40计算结果为整数，动力计算保留小数
3. **性能考虑**: 图表数据量较大时建议在前端进行适当的数据压缩
4. **缓存策略**: 推荐对计算结果进行缓存以提高响应速度
5. **错误处理**: 请妥善处理API返回的错误信息，提供友好的用户提示
