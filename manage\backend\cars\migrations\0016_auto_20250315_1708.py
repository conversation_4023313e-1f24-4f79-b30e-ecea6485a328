# Generated by Django 3.2.23 on 2025-03-15 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0015_auto_20250308_1726'),
    ]

    operations = [
        migrations.AddField(
            model_name='car',
            name='angle_super_nitro_speed',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='夹角超级喷极速'),
        ),
        migrations.AddField(
            model_name='car',
            name='drift_correction',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='漂移回正'),
        ),
        migrations.AddField(
            model_name='car',
            name='drift_reverse',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='漂移反向'),
        ),
        migrations.AddField(
            model_name='car',
            name='drift_steering',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='漂移转向'),
        ),
        migrations.AddField(
            model_name='car',
            name='drift_swing',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='漂移摆动'),
        ),
        migrations.AddField(
            model_name='car',
            name='fuel_duration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='燃料时长'),
        ),
        migrations.AddField(
            model_name='car',
            name='fuel_intensity',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='燃料强度'),
        ),
        migrations.AddField(
            model_name='car',
            name='ignition_duration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='点火时长'),
        ),
        migrations.AddField(
            model_name='car',
            name='ignition_intensity',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='点火强度'),
        ),
        migrations.AddField(
            model_name='car',
            name='intake_coefficient',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='进气系数'),
        ),
        migrations.AddField(
            model_name='car',
            name='original_intake_coefficient',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='原装进气系数'),
        ),
        migrations.AddField(
            model_name='car',
            name='super_nitro_250_acceleration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='超级喷250提速'),
        ),
        migrations.AddField(
            model_name='car',
            name='super_nitro_290_acceleration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='超级喷290提速'),
        ),
        migrations.AddField(
            model_name='car',
            name='super_nitro_duration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='超级喷时长'),
        ),
        migrations.AddField(
            model_name='car',
            name='super_nitro_intensity',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='超级喷强度'),
        ),
        migrations.AddField(
            model_name='car',
            name='super_nitro_trigger_condition',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='超级喷触发条件'),
        ),
    ]
