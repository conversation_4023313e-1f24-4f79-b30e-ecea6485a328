#!/usr/bin/env python
"""
调试推进计算
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

from cars.models import PropulsionLevelTable

def debug_propulsion_calculation():
    """调试推进计算"""
    
    # 检查推进计算表数据
    print("=== 检查推进计算表数据 ===")
    
    levels = ['A/M3/L3', 'T2', 'T2皮肤']
    
    for level in levels:
        try:
            table_data = PropulsionLevelTable.objects.get(car_level=level)
            print(f"找到等级 {level} 的计算表数据:")
            for i in range(1, 8):
                diff_value = getattr(table_data, f'level_{i}_diff')
                max_value = getattr(table_data, f'level_{i}_max')
                print(f"  档位{i}: diff={diff_value}, max={max_value}")
        except PropulsionLevelTable.DoesNotExist:
            print(f"未找到等级 {level} 的计算表数据")
    
    # 测试推进计算
    print("\n=== 测试推进计算 ===")
    
    original_levels = [4.527, 4.742, 5.400, 5.757, 6.450, 6.612, 6.792]
    print(f"原装推进档位: {original_levels}")

    # 先乘以1000（模拟新的逻辑）
    propulsion_levels_scaled = [level * 1000 for level in original_levels]
    print(f"乘以1000后: {propulsion_levels_scaled}")

    # 模拟计算过程
    car_level = "A/M3/L3"
    upgrade_count = 40
    
    try:
        # 处理T2皮肤等级
        lookup_level = car_level
        if car_level.startswith('T2('):
            lookup_level = 'T2皮肤'
        
        print(f"查找等级: {lookup_level}")
        
        # 获取对应等级的计算表数据
        table_data = PropulsionLevelTable.objects.get(car_level=lookup_level)
        print("找到计算表数据")
        
        propulsion_40_levels = []

        for i, original_level in enumerate(propulsion_levels_scaled):
            level_num = i + 1

            # 获取差值和上限
            diff_value = getattr(table_data, f'level_{level_num}_diff')
            max_value = getattr(table_data, f'level_{level_num}_max')

            print(f"档位{level_num}: 原装={original_level}, diff={diff_value}, max={max_value}")

            # 计算实际改装提升（按比例）
            actual_diff = int(diff_value * upgrade_count / 40)
            calculated_40 = original_level + actual_diff
            
            print(f"  实际提升: {actual_diff}, 计算后: {calculated_40}")
            
            if max_value is None:
                # 无上限（带+号）
                final_40 = calculated_40
                print(f"  无上限，最终: {final_40}")
            else:
                # 有上限，取最小值
                final_40 = min(calculated_40, max_value)
                print(f"  有上限，最终: {final_40}")
            
            propulsion_40_levels.append(int(final_40))
        
        print(f"推进40档位: {propulsion_40_levels}")
        
        # 乘以1000
        propulsion_powers = [level * 1000 for level in propulsion_40_levels]
        print(f"乘以1000后: {propulsion_powers}")
        
    except PropulsionLevelTable.DoesNotExist:
        print("未找到计算表数据，使用简单计算")
        upgrade_per_level = upgrade_count * 8  # 每次改装平均提升8点
        propulsion_40_levels = [int(level + upgrade_per_level) for level in original_levels]
        print(f"简单计算结果: {propulsion_40_levels}")
        
        # 乘以1000
        propulsion_powers = [level * 1000 for level in propulsion_40_levels]
        print(f"乘以1000后: {propulsion_powers}")

if __name__ == "__main__":
    debug_propulsion_calculation()
