from django.db import models
from django.utils import timezone

class WechatUser(models.Model):
    """微信用户信息"""
    openid = models.CharField('OpenID', max_length=100, unique=True)
    nickname = models.CharField('昵称', max_length=100, null=True, blank=True)
    avatar_url = models.URLField('头像', max_length=500, null=True, blank=True)
    gender = models.IntegerField('性别', null=True, blank=True)  # 0: 未知, 1: 男, 2: 女
    country = models.CharField('国家', max_length=100, null=True, blank=True)
    province = models.CharField('省份', max_length=100, null=True, blank=True)
    city = models.CharField('城市', max_length=100, null=True, blank=True)
    is_active = models.BooleanField('是否有效', default=True)
    is_vip = models.BooleanField('是否VIP会员', default=False)
    vip_expire_at = models.DateTimeField('VIP到期时间', null=True, blank=True)
    last_login = models.DateTimeField('最后登录时间', default=timezone.now)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        app_label = 'users'
        verbose_name = '微信用户'
        verbose_name_plural = '微信用户'
        ordering = ['-last_login']

    def __str__(self):
        return self.nickname or self.openid

    @property
    def is_valid_vip(self):
        """
        判断用户VIP是否有效
        """
        if not self.is_vip:
            return False
        if not self.vip_expire_at:
            return False
        return self.vip_expire_at > timezone.now()
