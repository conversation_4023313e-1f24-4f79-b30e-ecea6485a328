from rest_framework import serializers
from .models import TreasureItem, PrizeSource, Prize, PrizeSourceRelation

class TreasureItemSerializer(serializers.ModelSerializer):
    """夺宝奖品序列化器"""

    class Meta:
        model = TreasureItem
        fields = [
            'id', 'name', 'probability', 'expiry_days', 'image_id',
            'quality', 'item_type', 'fragment_value', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PrizeSourceSerializer(serializers.ModelSerializer):
    """奖品来源序列化器"""

    class Meta:
        model = PrizeSource
        fields = [
            'id', 'source_code', 'name', 'source_type', 'image_url', 'description',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PrizeSourceRelationSerializer(serializers.ModelSerializer):
    """奖品来源关系序列化器"""
    source_name = serializers.CharField(source='source.name', read_only=True)
    source_type = serializers.CharField(source='source.source_type', read_only=True)
    source_code = serializers.CharField(source='source.source_code', read_only=True)

    class Meta:
        model = PrizeSourceRelation
        fields = [
            'id', 'source', 'source_name', 'source_type', 'source_code', 'probability', 'created_at'
        ]
        read_only_fields = ['created_at']


class PrizeSerializer(serializers.ModelSerializer):
    """奖品序列化器"""
    rarity_display = serializers.CharField(source='get_rarity_display', read_only=True)
    sources_info = PrizeSourceRelationSerializer(source='source_relations', many=True, read_only=True)

    class Meta:
        model = Prize
        fields = [
            'id', 'prize_code', 'name', 'quantity', 'image_url', 'rarity', 'rarity_display',
            'prize_type', 'probability', 'description',
            'sources_info', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PrizeQuerySerializer(serializers.ModelSerializer):
    """奖品查询序列化器（去重版本，不包含数量字段）"""
    rarity_display = serializers.CharField(source='get_rarity_display', read_only=True)
    sources_info = PrizeSourceRelationSerializer(source='source_relations', many=True, read_only=True)

    class Meta:
        model = Prize
        fields = [
            'id', 'prize_code', 'name', 'image_url', 'rarity', 'rarity_display',
            'prize_type', 'probability', 'description',
            'sources_info', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PrizeSourceDetailSerializer(serializers.ModelSerializer):
    """带有奖品列表的奖品来源详情序列化器"""
    prizes = serializers.SerializerMethodField()
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)

    class Meta:
        model = PrizeSource
        fields = [
            'id', 'source_code', 'name', 'source_type', 'source_type_display', 'image_url',
            'description', 'is_active', 'prizes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_prizes(self, obj):
        """获取与该来源关联的所有奖品"""
        # 通过关联表获取所有奖品
        relations = obj.prize_relations.all()
        prizes = [relation.prize for relation in relations]
        return PrizeSerializer(prizes, many=True).data