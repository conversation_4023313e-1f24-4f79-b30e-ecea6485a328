# Generated by Django 3.2.23 on 2025-03-08 09:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0014_auto_20250307_2318'),
    ]

    operations = [
        migrations.AddField(
            model_name='car',
            name='angle_nitro_speed_advance40',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='夹角氮气极速（推进40）'),
        ),
        migrations.AddField(
            model_name='car',
            name='angle_normal_speed_advance40',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='夹角平跑极速（推进40）'),
        ),
        migrations.AddField(
            model_name='car',
            name='nitro_250_acceleration_advance40',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='大喷250提速（推进40）'),
        ),
        migrations.AddField(
            model_name='car',
            name='nitro_290_acceleration_advance40',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='大喷290提速（推进40）'),
        ),
        migrations.AddField(
            model_name='car',
            name='normal_180_acceleration_advance40',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='平跑180提速（推进40）'),
        ),
        migrations.AddField(
            model_name='car',
            name='normal_speed_acceleration_advance40',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='平跑极速提速（推进40）'),
        ),
    ]
