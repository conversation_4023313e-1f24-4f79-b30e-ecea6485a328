#!/usr/bin/env python
import os
import sys
import requests
import logging
from pathlib import Path
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('pet_images_upload.log')
    ]
)
logger = logging.getLogger(__name__)

def upload_images(image_files, base_url='http://localhost:8000', batch_size=10):
    """
    批量上传宠物图片，分批处理
    
    @param image_files: 图片文件路径列表
    @param base_url: API基础URL
    @param batch_size: 每批处理的文件数量
    @return: (bool, str) 上传是否成功及消息
    """
    url = f"{base_url}/api/pets/upload_images_by_name/"
    
    # 禁用代理
    proxies = {
        'http': None,
        'https': None
    }
    
    # 添加 verify=False 来处理可能的 SSL 证书问题
    verify_ssl = False
    if not verify_ssl:
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    total_success = 0
    total_errors = []
    
    # 分批处理文件
    for i in range(0, len(image_files), batch_size):
        batch_files = image_files[i:i + batch_size]
        logger.info(f"处理第 {i//batch_size + 1} 批，共 {len(batch_files)} 个文件")
        
        try:
            # 准备文件列表
            files = []
            for image_path in batch_files:
                # 检查文件类型
                content_type = 'image/jpeg'
                if image_path.suffix.lower() == '.png':
                    content_type = 'image/png'
                    
                # 使用二进制模式打开文件
                with open(image_path, 'rb') as img_file:
                    files.append(
                        ('images', (image_path.name, img_file.read(), content_type))
                    )
            
            # 发送请求时添加 proxies 参数
            response = requests.post(url, files=files, verify=verify_ssl, proxies=proxies)
            
            # 记录响应状态和内容
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")
            
            try:
                if response.status_code == 200:
                    result = response.json()
                    total_success += result.get('success_count', 0)
                    if result.get('errors'):
                        total_errors.extend(result.get('errors'))
                    logger.info(f"本批次成功上传 {result.get('success_count', 0)} 个文件")
                else:
                    error_text = response.text
                    try:
                        error_json = response.json()
                        error_msg = error_json.get('error', '未知错误')
                    except:
                        error_msg = error_text or '服务器返回非JSON响应'
                    logger.error(f"本批次上传失败: {error_msg}")
                    return False, f"上传失败: {error_msg}"
            except ValueError as json_error:
                logger.error(f"解析响应JSON失败: {str(json_error)}")
                logger.error(f"原始响应内容: {response.text}")
                return False, f"解析响应失败: {response.text}"
                
        except Exception as e:
            logger.error(f"处理批次失败: {str(e)}")
            return False, f"上传出错: {str(e)}"
    
    return True, {
        'success_count': total_success,
        'errors': total_errors
    }

def main():
    """主函数"""
    # 获取脚本所在目录
    script_dir = Path(__file__).resolve().parent
    
    # 支持的图片格式
    image_extensions = ('.jpg', '.jpeg', '.png')
    
    # 获取所有图片文件
    image_files = [
        f for f in script_dir.iterdir()
        if f.is_file() and f.suffix.lower() in image_extensions
    ]
    
    if not image_files:
        logger.error("当前目录未找到任何图片文件")
        sys.exit(1)
    
    logger.info(f"找到 {len(image_files)} 个图片文件")
    
    # 批量上传图片，每批10个文件
    success, result = upload_images(image_files, batch_size=10)
    
    if success:
        logger.info("上传完成！统计结果：")
        logger.info(f"总数: {len(image_files)}")
        logger.info(f"成功: {result.get('success_count', 0)}")
        logger.info("失败详情:")
        for error in result.get('errors', []):
            logger.error(f"文件 {error['file']}: {error['error']}")
    else:
        logger.error(f"批量上传失败: {result}")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\n用户中断执行")
        sys.exit(0)
    except Exception as e:
        logger.error(f"执行出错: {str(e)}")
        sys.exit(1) 