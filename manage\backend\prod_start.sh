#!/bin/bash

# 设置工作目录
PROJECT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd $PROJECT_DIR

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export DJANGO_SETTINGS_MODULE=config.settings
export PYTHONUNBUFFERED=1

# 确保日志目录存在
mkdir -p logs

# 检查是否已经运行
if [ -f logs/gunicorn.pid ]; then
    echo "服务似乎已经在运行，如果确认没有运行，请删除 logs/gunicorn.pid 文件"
    exit 1
fi

# 启动 Gunicorn
echo "正在启动生产环境服务器..."
nohup gunicorn car_wiki.wsgi:application \
    --bind 0.0.0.0:8000 \
    --workers 3 \
    --timeout 120 \
    --pid logs/gunicorn.pid \
    --access-logfile logs/access.log \
    --error-logfile logs/error.log \
    --daemon \
    --reload &

# 显示日志
echo "服务已在后台启动，正在显示日志..."
tail -f logs/access.log logs/error.log 