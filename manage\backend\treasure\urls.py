from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import TreasureItemViewSet, PrizeSourceViewSet, PrizeViewSet

app_name = 'treasure'  # 设置应用命名空间

# 创建路由器
router = DefaultRouter()
router.register(r'items', TreasureItemViewSet)
router.register(r'prize-sources', PrizeSourceViewSet)
router.register(r'prizes', PrizeViewSet)

# URL模式
urlpatterns = [
    path('', include(router.urls)),
]