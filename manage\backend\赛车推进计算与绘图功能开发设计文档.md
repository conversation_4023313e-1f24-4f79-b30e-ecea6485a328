# QQ飞车赛车推进计算与绘图功能开发设计文档

## 1. 项目概述

### 1.1 功能描述
在QQ飞车图鉴小程序中新增赛车推进计算与绘图功能，用户可以通过搜索选择赛车，系统自动从cars_car表获取原装赛车的推进档位和引擎档位数据，计算满改装赛车的推进40档位数据，并生成动力-速度曲线图和速度-时间曲线图。

### 1.2 核心特性
- 赛车搜索选择功能，自动获取原装数据
- 根据推进档位计算表自动计算满改装推进40数据
- 支持动力-速度曲线绘图
- 支持速度-时间曲线绘图
- 支持两车对比功能
- 集成VIP系统，免费用户有使用次数限制
- 响应式设计，适配不同屏幕尺寸

## 2. 技术架构

### 2.1 前端技术栈
- 微信小程序原生框架
- ec-canvas（ECharts for 微信小程序）图表库
- 自定义组件系统

### 2.2 后端技术栈
- Django REST Framework
- SQLite 数据库
- 数据计算服务

### 2.3 数据存储
- 推进档位计算表数据
- 现有cars_car表（包含赛车原装推进和引擎档位数据）
- 用户计算历史记录

## 3. 数据模型设计

### 3.1 推进档位计算表 (PropulsionLevelTable)
```python
class PropulsionLevelTable(models.Model):
    car_level = models.CharField(max_length=20)  # 赛车等级 (C/M1, B/M2/L2/R, T1, A/M3/L3, T2, T2皮肤)
    level_1_max = models.IntegerField(null=True, blank=True)  # 推进1上限，null表示无上限
    level_2_max = models.IntegerField(null=True, blank=True)  # 推进2上限，null表示无上限
    level_3_max = models.IntegerField(null=True, blank=True)  # 推进3上限，null表示无上限
    level_4_max = models.IntegerField(null=True, blank=True)  # 推进4上限，null表示无上限
    level_5_max = models.IntegerField(null=True, blank=True)  # 推进5上限，null表示无上限
    level_6_max = models.IntegerField(null=True, blank=True)  # 推进6上限，null表示无上限
    level_7_max = models.IntegerField(null=True, blank=True)  # 推进7上限，null表示无上限

    level_1_diff = models.IntegerField()  # 推进1的0与40差值
    level_2_diff = models.IntegerField()  # 推进2的0与40差值
    level_3_diff = models.IntegerField()  # 推进3的0与40差值
    level_4_diff = models.IntegerField()  # 推进4的0与40差值
    level_5_diff = models.IntegerField()  # 推进5的0与40差值
    level_6_diff = models.IntegerField()  # 推进6的0与40差值
    level_7_diff = models.IntegerField()  # 推进7的0与40差值

    level_1_avg_increase = models.FloatField()  # 推进1每次改装平均提升
    level_2_avg_increase = models.FloatField()  # 推进2每次改装平均提升
    level_3_avg_increase = models.FloatField()  # 推进3每次改装平均提升
    level_4_avg_increase = models.FloatField()  # 推进4每次改装平均提升
    level_5_avg_increase = models.FloatField()  # 推进5每次改装平均提升
    level_6_avg_increase = models.FloatField()  # 推进6每次改装平均提升
    level_7_avg_increase = models.FloatField()  # 推进7每次改装平均提升
```

### 3.2 现有赛车数据模型 (cars_car表)
现有的cars_car表已包含所需数据，主要字段包括：
```python
# 从现有Car模型中获取的相关字段
class Car(models.Model):
    car_id = models.CharField('赛车编号', max_length=50, unique=True)
    name = models.CharField('赛车名称', max_length=100)
    level = models.CharField('赛车级别', max_length=20)  # 对应推进计算表的car_level

    # 引擎档位数据
    engine_gear_1 = models.CharField('引擎1挡', max_length=50, null=True, blank=True)
    engine_gear_2 = models.CharField('引擎2挡', max_length=50, null=True, blank=True)
    engine_gear_3 = models.CharField('引擎3挡', max_length=50, null=True, blank=True)
    engine_gear_4 = models.CharField('引擎4挡', max_length=50, null=True, blank=True)
    engine_gear_5 = models.CharField('引擎5挡', max_length=50, null=True, blank=True)
    engine_gear_6 = models.CharField('引擎6挡', max_length=50, null=True, blank=True)

    # 推进档位数据（需要从其他字段推导或新增字段）
    original_propulsion_7 = models.CharField('原装推进7档', max_length=50, null=True, blank=True)

    # 燃料强度
    fuel_intensity = models.CharField('燃料强度', max_length=50, null=True, blank=True)

    # 其他相关字段...
```

**注意**：需要在cars_car表中新增推进1-6档的原装数据字段，或者通过现有数据推导获得。

### 3.3 计算历史记录 (CalculationHistory)
```python
class CalculationHistory(models.Model):
    user_id = models.CharField(max_length=100)  # 用户ID
    car_data = models.JSONField()  # 赛车数据
    calculation_result = models.JSONField()  # 计算结果
    chart_type = models.CharField(max_length=20)  # 图表类型 (power_speed, speed_time)
    created_at = models.DateTimeField(auto_now_add=True)
```

## 4. 核心算法实现

### 4.1 推进40计算算法
```python
def calculate_propulsion_40(car_level, original_propulsion_levels):
    """
    根据原装推进档位计算满改装推进40档位

    Args:
        car_level: 赛车等级
        original_propulsion_levels: 原装推进档位列表 [推进1, 推进2, ..., 推进7]

    Returns:
        propulsion_40_levels: 推进40档位列表
    """
    # 处理T2皮肤等级（以"T2("开头的赛车使用T2皮肤数据）
    if car_level.startswith('T2('):
        lookup_level = 'T2皮肤'
    else:
        lookup_level = car_level

    # 获取对应等级的计算表数据
    table_data = PropulsionLevelTable.objects.get(car_level=lookup_level)

    propulsion_40_levels = []

    for i, original_level in enumerate(original_propulsion_levels):
        level_num = i + 1

        # 获取对应档位的差值和上限
        diff_value = getattr(table_data, f'level_{level_num}_diff')
        max_value = getattr(table_data, f'level_{level_num}_max')

        # 计算推进40档位
        calculated_40 = original_level + diff_value

        # 如果上限为None（带+号的无上限），直接使用计算值
        if max_value is None:
            final_40 = calculated_40
        else:
            # 与上限比较，取最小值
            final_40 = min(calculated_40, max_value)

        propulsion_40_levels.append(final_40)

    return propulsion_40_levels
```

### 4.2 动力计算算法
```python
def calculate_power_values(propulsion_levels, engine_levels, fuel_intensity=6290):
    """
    根据推进和引擎档位计算各速度点的动力值

    Args:
        propulsion_levels: 推进档位列表
        engine_levels: 引擎档位列表
        fuel_intensity: 燃料强度

    Returns:
        power_data: 包含基础动力、大喷动力、cww动力的字典
    """
    # 速度锚点（与Python脚本保持一致）
    speed_anchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]

    # 基础动力计算（需要根据推进和引擎档位的具体算法实现）
    base_powers = calculate_base_powers(propulsion_levels, engine_levels, speed_anchors)

    # 大喷动力计算
    big_jet_power = fuel_intensity / 1.2
    fuel_powers = [power + big_jet_power for power in base_powers]

    # cww动力计算（假设小喷也是燃料强度）
    small_jet_power = fuel_intensity / 1.2
    boost_powers = [power + big_jet_power + small_jet_power for power in base_powers]

    return {
        'speed_anchors': speed_anchors,
        'base_powers': base_powers,
        'fuel_powers': fuel_powers,
        'boost_powers': boost_powers
    }
```

## 5. API接口设计

### 5.1 赛车搜索接口
```
GET /api/cars/search/?q=收割者
Content-Type: application/json

Response:
{
    "success": true,
    "data": [
        {
            "car_id": "car_001",
            "name": "收割者",
            "level": "A/M3/L3",
            "engine_levels": [40, 40, 40, 40, 40, 40],
            "propulsion_levels": [4709, 4842, 5400, 6009, 6500, 6753, 6890],
            "fuel_intensity": 6290
        }
    ]
}
```

### 5.2 推进计算接口
```
POST /api/cars/calculate-propulsion/
Content-Type: application/json

Request:
{
    "car_id": "car_001"  # 或者直接传递数据
}

Response:
{
    "success": true,
    "data": {
        "car_info": {
            "car_id": "car_001",
            "name": "收割者",
            "level": "A/M3/L3"
        },
        "original_data": {
            "propulsion_levels": [4709, 4842, 5400, 6009, 6500, 6753, 6890],
            "engine_levels": [40, 40, 40, 40, 40, 40, 40]
        },
        "propulsion_40_levels": [5036, 5184, 5800, 6416, 6900, 7115, 7282],
        "power_data": {
            "speed_anchors": [0, 76.5, 87.21, ...],
            "base_powers": [4709, 4842, 5400, ...],
            "fuel_powers": [9951.67, 10084.67, ...],
            "boost_powers": [15194.33, 15327.33, ...]
        }
    }
}
```

### 5.3 图表数据接口
```
POST /api/cars/generate-chart/
Content-Type: application/json

Request:
{
    "chart_type": "power_speed",  // power_speed 或 speed_time
    "cars": [
        {
            "name": "收割者",
            "power_data": { ... }
        },
        {
            "name": "至尊-麦凯伦",
            "power_data": { ... }
        }
    ]
}

Response:
{
    "success": true,
    "data": {
        "chart_config": {
            "type": "line",
            "data": { ... },
            "options": { ... }
        }
    }
}
```

## 6. 前端页面设计

### 6.1 页面结构
```
/pages/toolbox/car-calculation/
├── car-calculation.js      # 页面逻辑
├── car-calculation.json    # 页面配置
├── car-calculation.wxml    # 页面模板
└── car-calculation.wxss    # 页面样式
```

### 6.2 页面布局
```wxml
<view class="container">
  <!-- 背景图片 -->
  <image class="bg-image" src="{{backgroundImage}}" mode="aspectFill"></image>

  <!-- VIP徽章 -->
  <view class="vip-badge-container">
    <vip-badge id="vipBadge" isVip="{{isVip}}" remainingDays="{{vipRemainingDays}}" bind:tap="handleVipBadgeClick" />
  </view>

  <!-- 页面标题 -->
  <view class="header">
    <view class="title">赛车推进计算与绘图</view>
    <view class="subtitle">根据原装数据计算满改装推进40</view>
  </view>

  <!-- 输入表单 -->
  <view class="input-section">
    <view class="form-card">
      <view class="form-title">赛车选择</view>

      <!-- 赛车搜索 -->
      <view class="form-item">
        <text class="label">搜索赛车</text>
        <view class="search-container">
          <input class="search-input"
                 value="{{searchKeyword}}"
                 bindinput="onSearchInput"
                 placeholder="输入赛车名称搜索" />
          <button class="search-button" bindtap="searchCars">搜索</button>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view class="search-results" wx:if="{{searchResults.length > 0}}">
        <view class="result-item"
              wx:for="{{searchResults}}"
              wx:key="car_id"
              bindtap="selectCar"
              data-car="{{item}}">
          <view class="car-name">{{item.name}}</view>
          <view class="car-level">{{item.level}}</view>
        </view>
      </view>

      <!-- 选中的赛车信息 -->
      <view class="selected-car" wx:if="{{selectedCar}}">
        <view class="form-title">已选择赛车</view>
        <view class="car-info">
          <view class="car-name-selected">{{selectedCar.name}}</view>
          <view class="car-level-selected">{{selectedCar.level}}</view>
        </view>

        <!-- 显示原装数据 -->
        <view class="original-data">
          <view class="data-title">原装数据</view>
          <view class="data-grid">
            <view class="data-item" wx:for="{{selectedCar.propulsion_levels}}" wx:key="index">
              <text class="data-label">推进{{index + 1}}</text>
              <text class="data-value">{{item}}</text>
            </view>
          </view>
          <view class="data-grid">
            <view class="data-item" wx:for="{{selectedCar.engine_levels}}" wx:key="index">
              <text class="data-label">引擎{{index + 1}}</text>
              <text class="data-value">{{item}}</text>
            </view>
          </view>
          <view class="fuel-info">
            <text class="data-label">燃料强度</text>
            <text class="data-value">{{selectedCar.fuel_intensity || 6290}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section" wx:if="{{selectedCar}}">
    <view class="button-group">
      <button class="calc-button" bindtap="calculatePropulsion">计算推进40</button>
      <button class="chart-button" bindtap="generatePowerChart" disabled="{{!showResult}}">生成动力曲线</button>
      <button class="chart-button" bindtap="generateSpeedChart" disabled="{{!showResult}}">生成速度曲线</button>
    </view>
  </view>

  <!-- 计算结果显示 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-card">
      <view class="result-title">推进40计算结果</view>
      <view class="result-levels">
        <view class="result-item" wx:for="{{propulsion40Levels}}" wx:key="index">
          <text class="result-label">推进{{index + 1}}</text>
          <text class="result-value">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 图表显示区域 -->
  <view class="chart-section" wx:if="{{showChart}}">
    <view class="chart-card">
      <view class="chart-title">{{chartTitle}}</view>
      <canvas class="chart-canvas" canvas-id="carChart"></canvas>
    </view>
  </view>

  <!-- 底部区域 -->
  <view class="footer">
    <view class="disclaimer-card">
      <view class="disclaimer">* 仅供参考，实际数据可能有差异</view>
    </view>
    <ad-button text="支持我们" />
  </view>

  <!-- VIP对话框 -->
  <vip-dialog
    show="{{showVipDialog}}"
    pageKey="{{pageKey}}"
    isVip="{{isVip}}"
    vipRemainingDays="{{vipRemainingDays}}"
    bind:close="onVipDialogClose"
    bind:buy="onBuyVip">
  </vip-dialog>

  <!-- 反馈按钮 -->
  <feedback-button bind:tap="onFeedbackTap" />
</view>
```

### 6.3 页面逻辑实现
```javascript
import PageWithVIP from '../../../utils/page-with-vip';
const api = require('../../../utils/api');

const PAGE_KEY = 'car-calculation';

PageWithVIP({
  data: {
    pageKey: PAGE_KEY,
    backgroundImage: '/images/bg.jpg',

    // VIP相关
    isVip: false,
    freeCount: 0,
    vipRemainingDays: 0,
    showVipDialog: false,

    // 搜索相关
    searchKeyword: '',
    searchResults: [],
    selectedCar: null,

    // 结果数据
    showResult: false,
    propulsion40Levels: [],

    // 图表数据
    showChart: false,
    chartTitle: '',
    chartData: null
  },

  onLoad() {
    // 初始化VIP状态和免费次数
    this.initVipStatus();
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索赛车
  async searchCars() {
    if (!this.data.searchKeyword.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '搜索中...' });

    try {
      const result = await api.request('/api/cars/search/', {
        method: 'GET',
        data: {
          q: this.data.searchKeyword
        }
      });

      if (result.success) {
        this.setData({
          searchResults: result.data
        });

        if (result.data.length === 0) {
          wx.showToast({
            title: '未找到相关赛车',
            icon: 'none'
          });
        }
      } else {
        throw new Error(result.message || '搜索失败');
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '搜索失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 选择赛车
  selectCar(e) {
    const car = e.currentTarget.dataset.car;
    this.setData({
      selectedCar: car,
      searchResults: [],
      showResult: false,
      showChart: false
    });

    wx.showToast({
      title: `已选择 ${car.name}`,
      icon: 'success'
    });
  },

  // 计算推进40
  async calculatePropulsion() {
    if (!this.canUseFeature()) return;

    if (!this.data.selectedCar) {
      wx.showToast({
        title: '请先选择赛车',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '计算中...' });

    try {
      const result = await api.request('/api/cars/calculate-propulsion/', {
        method: 'POST',
        data: {
          car_id: this.data.selectedCar.car_id
        }
      });

      if (result.success) {
        this.setData({
          propulsion40Levels: result.data.propulsion_40_levels,
          calculationResult: result.data,
          showResult: true
        });

        // 消耗免费次数
        this.consumeFreeCount(1);

        wx.showToast({
          title: '计算完成',
          icon: 'success'
        });
      } else {
        throw new Error(result.message || '计算失败');
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '计算失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 生成动力曲线图
  async generatePowerChart() {
    if (!this.canUseFeature()) return;
    if (!this.data.showResult) {
      wx.showToast({
        title: '请先计算推进40',
        icon: 'none'
      });
      return;
    }

    await this.generateChart('power_speed', '动力-速度曲线图');
  },

  // 生成速度曲线图
  async generateSpeedChart() {
    if (!this.canUseFeature()) return;
    if (!this.data.showResult) {
      wx.showToast({
        title: '请先计算推进40',
        icon: 'none'
      });
      return;
    }

    await this.generateChart('speed_time', '速度-时间曲线图');
  },

  // 通用图表生成方法
  async generateChart(chartType, chartTitle) {
    wx.showLoading({ title: '生成图表中...' });

    try {
      const result = await api.request('/api/cars/generate-chart/', {
        method: 'POST',
        data: {
          chart_type: chartType,
          cars: [{
            name: `${this.data.carLevels[this.data.carLevelIndex]}级赛车`,
            car_level: this.data.carLevels[this.data.carLevelIndex],
            propulsion_levels: this.data.propulsion40Levels,
            engine_levels: this.data.engineLevels,
            fuel_intensity: this.data.fuelIntensity
          }]
        }
      });

      if (result.success) {
        this.setData({
          showChart: true,
          chartTitle: chartTitle,
          chartData: result.data.chart_config
        });

        // 渲染图表
        this.renderChart(result.data.chart_config);

        // 消耗免费次数
        this.consumeFreeCount(1);

        wx.showToast({
          title: '图表生成完成',
          icon: 'success'
        });
      } else {
        throw new Error(result.message || '图表生成失败');
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '图表生成失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 渲染图表
  renderChart(chartConfig) {
    const ctx = wx.createCanvasContext('carChart', this);

    // 这里需要集成图表库（如ECharts或Chart.js）
    // 具体实现根据选择的图表库而定

    // 示例：使用ECharts
    // const chart = echarts.init(ctx);
    // chart.setOption(chartConfig);
  },

  // 检查是否可以使用功能
  canUseFeature() {
    if (this.data.isVip) {
      return true;
    }

    if (this.data.freeCount <= 0) {
      this.setData({ showVipDialog: true });
      return false;
    }

    return true;
  },

  // 消耗免费次数
  consumeFreeCount(count) {
    if (!this.data.isVip) {
      const newFreeCount = Math.max(0, this.data.freeCount - count);
      this.setData({ freeCount: newFreeCount });
      api.updateFreeCount(PAGE_KEY, -count);
    }
  },

  // VIP徽章点击
  handleVipBadgeClick() {
    this.setData({ showVipDialog: true });
  },

  // VIP对话框关闭
  onVipDialogClose() {
    this.setData({ showVipDialog: false });
  },

  // 购买VIP
  onBuyVip() {
    // VIP购买逻辑
  },

  // 反馈按钮点击
  onFeedbackTap() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  }
});
```

## 7. 样式设计

### 7.1 主要样式
```css
/* 页面容器 */
.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 背景图片 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* VIP徽章容器 */
.vip-badge-container {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  z-index: 100;
}

/* 页面标题 */
.header {
  text-align: center;
  padding: 40rpx 20rpx 20rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 输入表单区域 */
.input-section {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.form-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #4a90e2;
  padding-left: 16rpx;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  min-width: 120rpx;
}

.picker {
  flex: 1;
  padding: 16rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  margin-left: 20rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 档位输入区域 */
.level-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.input-row {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
}

.level-label {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
}

.level-input {
  flex: 1;
  font-size: 28rpx;
  text-align: center;
  background-color: transparent;
}

.fuel-input {
  flex: 1;
  padding: 16rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  margin-left: 20rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 操作按钮区域 */
.action-section {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.calc-button, .chart-button {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.calc-button {
  background: linear-gradient(135deg, #4a90e2, #3670b2);
  color: white;
}

.chart-button {
  background: linear-gradient(135deg, #9c27b0, #7b1fa2);
  color: white;
}

/* 结果显示区域 */
.result-section {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.result-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.result-levels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background-color: #f0f8ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #4a90e2;
}

.result-label {
  font-size: 24rpx;
  color: #666;
}

.result-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #4a90e2;
}

/* 图表显示区域 */
.chart-section {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.chart-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.chart-canvas {
  width: 100%;
  height: 400rpx;
  background-color: #fff;
  border-radius: 8rpx;
}

/* 底部区域 */
.footer {
  padding: 20rpx;
  text-align: center;
}

.disclaimer-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.disclaimer {
  font-size: 24rpx;
  color: #999;
}
```

## 8. 数据库初始化

### 8.1 推进档位计算表数据
```sql
-- 注意：NULL值表示无上限（带+号的数据）
INSERT INTO propulsion_level_table (
  car_level,
  level_1_max, level_2_max, level_3_max, level_4_max, level_5_max, level_6_max, level_7_max,
  level_1_diff, level_2_diff, level_3_diff, level_4_diff, level_5_diff, level_6_diff, level_7_diff,
  level_1_avg_increase, level_2_avg_increase, level_3_avg_increase, level_4_avg_increase,
  level_5_avg_increase, level_6_avg_increase, level_7_avg_increase
) VALUES
('C/M1', NULL, 4381, 4959, 5462, NULL, NULL, 6244, 304, 304, 367, 370, 366, 334, 355, 7.6, 7.6, 9.175, 9.25, 9.15, 8.35, 8.875),
('B/M2/L2/R', 4599, 4671, 5179, 5752, NULL, NULL, 6621, 319, 324, 383, 389, 383, 342, 377, 7.975, 8.1, 9.575, 9.725, 9.575, 8.55, 9.425),
('T1', 4682, 4736, NULL, 5900, NULL, NULL, 6809, 325, 329, 397, 400, 396, 360, 388, 8.125, 8.225, 9.925, 10, 9.9, 9, 9.7),
('A/M3/L3', 4709, NULL, NULL, 6009, 6500, 6753, 6890, 327, 342, 400, 407, 400, 362, 392, 8.175, 8.55, 10, 10.175, 10, 9.05, 9.8),
('T2', NULL, 5038, NULL, NULL, NULL, 6753, 6890, 331, 350, 400, 400, 400, 362, 392, 8.275, 8.75, 10, 10, 10, 9.05, 9.8),
('T2皮肤', 4764, NULL, NULL, 5900, NULL, 6753, 6890, 331, 350, 400, 400, 400, 362, 392, 8.275, 8.75, 10, 10, 10, 9.05, 9.8);
```

## 9. 开发计划

### 9.1 第一阶段：基础功能开发（1-2周）
- [ ] 数据库模型设计和创建
- [ ] 后端API接口开发
- [ ] 推进40计算算法实现
- [ ] 基础前端页面搭建

### 9.2 第二阶段：图表功能开发（1-2周）
- [ ] 图表库集成（ECharts或Chart.js）
- [ ] 动力-速度曲线绘制
- [ ] 速度-时间曲线绘制
- [ ] 图表交互功能

### 9.3 第三阶段：功能完善（1周）
- [ ] VIP系统集成
- [ ] 两车对比功能
- [ ] 数据验证和错误处理
- [ ] 用户体验优化

### 9.4 第四阶段：测试和上线（1周）
- [ ] 功能测试
- [ ] 性能优化
- [ ] 文档完善
- [ ] 正式上线

## 10. 注意事项

### 10.1 特殊赛车处理
- 雷诺传奇皮肤和飞碟不适用推进档位计算表
- 这些特殊赛车直接使用在线表格给定的推进40数据
- 无法改装的赛车需要特殊标注和处理

### 10.2 数据准确性
- 计算结果仅供参考，实际游戏数据可能有差异
- 需要在页面中添加免责声明
- 建议用户以游戏内实际数据为准

### 10.3 性能考虑
- 图表渲染可能消耗较多资源，需要优化
- 大量计算应在后端完成，前端只负责展示
- 考虑添加缓存机制提高响应速度

### 10.4 用户体验
- 输入验证和错误提示要友好
- 加载状态要明确显示
- 支持数据保存和历史记录查看

## 11. 后端实现细节

### 11.1 Django Views实现
```python
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import PropulsionLevelTable, CarData
from .serializers import CarDataSerializer
import numpy as np

class CalculatePropulsionView(APIView):
    """推进40计算接口"""

    def post(self, request):
        try:
            car_level = request.data.get('car_level')
            propulsion_levels = request.data.get('propulsion_levels', [])
            engine_levels = request.data.get('engine_levels', [])
            fuel_intensity = request.data.get('fuel_intensity', 6290)

            # 验证输入数据
            if not car_level or len(propulsion_levels) != 7 or len(engine_levels) != 7:
                return Response({
                    'success': False,
                    'message': '输入数据不完整'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 计算推进40
            propulsion_40_levels = self.calculate_propulsion_40(car_level, propulsion_levels)

            # 计算动力数据
            power_data = self.calculate_power_data(propulsion_40_levels, engine_levels, fuel_intensity)

            return Response({
                'success': True,
                'data': {
                    'propulsion_40_levels': propulsion_40_levels,
                    'power_data': power_data
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def calculate_propulsion_40(self, car_level, original_levels):
        """计算推进40档位"""
        try:
            table_data = PropulsionLevelTable.objects.get(car_level=car_level)
        except PropulsionLevelTable.DoesNotExist:
            raise ValueError(f'未找到{car_level}等级的计算数据')

        propulsion_40_levels = []

        for i, original_level in enumerate(original_levels):
            level_num = i + 1

            # 获取差值和上限
            diff_value = getattr(table_data, f'level_{level_num}_diff')
            max_value = getattr(table_data, f'level_{level_num}_max')

            # 计算推进40
            calculated_40 = original_level + diff_value
            final_40 = min(calculated_40, max_value)

            propulsion_40_levels.append(final_40)

        return propulsion_40_levels

    def calculate_power_data(self, propulsion_levels, engine_levels, fuel_intensity):
        """计算动力数据"""
        # 速度锚点
        speed_anchors = [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]

        # 基础动力计算（这里需要根据实际算法实现）
        base_powers = self.calculate_base_powers(propulsion_levels, engine_levels, speed_anchors)

        # 大喷动力
        big_jet_power = fuel_intensity / 1.2
        fuel_powers = [power + big_jet_power for power in base_powers]

        # cww动力
        small_jet_power = fuel_intensity / 1.2
        boost_powers = [power + big_jet_power + small_jet_power for power in base_powers]

        return {
            'speed_anchors': speed_anchors,
            'base_powers': base_powers,
            'fuel_powers': fuel_powers,
            'boost_powers': boost_powers
        }

    def calculate_base_powers(self, propulsion_levels, engine_levels, speed_anchors):
        """计算基础动力值"""
        # 这里需要根据您的Python脚本中的具体算法来实现
        # 暂时返回示例数据
        return [4500 + i * 100 for i in range(len(speed_anchors))]

class GenerateChartView(APIView):
    """图表生成接口"""

    def post(self, request):
        try:
            chart_type = request.data.get('chart_type')
            cars = request.data.get('cars', [])

            if chart_type == 'power_speed':
                chart_config = self.generate_power_speed_chart(cars)
            elif chart_type == 'speed_time':
                chart_config = self.generate_speed_time_chart(cars)
            else:
                return Response({
                    'success': False,
                    'message': '不支持的图表类型'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'success': True,
                'data': {
                    'chart_config': chart_config
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def generate_power_speed_chart(self, cars):
        """生成动力-速度图表配置"""
        # 速度动力平衡线
        x_range = np.linspace(0, 382, 1000)
        y_balance = self.balance_curve(x_range)

        series = [{
            'name': '速度动力平衡线',
            'type': 'line',
            'data': [[x, y] for x, y in zip(x_range.tolist(), y_balance.tolist())],
            'lineStyle': {'color': '#000000'},
            'symbol': 'none'
        }]

        colors = ['#4a90e2', '#e74c3c', '#2ecc71', '#f39c12']

        for i, car in enumerate(cars):
            power_data = car.get('power_data', {})
            speed_anchors = power_data.get('speed_anchors', [])
            base_powers = power_data.get('base_powers', [])
            fuel_powers = power_data.get('fuel_powers', [])
            boost_powers = power_data.get('boost_powers', [])

            color = colors[i % len(colors)]

            # 基础动力线
            series.append({
                'name': f"{car['name']} 基础动力",
                'type': 'line',
                'data': [[x, y] for x, y in zip(speed_anchors, base_powers)],
                'lineStyle': {'color': color},
                'symbol': 'circle'
            })

            # 大喷动力线
            series.append({
                'name': f"{car['name']} 大喷动力",
                'type': 'line',
                'data': [[x, y] for x, y in zip(speed_anchors, fuel_powers)],
                'lineStyle': {'color': color, 'type': 'dashed'},
                'symbol': 'circle'
            })

            # cww动力线
            series.append({
                'name': f"{car['name']} cww动力",
                'type': 'line',
                'data': [[x, y] for x, y in zip(speed_anchors, boost_powers)],
                'lineStyle': {'color': color, 'type': 'dotted'},
                'symbol': 'circle'
            })

        return {
            'title': {
                'text': 'QQ飞车赛车动力-速度曲线图',
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'axis'
            },
            'legend': {
                'data': [s['name'] for s in series],
                'bottom': 0
            },
            'xAxis': {
                'type': 'value',
                'name': '速度(km/h)',
                'min': 0,
                'max': 400
            },
            'yAxis': {
                'type': 'value',
                'name': '动力(N)'
            },
            'series': series
        }

    def generate_speed_time_chart(self, cars):
        """生成速度-时间图表配置"""
        # 这里需要实现速度-时间曲线的计算逻辑
        # 参考您的speed_time-view.py脚本

        series = []
        colors = ['#4a90e2', '#e74c3c', '#2ecc71', '#f39c12']

        for i, car in enumerate(cars):
            # 计算速度-时间数据
            time_data, speed_data = self.calculate_speed_time_curve(car)

            color = colors[i % len(colors)]

            series.append({
                'name': f"{car['name']} 平跑",
                'type': 'line',
                'data': [[t, v] for t, v in zip(time_data, speed_data)],
                'lineStyle': {'color': color},
                'symbol': 'none'
            })

        return {
            'title': {
                'text': 'QQ飞车赛车速度-时间曲线图',
                'left': 'center'
            },
            'tooltip': {
                'trigger': 'axis'
            },
            'legend': {
                'data': [s['name'] for s in series],
                'bottom': 0
            },
            'xAxis': {
                'type': 'value',
                'name': '时间(秒)',
                'min': 0,
                'max': 16
            },
            'yAxis': {
                'type': 'value',
                'name': '速度(km/h)',
                'min': 0,
                'max': 350
            },
            'series': series
        }

    def balance_curve(self, x):
        """速度动力平衡线"""
        return np.piecewise(x, [x < 45.9, x >= 45.9],
                           [lambda x: 10.9 * x + 0.14 * x * x,
                            lambda x: 500.81703297 - 0.00308486 * x + 0.14017491 * x * x])

    def calculate_speed_time_curve(self, car):
        """计算速度-时间曲线"""
        # 这里需要实现微分方程求解
        # 参考您的Python脚本中的solve_ivp部分

        # 示例数据，实际需要根据物理模型计算
        time_data = np.linspace(0, 16, 100)
        speed_data = 200 * (1 - np.exp(-time_data / 3))  # 示例指数增长曲线

        return time_data.tolist(), speed_data.tolist()
```

### 11.2 URL配置
```python
# urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('api/cars/calculate-propulsion/', views.CalculatePropulsionView.as_view(), name='calculate-propulsion'),
    path('api/cars/generate-chart/', views.GenerateChartView.as_view(), name='generate-chart'),
]
```

## 12. 前端图表集成

### 12.1 图表库选择说明
经过调研，微信小程序原生框架支持以下图表库：

1. **ec-canvas（推荐）**：ECharts官方提供的微信小程序版本
   - 功能完整，支持复杂图表
   - 官方维护，稳定性好
   - 需要引入ec-canvas组件

2. **wx-charts**：专为微信小程序设计的轻量级图表库
   - 体积小，性能好
   - 功能相对简单
   - 适合基础图表需求

**建议使用ec-canvas**，因为我们需要绘制复杂的动力-速度曲线图。

### 12.2 ec-canvas集成步骤

#### 12.2.1 安装ec-canvas
```bash
# 下载ec-canvas组件
git clone https://github.com/ecomfe/echarts-for-weixin.git
# 将ec-canvas文件夹复制到小程序项目的components目录
```

#### 12.2.2 页面配置
```json
{
  "usingComponents": {
    "ec-canvas": "../../components/ec-canvas/ec-canvas"
  }
}
```

#### 12.2.3 WXML模板
```xml
<ec-canvas id="mychart-dom-bar" canvas-id="mychart-bar" ec="{{ ec }}"></ec-canvas>
```

### 12.3 ECharts集成实现
```javascript
// 在页面中引入ECharts
import * as echarts from '../../components/ec-canvas/echarts';

Page({
  data: {
    ec: {
      onInit: this.initChart
    }
  },

  initChart(canvas, width, height, dpr) {
    const chart = echarts.init(canvas, null, {
      width: width,
      height: height,
      devicePixelRatio: dpr
    });
    canvas.setChart(chart);

    // 返回chart实例，供后续使用
    this.chart = chart;
    return chart;
  },

  // 渲染图表的具体实现
  renderChart(chartConfig) {
    if (this.chart) {
      this.chart.setOption(chartConfig);
    }
  }
```

### 12.2 图表交互功能
```javascript
// 添加图表点击事件
onChartClick(e) {
  if (this.chart) {
    // 处理图表点击事件
    console.log('图表点击:', e);
  }
}

// 图表缩放功能
enableChartZoom() {
  if (this.chart) {
    this.chart.setOption({
      dataZoom: [{
        type: 'inside',
        xAxisIndex: 0
      }, {
        type: 'slider',
        xAxisIndex: 0,
        bottom: 10
      }]
    });
  }
}
```

## 13. 测试用例

### 13.1 后端API测试
```python
# tests.py
from django.test import TestCase
from rest_framework.test import APIClient
from .models import PropulsionLevelTable

class CarCalculationAPITest(TestCase):
    def setUp(self):
        self.client = APIClient()

        # 创建测试数据
        PropulsionLevelTable.objects.create(
            car_level='A/M3/L3',
            level_1_max=4709, level_2_max=4842, level_3_max=5400,
            level_4_max=6009, level_5_max=6500, level_6_max=6753, level_7_max=6890,
            level_1_diff=327, level_2_diff=342, level_3_diff=400,
            level_4_diff=407, level_5_diff=400, level_6_diff=362, level_7_diff=392,
            level_1_avg_increase=8.175, level_2_avg_increase=8.55, level_3_avg_increase=10,
            level_4_avg_increase=10.175, level_5_avg_increase=10, level_6_avg_increase=9.05, level_7_avg_increase=9.8
        )

    def test_calculate_propulsion_40(self):
        """测试推进40计算"""
        data = {
            'car_level': 'A/M3/L3',
            'propulsion_levels': [4200, 4400, 5000, 5350, 6050, 6250, 6400],
            'engine_levels': [40, 40, 40, 40, 40, 40, 40],
            'fuel_intensity': 6290
        }

        response = self.client.post('/api/cars/calculate-propulsion/', data, format='json')

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['success'])
        self.assertIn('propulsion_40_levels', response.data['data'])
        self.assertIn('power_data', response.data['data'])

    def test_generate_chart(self):
        """测试图表生成"""
        data = {
            'chart_type': 'power_speed',
            'cars': [{
                'name': 'A级赛车',
                'car_level': 'A/M3/L3',
                'propulsion_levels': [4527, 4742, 5400, 5757, 6450, 6612, 6792],
                'engine_levels': [40, 40, 40, 40, 40, 40, 40],
                'fuel_intensity': 6290
            }]
        }

        response = self.client.post('/api/cars/generate-chart/', data, format='json')

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['success'])
        self.assertIn('chart_config', response.data['data'])
```

### 13.2 前端功能测试
```javascript
// 测试用例（使用小程序测试框架）
describe('赛车推进计算页面', () => {
  let page;

  beforeEach(() => {
    page = new Page();
  });

  test('应该正确计算推进40', async () => {
    // 设置输入数据
    page.setData({
      carLevelIndex: 3, // A/M3/L3
      propulsionLevels: [4200, 4400, 5000, 5350, 6050, 6250, 6400],
      engineLevels: [40, 40, 40, 40, 40, 40, 40]
    });

    // 模拟API响应
    wx.request = jest.fn().mockResolvedValue({
      data: {
        success: true,
        data: {
          propulsion_40_levels: [4527, 4742, 5400, 5757, 6450, 6612, 6792]
        }
      }
    });

    // 执行计算
    await page.calculatePropulsion();

    // 验证结果
    expect(page.data.showResult).toBe(true);
    expect(page.data.propulsion40Levels).toHaveLength(7);
  });

  test('应该正确生成图表', async () => {
    // 设置前置条件
    page.setData({
      showResult: true,
      propulsion40Levels: [4527, 4742, 5400, 5757, 6450, 6612, 6792]
    });

    // 模拟图表生成
    await page.generatePowerChart();

    // 验证图表显示
    expect(page.data.showChart).toBe(true);
    expect(page.data.chartTitle).toBe('动力-速度曲线图');
  });
});
```

## 14. 部署说明

### 14.1 后端部署
1. 安装依赖包：`pip install -r requirements.txt`
2. 运行数据库迁移：`python manage.py migrate`
3. 初始化推进档位计算表数据
4. 启动服务：`python manage.py runserver`

### 14.2 前端部署
1. 在小程序开发工具中导入项目
2. 配置后端API地址
3. 上传代码并提交审核
4. 发布上线

### 14.3 数据备份
- 定期备份推进档位计算表数据
- 备份用户计算历史记录
- 监控API调用频率和错误率

## 15. 总结

### 15.1 功能特点
本设计文档详细描述了QQ飞车赛车推进计算与绘图功能的完整实现方案，主要特点包括：

1. **智能赛车搜索**：用户通过搜索选择赛车，自动从cars_car表获取原装数据
2. **准确的计算逻辑**：基于您提供的推进档位计算表，实现精确的推进40计算，正确处理带+号的无上限档位
3. **T2皮肤识别**：自动识别以"T2("开头的赛车，使用T2皮肤计算数据
4. **直观的图表展示**：使用ec-canvas（ECharts for 微信小程序）支持动力-速度曲线和速度-时间曲线
5. **完整的VIP系统**：集成现有VIP系统，支持免费次数限制
6. **响应式设计**：遵循项目设计规范，适配不同屏幕尺寸
7. **两车对比功能**：支持最多两辆赛车的性能对比
8. **SQLite数据库**：使用项目标准的SQLite数据库存储

### 15.2 技术亮点
- 前后端分离架构，后端负责复杂计算，前端专注用户体验
- 使用ec-canvas（ECharts for 微信小程序），提供丰富的图表交互功能
- 集成现有cars_car表，无需重复录入赛车数据
- 智能处理T2皮肤等级和无上限档位
- 完善的错误处理和用户提示机制
- 支持数据缓存，提高响应速度
- 完整的测试用例覆盖

### 15.3 开发建议
1. **分阶段开发**：建议按照文档中的开发计划分4个阶段进行
2. **数据完善**：需要在cars_car表中补充推进1-6档的原装数据字段
3. **图表库集成**：优先集成ec-canvas组件，确保图表功能正常
4. **数据验证**：在开发过程中需要与您的Python脚本对比验证计算结果的准确性
5. **性能优化**：图表渲染可能消耗较多资源，需要在实际设备上测试性能
6. **用户体验**：重点关注搜索便利性和结果展示的清晰度

### 15.4 后续扩展
- 支持更多赛车等级和特殊赛车
- 添加历史记录和收藏功能
- 支持导出图表为图片
- 添加更多图表类型（如加速度曲线等）
- 支持批量计算和对比

### 15.5 交付清单
开发完成后，您将获得：
- [ ] 完整的前端页面（WXML、WXSS、JS、JSON文件）
- [ ] 后端API接口（Django Views、Models、URLs）
- [ ] 数据库初始化脚本
- [ ] 测试用例和文档
- [ ] 部署说明和运维指南

---

**注意**：本文档基于您提供的计算逻辑和现有项目结构编写，在实际开发过程中可能需要根据具体情况进行调整。建议在开发前与开发团队详细讨论技术细节，确保实现方案的可行性。
