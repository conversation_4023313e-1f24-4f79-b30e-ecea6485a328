# Generated by Django 3.2.23 on 2025-04-20 11:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0002_auto_20250322_1653'),
    ]

    operations = [
        migrations.CreateModel(
            name='LotteryRecord',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('activity_type', models.CharField(choices=[('treasure-hunting', '赛车夺宝'), ('supertreasure', '至尊夺宝'), ('luckytree', '幸运摇钱树')], db_index=True, max_length=50)),
                ('rarity_score', models.FloatField(default=0, help_text='稀有度得分')),
                ('draw_count', models.IntegerField(default=0, help_text='总抽奖次数')),
                ('has_legendary_items', models.BooleanField(default=False, help_text='是否有传说级物品')),
                ('has_gold_items', models.BooleanField(default=False, help_text='是否有金色物品')),
                ('timestamp', models.DateTimeField(auto_now=True, help_text='记录更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lottery_records', to='users.wechatuser')),
            ],
        ),
        migrations.CreateModel(
            name='LotteryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_id', models.IntegerField(help_text='物品ID')),
                ('name', models.CharField(help_text='物品名称', max_length=100)),
                ('background', models.CharField(choices=[('legendary', '传说'), ('gold', '金色'), ('purple', '紫色'), ('normal', '普通')], help_text='物品背景色', max_length=20)),
                ('probability', models.FloatField(help_text='物品抽取概率')),
                ('count', models.IntegerField(default=1, help_text='抽到的数量')),
                ('record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='lottery.lotteryrecord')),
            ],
        ),
        migrations.AddIndex(
            model_name='lotteryrecord',
            index=models.Index(fields=['activity_type', 'rarity_score'], name='lottery_lot_activit_917e2e_idx'),
        ),
        migrations.AddIndex(
            model_name='lotteryrecord',
            index=models.Index(fields=['activity_type', 'draw_count'], name='lottery_lot_activit_ad63c2_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='lotteryrecord',
            unique_together={('user', 'activity_type')},
        ),
    ]
