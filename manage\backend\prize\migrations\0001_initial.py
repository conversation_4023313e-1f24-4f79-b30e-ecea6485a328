# Generated by Django 3.2.23 on 2025-05-03 11:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PrizeSource',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='名称')),
                ('source_type', models.CharField(choices=[('item', '道具'), ('mode', '模式')], default='item', max_length=20, verbose_name='类型')),
                ('image_url', models.URLField(blank=True, max_length=500, null=True, verbose_name='图片URL')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否上架')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '奖品来源',
                'verbose_name_plural': '奖品来源',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Prize',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='奖品名称')),
                ('quantity', models.IntegerField(default=1, verbose_name='奖品数量')),
                ('image_url', models.URLField(blank=True, max_length=500, null=True, verbose_name='图片URL')),
                ('rarity', models.CharField(choices=[('common', '普通'), ('rare', '稀有'), ('epic', '史诗'), ('legendary', '传说')], default='common', max_length=20, verbose_name='稀有度')),
                ('probability', models.DecimalField(blank=True, decimal_places=6, help_text='百分比，例如5.25表示5.25%', max_digits=10, null=True, verbose_name='获取概率')),
                ('description', models.TextField(blank=True, null=True, verbose_name='奖品描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prizes', to='prize.prizesource', verbose_name='所属来源')),
            ],
            options={
                'verbose_name': '奖品',
                'verbose_name_plural': '奖品',
                'ordering': ['-created_at'],
            },
        ),
    ]
