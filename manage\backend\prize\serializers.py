from rest_framework import serializers
from .models import PrizeSource, Prize

class PrizeSourceSerializer(serializers.ModelSerializer):
    """奖品来源序列化器"""
    
    class Meta:
        model = PrizeSource
        fields = [
            'id', 'name', 'source_type', 'image_url', 'description', 
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PrizeSerializer(serializers.ModelSerializer):
    """奖品序列化器"""
    source_name = serializers.CharField(source='source.name', read_only=True)
    source_type = serializers.CharField(source='source.source_type', read_only=True)
    rarity_display = serializers.CharField(source='get_rarity_display', read_only=True)
    
    class Meta:
        model = Prize
        fields = [
            'id', 'name', 'quantity', 'image_url', 'rarity', 'rarity_display',
            'probability', 'description', 'source', 'source_name', 'source_type',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PrizeSourceDetailSerializer(serializers.ModelSerializer):
    """带有奖品列表的奖品来源详情序列化器"""
    prizes = PrizeSerializer(many=True, read_only=True)
    source_type_display = serializers.CharField(source='get_source_type_display', read_only=True)
    
    class Meta:
        model = PrizeSource
        fields = [
            'id', 'name', 'source_type', 'source_type_display', 'image_url', 
            'description', 'is_active', 'prizes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
