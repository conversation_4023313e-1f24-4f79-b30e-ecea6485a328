# Generated by Django 3.2.23 on 2025-03-07 15:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0013_alter_carrating_appearance_rating'),
    ]

    operations = [
        migrations.AddField(
            model_name='car',
            name='angle_nitro_speed',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='夹角氮气极速'),
        ),
        migrations.AddField(
            model_name='car',
            name='angle_normal_speed',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='夹角平跑极速'),
        ),
        migrations.AddField(
            model_name='car',
            name='nitro_250_acceleration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='大喷250提速'),
        ),
        migrations.AddField(
            model_name='car',
            name='nitro_290_acceleration',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True, verbose_name='大喷290提速'),
        ),
        migrations.AddField(
            model_name='car',
            name='normal_180_acceleration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='平跑180提速'),
        ),
        migrations.AddField(
            model_name='car',
            name='normal_speed_acceleration',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='平跑极速提速'),
        ),
    ]
