from django.contrib import admin
from .models import Car, PropulsionLevelTable, CalculationHistory

# Register your models here.

@admin.register(Car)
class CarAdmin(admin.ModelAdmin):
    list_display = ('car_id', 'name', 'level', 'normal_speed', 'nitro_speed')
    list_filter = ('level',)
    search_fields = ('car_id', 'name')
    ordering = ('car_id',)

@admin.register(PropulsionLevelTable)
class PropulsionLevelTableAdmin(admin.ModelAdmin):
    list_display = ('car_level', 'level_1_diff', 'level_2_diff', 'level_3_diff', 'level_4_diff', 'created_at')
    list_filter = ('car_level',)
    search_fields = ('car_level',)
    ordering = ('car_level',)

    fieldsets = (
        ('基本信息', {
            'fields': ('car_level',)
        }),
        ('推进档位上限', {
            'fields': ('level_1_max', 'level_2_max', 'level_3_max', 'level_4_max',
                      'level_5_max', 'level_6_max', 'level_7_max'),
            'classes': ('collapse',)
        }),
        ('推进档位差值', {
            'fields': ('level_1_diff', 'level_2_diff', 'level_3_diff', 'level_4_diff',
                      'level_5_diff', 'level_6_diff', 'level_7_diff')
        }),
        ('平均提升值', {
            'fields': ('level_1_avg_increase', 'level_2_avg_increase', 'level_3_avg_increase',
                      'level_4_avg_increase', 'level_5_avg_increase', 'level_6_avg_increase',
                      'level_7_avg_increase'),
            'classes': ('collapse',)
        })
    )

@admin.register(CalculationHistory)
class CalculationHistoryAdmin(admin.ModelAdmin):
    list_display = ('user_id', 'chart_type', 'created_at')
    list_filter = ('chart_type', 'created_at')
    search_fields = ('user_id',)
    ordering = ('-created_at',)
    readonly_fields = ('created_at',)

    def has_add_permission(self, request):
        return False  # 不允许手动添加历史记录
