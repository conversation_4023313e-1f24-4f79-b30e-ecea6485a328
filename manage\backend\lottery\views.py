from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from .models import LotteryRecord, LotteryItem
from .serializers import (
    LotteryRecordSerializer,
    LotteryRecordDetailSerializer,
    RankingItemSerializer,
    RankingResponseSerializer
)
from .utils import LotteryManager, RarityScoreCalculator

# Create your views here.

class LotteryRankingsView(APIView):
    """
    获取排行榜数据
    GET /lottery/rankings
    """
    def get(self, request):
        # 获取参数
        activity_type = request.query_params.get('activityType')
        rank_type = request.query_params.get('type')
        limit = min(int(request.query_params.get('limit', 20)), 50)  # 最大50
        page = int(request.query_params.get('page', 1))
        openid = request.query_params.get('openid')  # 可选参数，如果提供则获取该用户排名
        nickname = request.query_params.get('nickname')  # 可选参数，用于更新用户昵称
        order = request.query_params.get('order', 'desc')  # 排序方式，默认降序

        # 检查参数
        if not activity_type or activity_type not in dict(LotteryRecord.ACTIVITY_TYPES):
            return Response({
                'code': 1004,
                'message': '活动类型不存在',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        if not rank_type or rank_type not in ['rarity', 'draws']:
            return Response({
                'code': 1002,
                'message': '排行榜类型不存在',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        if order not in ['asc', 'desc']:
            return Response({
                'code': 1005,
                'message': '排序方式不存在，只支持asc(升序)和desc(降序)',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 获取排行榜数据
            rankings = LotteryManager.get_rankings(activity_type, rank_type, limit, page, order)

            # 获取用户排名（如果提供了openid）
            user_rank = None
            if openid:
                # 获取或创建用户，如果提供了nickname则同步更新
                from users.models import WechatUser
                defaults = {'nickname': nickname} if nickname else {}
                user, created = WechatUser.objects.update_or_create(openid=openid, defaults=defaults)
                user_rank = LotteryManager.get_user_rank(user, activity_type, rank_type, order)

            # 准备响应数据
            response_data = {
                'activityType': activity_type,
                'rankList': rankings['rank_list'],
                'userRank': user_rank,
                'totalParticipants': rankings['total_count'],
                'order': rankings['order'],  # 添加排序方式
                'lastUpdateTime': rankings['last_update'] if rankings['last_update'] else timezone.now()
            }

            # 序列化响应数据
            serializer = RankingResponseSerializer(response_data)

            return Response({
                'code': 0,
                'message': 'success',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'code': 2001,
                'message': f'服务器内部错误: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LotteryRecordsView(APIView):
    """
    上传抽奖记录
    POST /lottery/records
    """
    # 移除权限类，不再需要认证
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        activity_type = request.data.get('activityType')
        openid = request.data.get('openid')  # 直接从请求数据获取openid
        nickname = request.data.get('nickname')  # 获取昵称

        # 检查参数
        if not activity_type or activity_type not in dict(LotteryRecord.ACTIVITY_TYPES):
            return Response({
                'code': 1004,
                'message': '活动类型不存在',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        if not openid:
            return Response({
                'code': 1001,
                'message': '缺少openid参数',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 更新或创建用户信息
            from users.models import WechatUser
            user, created = WechatUser.objects.update_or_create(
                openid=openid,
                defaults={'nickname': nickname} if nickname else {}
            )

            # 创建或更新记录
            record = LotteryManager.create_or_update_record(user, request.data)

            # 获取排名
            rarity_rank = LotteryManager.get_user_rank(user, activity_type, 'rarity')
            draws_rank = LotteryManager.get_user_rank(user, activity_type, 'draws')

            response_data = {
                'activityType': activity_type,
                'recordId': record.id,
                'rarityScore': record.rarity_score,
                'timestamp': record.timestamp,
                'rank': {
                    'rarityRank': getattr(rarity_rank, 'rank', None),
                    'drawsRank': getattr(draws_rank, 'rank', None)
                }
            }

            return Response({
                'code': 0,
                'message': 'success',
                'data': response_data
            })

        except Exception as e:
            return Response({
                'code': 2001,
                'message': f'服务器内部错误: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        """
        删除抽奖记录
        DELETE /lottery/records
        """
        activity_type = request.query_params.get('activityType')
        openid = request.query_params.get('openid')  # 直接从查询参数获取openid

        # 检查参数
        if not activity_type or activity_type not in dict(LotteryRecord.ACTIVITY_TYPES):
            return Response({
                'code': 1004,
                'message': '活动类型不存在',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        if not openid:
            return Response({
                'code': 1001,
                'message': '缺少openid参数',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 获取或创建用户
            from users.models import WechatUser
            user, created = WechatUser.objects.get_or_create(openid=openid)

            # 删除记录
            deleted = LotteryManager.delete_record(user, activity_type)

            if not deleted:
                return Response({
                    'code': 4001,
                    'message': '记录不存在',
                    'data': None
                }, status=status.HTTP_404_NOT_FOUND)

            return Response({
                'code': 0,
                'message': 'success',
                'data': {
                    'activityType': activity_type,
                    'deleted': True
                }
            })

        except Exception as e:
            return Response({
                'code': 2001,
                'message': f'服务器内部错误: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LotteryRecordDetailView(APIView):
    """
    获取用户抽奖记录详情
    GET /lottery/record-detail
    """
    # 移除权限类，不再需要认证
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        activity_type = request.query_params.get('activityType')
        openid = request.query_params.get('openid')  # 直接从查询参数获取openid
        nickname = request.query_params.get('nickname')  # 可选参数，用于更新用户昵称

        # 检查参数
        if not activity_type or activity_type not in dict(LotteryRecord.ACTIVITY_TYPES):
            return Response({
                'code': 1004,
                'message': '活动类型不存在',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        if not openid:
            return Response({
                'code': 1001,
                'message': '缺少openid参数',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 获取或创建用户，如果提供了nickname则同步更新
            from users.models import WechatUser
            defaults = {'nickname': nickname} if nickname else {}
            user, created = WechatUser.objects.update_or_create(openid=openid, defaults=defaults)

            # 获取记录详情
            record = LotteryManager.get_record_detail(user, activity_type)

            if not record:
                return Response({
                    'code': 4001,
                    'message': '记录不存在',
                    'data': None
                }, status=status.HTTP_404_NOT_FOUND)

            # 序列化详情
            serializer = LotteryRecordDetailSerializer(record)

            return Response({
                'code': 0,
                'message': 'success',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'code': 2001,
                'message': f'服务器内部错误: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
