#!/usr/bin/env python
"""
测试导入
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'car_wiki.settings')
django.setup()

try:
    from cars.curve_views import CurveGenerationView
    print("✅ CurveGenerationView 导入成功")
    
    # 检查视图的方法
    view = CurveGenerationView()
    print(f"✅ 视图实例化成功: {view}")
    
    # 检查是否有post方法
    if hasattr(view, 'post'):
        print("✅ 视图有post方法")
    else:
        print("❌ 视图没有post方法")
        
except Exception as e:
    print(f"❌ 导入失败: {str(e)}")
    import traceback
    traceback.print_exc()
