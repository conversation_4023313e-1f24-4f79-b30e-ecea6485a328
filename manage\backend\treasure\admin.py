from django.contrib import admin
from .models import TreasureItem, PrizeSource, Prize, PrizeSourceRelation

@admin.register(TreasureItem)
class TreasureItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'quality', 'item_type', 'probability', 'expiry_days', 'fragment_value', 'is_active')
    list_filter = ('quality', 'item_type', 'is_active')
    search_fields = ('name',)
    list_editable = ('is_active',)
    ordering = ('-created_at',)


class PrizeSourceRelationInline(admin.TabularInline):
    """奖品来源关系内联管理"""
    model = PrizeSourceRelation
    extra = 1
    fields = ['prize', 'probability']


@admin.register(PrizeSource)
class PrizeSourceAdmin(admin.ModelAdmin):
    """奖品来源管理"""
    list_display = ['id', 'source_code', 'name', 'source_type', 'is_active', 'created_at']
    list_filter = ['source_type', 'is_active']
    search_fields = ['source_code', 'name', 'description']
    inlines = [PrizeSourceRelationInline]
    list_per_page = 20


class PrizeSourceRelationAdmin(admin.TabularInline):
    """奖品来源关系管理"""
    model = PrizeSourceRelation
    extra = 1
    fields = ['source', 'probability']


@admin.register(Prize)
class PrizeAdmin(admin.ModelAdmin):
    """奖品管理"""
    list_display = ['id', 'prize_code', 'name', 'prize_type', 'rarity', 'quantity', 'is_active', 'created_at']
    list_filter = ['prize_type', 'rarity', 'is_active']
    search_fields = ['prize_code', 'name', 'description']
    inlines = [PrizeSourceRelationAdmin]
    list_per_page = 20


@admin.register(PrizeSourceRelation)
class PrizeSourceRelationStandaloneAdmin(admin.ModelAdmin):
    """奖品来源关系独立管理"""
    list_display = ['id', 'prize', 'source', 'probability', 'created_at']
    list_filter = ['source', 'prize']
    search_fields = ['prize__name', 'source__name']
    list_per_page = 20
