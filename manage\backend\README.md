# QQ飞车赛车图鉴后台管理系统

## 项目概述

本项目是QQ飞车赛车图鉴的后台管理系统，用于管理QQ飞车游戏中的赛车数据、用户评价、后台管理及反馈、夺宝奖品等内容。系统提供了完整的数据管理、内容审核、用户管理等功能，支持微信小程序前端调用API接口。

## 架构选型

### 后端技术栈

- **Web框架**: Django 3.2.23
- **API框架**: Django REST Framework 3.14.0
- **数据库**: SQLite3 (开发环境)
- **认证方式**: JWT + 微信小程序登录
- **图片存储**: 本地文件系统
- **跨域支持**: django-cors-headers 3.14.0
- **数据过滤**: django-filter 22.1
- **数据处理**: pandas 2.2.0, openpyxl 3.1.2
- **密码加密**: bcrypt 4.1.2

### 部署环境

- **Web服务器**: Nginx (配置文件: pikario.site.conf)
- **应用服务器**: Gunicorn (通过脚本管理)
- **启动脚本**: 
  - 开发环境: dev_start.bat
  - 生产环境: prod_start.sh, prod_stop.sh

## 目录结构及功能

### 核心应用

1. **car_wiki** - 项目主配置
   - `settings.py`: 项目全局配置，包括应用注册、中间件、数据库等配置
   - `urls.py`: 全局URL路由配置

2. **cars** - 赛车数据管理
   - `models.py`: 定义赛车、评论、评分和敏感词等模型
   - `views.py`: 处理赛车相关API请求
   - `serializers.py`: 赛车数据序列化器
   - `urls.py`: 赛车应用URL路由配置
   - `sql/`: SQL脚本目录

3. **users** - 用户管理
   - `models.py`: 定义微信用户模型
   - `views.py`: 处理用户认证、登录等请求
   - `urls.py`: 用户应用URL路由配置

4. **treasure** - 赛车夺宝功能
   - `models.py`: 定义夺宝奖品模型
   - `views.py`: 处理夺宝奖品相关API请求
   - `serializers.py`: 夺宝奖品数据序列化器
   - `urls.py`: 夺宝应用URL路由配置

5. **routes** - 后台管理系统用户及反馈功能
   - `views.py`: 处理后台管理系统的用户相关API请求，提供注册、登录、用户列表、权限管理等功能
   - `feedback.py`: 处理反馈相关API请求，提供反馈提交、查询、回复等功能
   - `auth.py`: Django路由配置文件，定义认证相关URL路由
   - `auth.js`: Node.js认证模块，提供备用的用户认证功能

6. **pets** - 宠物图鉴系统
   - `models.py`: 定义宠物信息模型
   - `views.py`: 处理宠物数据API请求，提供CRUD、Excel导入导出、图片上传等功能
   - `serializers.py`: 宠物数据序列化器
   - `urls.py`: 宠物应用URL路由配置
   - `admin.py`: 后台管理界面配置

7. **feedback** - 用户反馈系统
   - `models.py`: 定义用户反馈模型
   - `views.py`: 处理用户反馈API请求，提供反馈提交、查询、回复、删除等功能
   - `serializers.py`: 反馈数据序列化器

8. **sql_tool** - SQL查询工具
   - `views.py`: 提供SQL执行工具，支持查询数据库和执行SQL指令
   - `urls.py`: SQL工具URL路由配置

### 辅助目录

- **media/**: 媒体文件存储目录
- **config/**: 配置文件目录
- **scripts/**: 脚本工具目录
- **docs/**: 文档目录
- **templates/**: 模板文件目录
- **middleware/**: 自定义中间件目录
- **venv/**: Python虚拟环境目录

## 主要功能模块

### 1. 赛车数据管理

赛车数据模块是系统的核心功能，主要管理QQ飞车游戏中的赛车数据。

#### 主要模型

- **Car**: 存储赛车基本信息和性能参数
  - 包含赛车编号、名称、级别、各种性能参数(速度、漂移等)
  - 支持通过Excel导入数据

#### 特殊处理

- 赛车数据支持Excel导入功能，使用`openpyxl`和`pandas`处理Excel数据
- 赛车图片通过`image_id`关联，存储在`media/car_images/`目录

### 2. 用户评价系统

#### 主要模型

- **CarComment**: 用户赛车评论
  - 支持软删除，避免直接从数据库删除评论
  - 通过`is_deleted`字段标记删除状态

- **CommentReport**: 评论举报
  - 记录评论举报信息，支持多种处理状态

- **CarRating**: 赛车评分
  - 记录用户对赛车的多维度评分
  - 通过加权计算得出综合评分

#### 特殊处理

- 评论支持敏感词过滤功能
- 使用`SensitiveWord`模型存储和管理敏感词
- 实现防刷评论机制，限制单个用户对同一赛车的评论频率

### 3. 用户管理系统

#### 主要模型

- **WechatUser**: 微信用户信息
  - 存储用户OpenID、昵称、头像等信息
  - 支持VIP会员管理

#### 特殊处理

- 通过微信小程序授权接口获取用户信息
- 使用JWT实现无状态认证
- VIP会员有效期管理

### 4. 赛车夺宝系统

#### 主要模型

- **TreasureItem**: 夺宝奖品配置
  - 支持多种奖品类型和品质
  - 可配置奖品概率和有效期

#### 特殊处理

- 奖品类型和品质使用自由文本，不受枚举限制，便于灵活配置
- 提供建议选项接口，但不强制限制实际使用的值

### 5. 后台用户管理系统

后台用户管理系统用于管理后台管理界面的用户账号和权限。

#### 主要功能

- **用户注册**: 支持后台管理用户的注册功能，新注册用户默认没有高级权限
- **用户登录**: 使用用户名和密码登录，登录成功后生成JWT令牌
- **用户权限管理**: 管理员可以更新其他用户的权限，包括赛车数据管理权限和管理员权限
- **密码更新**: 支持用户密码修改功能
- **用户列表**: 提供用户列表查询功能，显示用户基本信息和权限状态

#### 特殊处理

- 使用bcrypt进行密码加密存储，保证安全性
- 使用JWT实现无状态认证，提供认证中间件保护API接口
- 区分管理员用户和普通用户，只有管理员能够修改其他用户的权限

### 6. 宠物图鉴系统

宠物图鉴系统用于管理QQ飞车游戏中的宠物数据。

#### 主要模型

- **Pet**: 宠物信息模型
  - 包含宠物代码、名称、基本技能、强化技能、形态、战斗力等信息
  - 支持图片关联存储

#### 主要功能

- **基础CRUD操作**: 提供宠物数据的创建、读取、更新和删除功能
- **Excel数据导入**: 支持从Excel表格批量导入宠物数据，自动处理更新和创建
- **Excel数据导出**: 支持将宠物数据导出为Excel格式
- **图片上传功能**: 支持为宠物上传图片，包括单个上传和批量上传
- **高级筛选查询**: 支持按宠物代码、名称、形态、战斗力、技能关键词等多维度筛选

#### 特殊处理

- 导入Excel时只更新非空字段，保留原有数据
- 按宠物ID进行数值排序，而不是字符串排序
- 支持按宠物名称批量上传图片，自动关联到对应宠物
- 提供Excel模板下载功能，便于用户按格式准备导入数据

### 7. SQL执行工具

SQL执行工具提供一个界面，供管理员直接执行SQL查询和操作数据库。

#### 主要功能

- **SQL查询执行**: 支持执行SELECT语句查询数据
- **数据库操作**: 支持执行INSERT、UPDATE、DELETE等修改操作
- **分页显示**: 查询结果支持分页显示，提高大数据量查询的性能
- **执行日志**: 记录SQL执行信息，包括执行时间、影响行数等

#### 特殊处理

- 自动添加分页功能，支持大数据量查询
- 区分查询操作和修改操作，提供不同的结果显示
- 详细的错误处理和日志记录，便于调试和审计
- 记录执行性能指标，如执行时间和影响记录数

### 8. 反馈管理系统

反馈管理系统用于处理用户提交的反馈和建议。

#### 主要模型

- **Feedback**: 用户反馈模型
  - 包含反馈类型（功能建议/Bug反馈/其他）、反馈内容、设备信息、联系方式等
  - 支持状态跟踪（待处理/处理中/已处理）
  - 管理员回复功能

#### 主要功能

- **反馈提交**: 允许普通用户提交不同类型的反馈信息
- **反馈列表**: 管理员可以查看所有用户提交的反馈，支持分页
- **反馈回复**: 管理员可以对用户反馈进行回复
- **状态管理**: 支持更新反馈处理状态，跟踪处理进度
- **反馈删除**: 支持删除不当或已处理的反馈

#### 特殊处理

- 使用Django REST Framework视图集实现API接口
- 自定义分页类，支持调整每页显示数量
- 不同反馈类型使用不同的处理流程和显示方式
- 管理员回复操作自动将状态更新为"已处理"

## 配置说明

### 1. 数据库配置

位于`car_wiki/settings.py`中的`DATABASES`配置：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

### 2. 媒体文件配置

位于`car_wiki/settings.py`中的媒体文件配置：

```python
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'backend', 'media')

# 确保car_images目录存在
CAR_IMAGES_DIR = os.path.join(MEDIA_ROOT, 'car_images')
```

### 3. REST Framework配置

位于`car_wiki/settings.py`中的REST Framework配置：

```python
REST_FRAMEWORK = {
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 24
}
```

### 4. 跨域配置

位于`car_wiki/settings.py`中的CORS配置：

```python
CSRF_TRUSTED_ORIGINS = [
    'https://pikario.site',
    'https://www.pikario.site',
    'http://localhost:5173',
]
```

### 5. 微信小程序配置

位于`car_wiki/settings.py`中的微信小程序配置：

```python
WECHAT_APP_ID = 'wx5f17c4ba385491af'  # 小程序 AppID
WECHAT_APP_SECRET = 'fa7f887758bff060eeb2797891bbe208'  # 小程序 AppSecret
```

### 6. 认证配置

位于`middleware/auth.py`中的认证中间件配置，用于验证JWT令牌并保护需要认证的API端点。

## 部署说明

### 开发环境启动

使用 `dev_start.bat` 脚本启动开发环境：

```bash
# Windows环境
dev_start.bat
```

### 生产环境部署

1. 使用 `prod_start.sh` 脚本启动生产环境：

```bash
# Linux环境
sh prod_start.sh
```

2. 使用 `prod_stop.sh` 脚本停止生产环境：

```bash
# Linux环境
sh prod_stop.sh
```

## 依赖包列表

主要依赖包记录在 `requirements.txt` 文件中，包括：

- Django==3.2.23
- djangorestframework==3.14.0
- django-cors-headers==3.14.0
- Pillow==10.2.0
- django-filter==22.1
- openpyxl==3.1.2
- python-dotenv==0.21.1
- bcrypt==4.1.2
- PyJWT==2.8.0
- pandas==2.2.0
- requests==2.31.0

## 开发注意事项

1. 本项目使用 Django 3.2 LTS 版本，确保与该版本兼容
2. API 遵循 RESTful 设计规范
3. 所有敏感配置应使用环境变量或 .env 文件管理
4. 代码提交前请运行测试确保功能正常 