from rest_framework import serializers
from .models import LotteryRecord, LotteryItem
from users.models import WechatUser

class LotteryItemSerializer(serializers.ModelSerializer):
    """
    抽奖物品序列化器
    """
    item = serializers.SerializerMethodField()
    
    class Meta:
        model = LotteryItem
        fields = ['item', 'count']
        
    def get_item(self, obj):
        return {
            'id': obj.item_id,
            'name': obj.name,
            'background': obj.background,
            'probability': obj.probability
        }


class LotteryRecordSerializer(serializers.ModelSerializer):
    """
    抽奖记录序列化器，用于创建和更新抽奖记录
    """
    statistics = LotteryItemSerializer(many=True, source='items', read_only=True)
    activityType = serializers.CharField(source='activity_type', write_only=True)
    hasLegendaryItems = serializers.BooleanField(source='has_legendary_items', required=False, default=False)
    hasTotalGoldItems = serializers.BooleanField(source='has_gold_items', required=False, default=False)
    totalDraws = serializers.IntegerField(source='draw_count', required=False, default=0)
    
    class Meta:
        model = LotteryRecord
        fields = ['id', 'activityType', 'statistics', 'totalDraws', 
                  'hasLegendaryItems', 'hasTotalGoldItems', 'timestamp']
        read_only_fields = ['id', 'timestamp']
    
    def create(self, validated_data):
        user = self.context['request'].user
        activity_type = validated_data.pop('activity_type')
        
        # 尝试获取已存在的记录，如果不存在则创建新记录
        record, created = LotteryRecord.objects.get_or_create(
            user=user,
            activity_type=activity_type,
            defaults=validated_data
        )
        
        # 如果记录已存在，更新字段
        if not created:
            for attr, value in validated_data.items():
                setattr(record, attr, value)
            record.save()
            
        return record


class LotteryRecordDetailSerializer(serializers.ModelSerializer):
    """
    抽奖记录详情序列化器
    """
    openid = serializers.SerializerMethodField()
    nickName = serializers.SerializerMethodField()
    statistics = LotteryItemSerializer(many=True, source='items')
    activityType = serializers.CharField(source='activity_type')
    rarityScore = serializers.FloatField(source='rarity_score')
    totalDraws = serializers.IntegerField(source='draw_count')
    hasLegendaryItems = serializers.BooleanField(source='has_legendary_items')
    hasTotalGoldItems = serializers.BooleanField(source='has_gold_items')
    
    class Meta:
        model = LotteryRecord
        fields = ['id', 'activityType', 'openid', 'nickName', 'statistics',
                  'totalDraws', 'rarityScore', 'hasLegendaryItems', 
                  'hasTotalGoldItems', 'timestamp']
    
    def get_openid(self, obj):
        # 实际应用中可能需要加密处理
        return obj.user.openid
    
    def get_nickName(self, obj):
        return obj.user.nickname


class RankingItemSerializer(serializers.ModelSerializer):
    """
    排行榜项目序列化器
    """
    rank = serializers.SerializerMethodField()
    openid = serializers.SerializerMethodField()
    nickName = serializers.SerializerMethodField()
    rarityScore = serializers.FloatField(source='rarity_score')
    drawCount = serializers.IntegerField(source='draw_count')
    hasLegendaryItems = serializers.BooleanField(source='has_legendary_items')
    hasTotalGoldItems = serializers.BooleanField(source='has_gold_items')
    
    class Meta:
        model = LotteryRecord
        fields = ['id', 'rank', 'openid', 'nickName', 'rarityScore', 
                  'drawCount', 'timestamp', 'hasLegendaryItems', 'hasTotalGoldItems']
    
    def get_rank(self, obj):
        return obj.rank if hasattr(obj, 'rank') else None
    
    def get_openid(self, obj):
        # 实际应用中可能需要加密处理
        return obj.user.openid
    
    def get_nickName(self, obj):
        return obj.user.nickname


class RankingResponseSerializer(serializers.Serializer):
    """
    排行榜响应序列化器
    """
    activityType = serializers.CharField()
    rankList = RankingItemSerializer(many=True)
    userRank = RankingItemSerializer(allow_null=True)
    totalParticipants = serializers.IntegerField()
    lastUpdateTime = serializers.DateTimeField() 