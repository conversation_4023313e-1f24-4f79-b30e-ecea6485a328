# Generated by Django 3.2.23 on 2025-03-23 09:42

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TreasureItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='奖品名称')),
                ('probability', models.DecimalField(decimal_places=2, help_text='百分比，例如5.25表示5.25%', max_digits=5, verbose_name='奖品概率')),
                ('expiry_days', models.IntegerField(default=0, help_text='0表示永久', verbose_name='奖品期限(天)')),
                ('image_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='奖品图片ID')),
                ('quality', models.CharField(choices=[('common', '普通'), ('rare', '稀有'), ('epic', '史诗'), ('legendary', '传说')], default='common', max_length=20, verbose_name='奖品品质')),
                ('item_type', models.CharField(choices=[('fixed', '固定奖品'), ('optional', '自选奖品')], default='fixed', max_length=20, verbose_name='奖品类型')),
                ('fragment_value', models.IntegerField(default=0, verbose_name='分解碎片数')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '夺宝奖品',
                'verbose_name_plural': '夺宝奖品',
                'ordering': ['-created_at'],
            },
        ),
    ]
