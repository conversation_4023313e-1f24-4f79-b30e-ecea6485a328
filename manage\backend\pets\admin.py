from django.contrib import admin
from .models import Pet

@admin.register(Pet)
class PetAdmin(admin.ModelAdmin):
    """宠物管理后台配置"""
    
    # 列表页显示的字段
    list_display = ('pet_id', 'name', 'form', 'combat_power', 'aptitude', 'main_attribute')
    
    # 搜索字段
    search_fields = ('pet_id', 'name')
    
    # 筛选器
    list_filter = ('form', 'aptitude', 'main_attribute')
    
    # 排序
    ordering = ('pet_id',)
    
    # 每页显示数量
    list_per_page = 20
    
    # 详情页字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('pet_id', 'name', 'form', 'combat_power', 'aptitude', 'main_attribute')
        }),
        ('技能信息', {
            'fields': ('basic_skill', 'enhanced_skill', 'normal_skill', 'rage_skill')
        }),
        ('系统信息', {
            'fields': ('image_id', 'created_at', 'updated_at'),
            'classes': ('collapse',)  # 可折叠
        })
    )
    
    # 只读字段
    readonly_fields = ('created_at', 'updated_at')
    
    # 列表页快速编辑字段
    list_editable = ('combat_power', 'aptitude')
    
    # 自定义列表操作
    actions = ['export_as_excel']
    
    def export_as_excel(self, request, queryset):
        """导出所选记录为Excel"""
        import pandas as pd
        from django.http import HttpResponse
        
        # 准备数据
        data = []
        for pet in queryset:
            data.append({
                '宠物代码': pet.pet_id,
                '宠物名称': pet.name,
                '基本技能': pet.basic_skill,
                '强化技能': pet.enhanced_skill,
                '宠物形态': pet.form,
                '战斗力': pet.combat_power,
                '资质': pet.aptitude,
                '主属性': pet.main_attribute,
                '普通技能': pet.normal_skill,
                '怒气技能': pet.rage_skill
            })
            
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 创建HTTP响应
        response = HttpResponse(content_type='application/vnd.ms-excel')
        response['Content-Disposition'] = 'attachment; filename="pets.xlsx"'
        
        # 写入Excel
        df.to_excel(response, index=False)
        return response
    
    export_as_excel.short_description = "导出所选为Excel"  # 操作显示的名称

    def get_readonly_fields(self, request, obj=None):
        """动态设置只读字段"""
        if obj:  # 编辑时
            return self.readonly_fields + ('pet_id',)  # 编辑时宠物代码不可修改
        return self.readonly_fields 