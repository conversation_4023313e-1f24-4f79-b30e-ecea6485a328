from PIL import Image
import os

def convert_to_ico(png_path, ico_path):
    """
    将PNG图片转换为ICO格式
    """
    if not os.path.exists(png_path):
        raise FileNotFoundError(f"找不到PNG文件: {png_path}")
        
    # 打开PNG图片
    img = Image.open(png_path)
    
    # 创建不同尺寸的图标
    sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
    icon_images = []
    
    for size in sizes:
        # 调整大小时保持纵横比
        resized_img = img.copy()
        resized_img.thumbnail(size, Image.Resampling.LANCZOS)
        
        # 如果图片不是正方形，创建一个正方形的透明背景
        if resized_img.size != size:
            new_img = Image.new('RGBA', size, (0, 0, 0, 0))
            # 将调整后的图片居中放置
            x = (size[0] - resized_img.size[0]) // 2
            y = (size[1] - resized_img.size[1]) // 2
            new_img.paste(resized_img, (x, y))
            resized_img = new_img
            
        icon_images.append(resized_img)
    
    # 保存为ICO文件
    icon_images[0].save(
        ico_path,
        format='ICO',
        sizes=sizes,
        append_images=icon_images[1:]
    )
    
    return ico_path

if __name__ == '__main__':
    # 获取脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    png_path = os.path.join(current_dir, 'icon.png')
    ico_path = os.path.join(current_dir, 'icon.ico')
    
    convert_to_ico(png_path, ico_path) 