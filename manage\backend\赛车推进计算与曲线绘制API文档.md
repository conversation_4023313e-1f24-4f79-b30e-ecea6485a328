# QQ飞车赛车推进计算与曲线绘制API文档

## 概述

本文档描述了QQ飞车图鉴小程序中赛车推进计算与曲线绘制功能的后端API接口。该功能基于`speed_power-view.py`和`speed_time-view.py`的逻辑实现，支持生成动力-速度曲线和速度-时间曲线。

## 基础信息

- **接口地址**: `POST /api/cars/generate-curves/`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **支持功能**: 
  - 动力-速度曲线绘制
  - 速度-时间曲线绘制
  - 最多两辆赛车对比
  - 自定义推进改装次数(0-40)

## 接口详情

### 曲线绘制接口

**接口地址**: `POST /api/cars/generate-curves/`

**功能描述**: 根据赛车数据生成动力-速度曲线或速度-时间曲线的数据点，支持最多两辆赛车对比。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| curve_type | string | 是 | 曲线类型：`power_speed` 或 `speed_time` |
| cars | array | 是 | 赛车数据数组，最多2辆 |

**cars数组中每个对象的参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 赛车名称 |
| level | string | 是 | 赛车等级（用于查询推进计算表） |
| propulsion_levels | array | 是 | 原装推进1-7档数据（7个数值） |
| engine_levels | array | 是 | 引擎1-6档数据（6个数值） |
| fuel_intensity | number | 是 | 燃料强度 |
| ignition_intensity | number | 是 | 点火强度 |
| propulsion_upgrades | number | 否 | 推进改装次数(0-40)，默认40 |

**请求示例**:

```json
{
    "curve_type": "power_speed",
    "cars": [
        {
            "name": "收割者",
            "level": "A/M3/L3",
            "propulsion_levels": [4.2, 4.4, 5.0, 5.35, 6.05, 6.25, 6.4],
            "engine_levels": [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
            "fuel_intensity": 6290,
            "ignition_intensity": 6290,
            "propulsion_upgrades": 40
        },
        {
            "name": "雷诺",
            "level": "A/M3/L3",
            "propulsion_levels": [4.1, 4.1, 4.6, 5.1, 5.6, 6.0, 6.1],
            "engine_levels": [7.5, 7.5, 7.5, 7.3, 7.3, 7.3],
            "fuel_intensity": 6290,
            "ignition_intensity": 6290,
            "propulsion_upgrades": 0
        }
    ]
}
```

## 响应格式

### 动力-速度曲线响应 (curve_type: "power_speed")

```json
{
    "success": true,
    "data": {
        "curve_type": "power_speed",
        "balance_curve": {
            "name": "速度动力平衡线",
            "data": [[0, 0], [0.382, 1.659], [0.764, 3.318], ...]
        },
        "car_curves": [
            {
                "name": "收割者",
                "base_power": [[0, 4200], [76.5, 4400], [87.21, 5000], ...],
                "fuel_power": [[0, 9442.67], [76.5, 9642.67], [87.21, 10242.67], ...],
                "boost_power": [[0, 14685.33], [76.5, 14885.33], [87.21, 15485.33], ...]
            },
            {
                "name": "雷诺",
                "base_power": [[0, 4100], [76.5, 4100], [87.21, 4600], ...],
                "fuel_power": [[0, 9342.67], [76.5, 9342.67], [87.21, 9842.67], ...],
                "boost_power": [[0, 14585.33], [76.5, 14585.33], [87.21, 15085.33], ...]
            }
        ]
    }
}
```

### 速度-时间曲线响应 (curve_type: "speed_time")

```json
{
    "success": true,
    "data": {
        "curve_type": "speed_time",
        "car_curves": [
            {
                "name": "收割者",
                "normal_speed": [[0, 0], [0.0016, 0.123], [0.0032, 0.246], ...],
                "fuel_speed": [[0, 0], [0.0016, 0.185], [0.0032, 0.369], ...],
                "super_speed": [[0, 0], [0.0016, 0.246], [0.0032, 0.492], ...]
            },
            {
                "name": "雷诺",
                "normal_speed": [[0, 0], [0.0016, 0.118], [0.0032, 0.236], ...],
                "fuel_speed": [[0, 0], [0.0016, 0.177], [0.0032, 0.354], ...],
                "super_speed": [[0, 0], [0.0016, 0.236], [0.0032, 0.472], ...]
            }
        ]
    }
}
```

## 响应字段说明

### 动力-速度曲线字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| balance_curve | object | 速度动力平衡线数据 |
| balance_curve.name | string | 平衡线名称 |
| balance_curve.data | array | 数据点数组 [[速度, 动力], ...] |
| car_curves | array | 赛车曲线数据数组 |
| car_curves[].name | string | 赛车名称 |
| car_curves[].base_power | array | 基础动力数据点 [[速度, 动力], ...] |
| car_curves[].fuel_power | array | 大喷动力数据点 [[速度, 动力], ...] |
| car_curves[].boost_power | array | cww动力数据点 [[速度, 动力], ...] |

### 速度-时间曲线字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| car_curves | array | 赛车曲线数据数组 |
| car_curves[].name | string | 赛车名称 |
| car_curves[].normal_speed | array | 平跑速度数据点 [[时间, 速度], ...] |
| car_curves[].fuel_speed | array | 大喷速度数据点 [[时间, 速度], ...] |
| car_curves[].super_speed | array | 超级喷速度数据点 [[时间, 速度], ...] |

## 错误响应

```json
{
    "success": false,
    "message": "错误描述信息"
}
```

**常见错误码**:

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

**常见错误信息**:

- "无效的曲线类型，支持: power_speed, speed_time"
- "至少需要提供一辆赛车的数据"
- "最多支持两辆赛车对比"
- "推进档位数据必须是包含7个数值的数组"
- "引擎档位数据必须是包含6个数值的数组"
- "推进改装次数必须是0-40之间的整数"
- "该级别赛车不支持改装推进：[等级名称]"

## 计算逻辑说明

### 数据处理

1. **推进和引擎档位数据乘以1000**: 所有推进档位和引擎档位数据在计算时都会乘以1000
2. **推进改装计算**: 根据赛车等级直接查询推进计算表，按改装次数比例计算实际推进值
3. **等级支持检查**: 如果传入的赛车等级在推进计算表中不存在，将返回"该级别赛车不支持改装推进"错误

### 动力-速度曲线

1. **速度锚点**: 使用固定的13个速度锚点 [0, 76.5, 87.21, 137.7, 142.29, 168.3, 180.54, 229.5, 244.8, 306, 382.5, 459, 535.5]
2. **基础动力**: 前7个锚点使用推进档位数据，后6个使用引擎档位数据
3. **大喷动力**: 基础动力 + 燃料强度/1.2
4. **cww动力**: 基础动力 + (燃料强度 + 点火强度)/1.2
5. **平衡线**: 使用分段函数计算速度动力平衡线

### 速度-时间曲线

1. **物理模拟**: 使用微分方程求解器模拟赛车加速过程
2. **阻力函数**: 使用分段函数计算空气阻力
3. **时间范围**: 0-16秒，10000个数据点
4. **三种模式**: 
   - 平跑: 仅基础动力
   - 大喷: 基础动力 + 燃料强度/1.2
   - 超级喷: 基础动力 + (燃料强度 + 点火强度)/1.2

## 使用示例

### 错误处理示例

```javascript
// 测试不支持的赛车等级
async function testUnsupportedLevel() {
    const response = await fetch('/api/cars/generate-curves/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            curve_type: 'power_speed',
            cars: [{
                name: '测试赛车',
                level: 'S级',  // 不支持的等级
                propulsion_levels: [4.2, 4.4, 5.0, 5.35, 6.05, 6.25, 6.4],
                engine_levels: [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                fuel_intensity: 6290,
                ignition_intensity: 6290,
                propulsion_upgrades: 40
            }]
        })
    });

    const data = await response.json();
    if (!data.success) {
        console.log('错误信息:', data.message);
        // 输出: "第1辆赛车数据错误: 该级别赛车不支持改装推进：S级"
    }
}
```

### JavaScript调用示例

```javascript
// 生成动力-速度曲线
async function generatePowerSpeedCurve() {
    const response = await fetch('/api/cars/generate-curves/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            curve_type: 'power_speed',
            cars: [{
                name: '收割者',
                level: 'A/M3/L3',
                propulsion_levels: [4.2, 4.4, 5.0, 5.35, 6.05, 6.25, 6.4],
                engine_levels: [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                fuel_intensity: 6290,
                ignition_intensity: 6290,
                propulsion_upgrades: 40
            }]
        })
    });
    
    const data = await response.json();
    if (data.success) {
        // 使用返回的数据点绘制图表
        console.log('平衡线数据:', data.data.balance_curve.data);
        console.log('基础动力数据:', data.data.car_curves[0].base_power);
    }
}

// 生成速度-时间曲线
async function generateSpeedTimeCurve() {
    const response = await fetch('/api/cars/generate-curves/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            curve_type: 'speed_time',
            cars: [{
                name: '收割者',
                level: 'A/M3/L3',
                propulsion_levels: [4.2, 4.4, 5.0, 5.35, 6.05, 6.25, 6.4],
                engine_levels: [7.5, 7.5, 7.5, 7.2, 7.25, 7.3],
                fuel_intensity: 6290,
                ignition_intensity: 6290,
                propulsion_upgrades: 40
            }]
        })
    });
    
    const data = await response.json();
    if (data.success) {
        // 使用返回的数据点绘制图表
        console.log('平跑速度数据:', data.data.car_curves[0].normal_speed);
        console.log('大喷速度数据:', data.data.car_curves[0].fuel_speed);
    }
}
```

## 注意事项

1. **数据精度**: 推进和引擎档位数据支持小数，计算时会乘以1000
2. **改装次数**: 支持0-40次改装，影响最终推进档位数值
3. **赛车等级**: 必须提供正确的赛车等级以查询推进计算表
4. **性能考虑**: 速度-时间曲线包含10000个数据点，数据量较大
5. **前端绘制**: 接口返回原始数据点，前端需要自行实现图表绘制逻辑
