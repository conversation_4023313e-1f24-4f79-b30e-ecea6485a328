<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推进计算与绘图功能测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #357abd;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #4a90e2;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .chart-container {
            width: 100%;
            height: 500px;
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .car-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        .car-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        .car-item:hover {
            background-color: #f0f0f0;
        }
        .car-item.selected {
            background-color: #e3f2fd;
            border-left: 3px solid #4a90e2;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ飞车推进计算与绘图功能测试</h1>
        
        <!-- 赛车搜索部分 -->
        <div class="section">
            <h2>1. 赛车搜索</h2>
            <div class="form-group">
                <label for="searchKeyword">搜索关键词（赛车名称或编号）:</label>
                <input type="text" id="searchKeyword" placeholder="例如：收割者、A001">
            </div>
            <button onclick="searchCars()">搜索赛车</button>
            <div id="searchResult" class="result" style="display: none;"></div>
            <div id="carList" class="car-list" style="display: none;"></div>
        </div>

        <!-- 推进计算部分 -->
        <div class="section">
            <h2>2. 推进40计算</h2>
            <div class="form-group">
                <label for="selectedCarId">选择的赛车ID:</label>
                <input type="text" id="selectedCarId" readonly placeholder="请先搜索并选择赛车">
            </div>
            <div class="form-group">
                <label for="userId">用户ID（可选）:</label>
                <input type="text" id="userId" placeholder="test_user">
            </div>
            <button onclick="calculatePropulsion()" id="calculateBtn" disabled>计算推进40</button>
            <div id="calculationResult" class="result" style="display: none;"></div>
        </div>

        <!-- 图表生成部分 -->
        <div class="section">
            <h2>3. 图表生成</h2>
            <div class="form-group">
                <label for="chartType">图表类型:</label>
                <select id="chartType">
                    <option value="power_speed">动力-速度曲线</option>
                    <option value="speed_time">速度-时间曲线</option>
                </select>
            </div>
            <button onclick="generateChart()" id="chartBtn" disabled>生成图表</button>
            <div id="chartResult" class="result" style="display: none;"></div>
            <div id="chartContainer" class="chart-container" style="display: none;"></div>
        </div>
    </div>

    <script>
        let selectedCar = null;
        let calculationData = null;
        let chart = null;

        // 搜索赛车
        async function searchCars() {
            const keyword = document.getElementById('searchKeyword').value.trim();
            if (!keyword) {
                showResult('searchResult', '请输入搜索关键词', 'error');
                return;
            }

            try {
                showResult('searchResult', '搜索中...', 'loading');
                const response = await fetch(`/api/cars/search/?q=${encodeURIComponent(keyword)}`);
                const data = await response.json();

                if (data.success) {
                    displayCarList(data.data);
                    showResult('searchResult', `找到 ${data.data.length} 辆赛车`, 'success');
                } else {
                    showResult('searchResult', `搜索失败: ${data.message}`, 'error');
                    document.getElementById('carList').style.display = 'none';
                }
            } catch (error) {
                showResult('searchResult', `搜索出错: ${error.message}`, 'error');
                document.getElementById('carList').style.display = 'none';
            }
        }

        // 显示赛车列表
        function displayCarList(cars) {
            const carList = document.getElementById('carList');
            carList.innerHTML = '';
            
            cars.forEach(car => {
                const carItem = document.createElement('div');
                carItem.className = 'car-item';
                carItem.innerHTML = `
                    <strong>${car.name}</strong> (${car.car_id}) - ${car.level}<br>
                    <small>推进档位: ${car.propulsion_levels.join(', ')}</small>
                `;
                carItem.onclick = () => selectCar(car, carItem);
                carList.appendChild(carItem);
            });
            
            carList.style.display = 'block';
        }

        // 选择赛车
        function selectCar(car, element) {
            // 清除之前的选择
            document.querySelectorAll('.car-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选择当前赛车
            element.classList.add('selected');
            selectedCar = car;
            document.getElementById('selectedCarId').value = car.car_id;
            document.getElementById('calculateBtn').disabled = false;
            
            // 清除之前的计算结果
            calculationData = null;
            document.getElementById('chartBtn').disabled = true;
            document.getElementById('calculationResult').style.display = 'none';
            document.getElementById('chartResult').style.display = 'none';
            document.getElementById('chartContainer').style.display = 'none';
        }

        // 计算推进40
        async function calculatePropulsion() {
            if (!selectedCar) {
                showResult('calculationResult', '请先选择赛车', 'error');
                return;
            }

            try {
                showResult('calculationResult', '计算中...', 'loading');
                
                const requestData = {
                    car_id: selectedCar.car_id,
                    user_id: document.getElementById('userId').value || 'test_user'
                };

                const response = await fetch('/api/cars/calculate-propulsion/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                if (data.success) {
                    calculationData = data.data;
                    displayCalculationResult(data.data);
                    document.getElementById('chartBtn').disabled = false;
                } else {
                    showResult('calculationResult', `计算失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('calculationResult', `计算出错: ${error.message}`, 'error');
            }
        }

        // 显示计算结果
        function displayCalculationResult(data) {
            const resultHtml = `
                <h4>计算结果</h4>
                <p><strong>赛车:</strong> ${data.car_info.name} (${data.car_info.car_id})</p>
                <p><strong>等级:</strong> ${data.car_info.level}</p>
                <p><strong>原装推进档位:</strong> ${data.original_data.propulsion_levels.join(', ')}</p>
                <p><strong>推进40档位:</strong> ${data.propulsion_40_levels.join(', ')}</p>
                <p><strong>燃料强度:</strong> ${data.original_data.fuel_intensity}</p>
                <details>
                    <summary>详细动力数据</summary>
                    <p><strong>速度锚点:</strong> ${data.power_data.speed_anchors.join(', ')}</p>
                    <p><strong>基础动力:</strong> ${data.power_data.base_powers.join(', ')}</p>
                    <p><strong>大喷动力:</strong> ${data.power_data.fuel_powers.map(p => Math.round(p)).join(', ')}</p>
                    <p><strong>cww动力:</strong> ${data.power_data.boost_powers.map(p => Math.round(p)).join(', ')}</p>
                </details>
            `;
            showResult('calculationResult', resultHtml, 'success');
        }

        // 生成图表
        async function generateChart() {
            if (!calculationData) {
                showResult('chartResult', '请先计算推进40数据', 'error');
                return;
            }

            try {
                showResult('chartResult', '生成图表中...', 'loading');
                
                const chartType = document.getElementById('chartType').value;
                const requestData = {
                    chart_type: chartType,
                    cars: [{
                        name: calculationData.car_info.name,
                        power_data: calculationData.power_data
                    }]
                };

                const response = await fetch('/api/cars/generate-chart/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                if (data.success) {
                    displayChart(data.data.chart_config);
                    showResult('chartResult', '图表生成成功', 'success');
                } else {
                    showResult('chartResult', `图表生成失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('chartResult', `图表生成出错: ${error.message}`, 'error');
            }
        }

        // 显示图表
        function displayChart(chartConfig) {
            const container = document.getElementById('chartContainer');
            container.style.display = 'block';
            
            if (chart) {
                chart.dispose();
            }
            
            chart = echarts.init(container);
            chart.setOption(chartConfig);
        }

        // 显示结果信息
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 回车键搜索
            document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchCars();
                }
            });
        });
    </script>
</body>
</html>
