import PyInstaller.__main__
import os
from convert_icon import convert_to_ico

def build_exe():
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 转换图标
    png_path = os.path.join(current_dir, 'icon.png')
    ico_path = os.path.join(current_dir, 'icon.ico')
    
    if not os.path.exists(png_path):
        raise FileNotFoundError(f"找不到图标文件: {png_path}")
    
    # 转换PNG为ICO
    convert_to_ico(png_path, ico_path)
    
    # 设置工作目录
    os.chdir(current_dir)
    
    # README文件路径
    readme_path = os.path.join(current_dir, 'README.txt')
    if not os.path.exists(readme_path):
        raise FileNotFoundError(f"找不到README文件: {readme_path}")
    
    # 设置输出目录
    dist_dir = os.path.join(current_dir, 'dist')
    build_dir = os.path.join(current_dir, 'build')
    
    # PyInstaller参数
    args = [
        os.path.join(current_dir, 'upload_car_images.py'),  # 主程序文件
        '--onefile',  # 打包成单个文件
        f'--icon={ico_path}',  # 使用转换后的ICO图标
        '--name=赛车图片上传工具',  # 可执行文件名称
        f'--add-data={readme_path};.',  # 包含README文件
        f'--distpath={dist_dir}',  # 指定输出目录
        f'--workpath={build_dir}',  # 指定工作目录
        f'--specpath={current_dir}',  # 指定spec文件目录
    ]
    
    try:
        # 运行PyInstaller
        PyInstaller.__main__.run(args)
    finally:
        # 清理临时文件
        if os.path.exists(ico_path):
            os.remove(ico_path)

if __name__ == '__main__':
    build_exe() 