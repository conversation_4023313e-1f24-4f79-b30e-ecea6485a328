from django.db import models

class Pet(models.Model):
    """
    宠物信息模型
    
    @property {str} pet_id - 宠物代码，唯一标识（必填）
    @property {str} name - 宠物名称（必填）
    @property {str} basic_skill - 基本技能(满级数值)（选填）
    @property {str} enhanced_skill - 强化技能(满级数值)（选填）
    @property {str} form - 宠物形态（选填）
    @property {str} combat_power - 战斗力，可以是数值或文本描述（选填）
    @property {str} aptitude - 资质（选填）
    @property {str} main_attribute - 主属性（选填）
    @property {str} normal_skill - 普通技能（选填）
    @property {str} rage_skill - 怒气技能（选填）
    @property {str} image_id - 图片ID（选填）
    """
    
    pet_id = models.CharField('宠物代码', max_length=50, unique=True)
    name = models.Char<PERSON><PERSON>('宠物名称', max_length=100)
    basic_skill = models.Char<PERSON><PERSON>('基本技能', max_length=200, null=True, blank=True)
    enhanced_skill = models.CharField('强化技能', max_length=200, null=True, blank=True)
    form = models.Char<PERSON>ield('宠物形态', max_length=50, null=True, blank=True)
    combat_power = models.CharField('战斗力', max_length=50, null=True, blank=True)
    aptitude = models.CharField('资质', max_length=50, null=True, blank=True)
    main_attribute = models.CharField('主属性', max_length=50, null=True, blank=True)
    normal_skill = models.TextField('普通技能', null=True, blank=True)
    rage_skill = models.TextField('怒气技能', null=True, blank=True)
    image_id = models.CharField('图片ID', max_length=100, null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '宠物'
        verbose_name_plural = '宠物'
        ordering = ['pet_id']
        indexes = [
            models.Index(fields=['pet_id']),
            models.Index(fields=['name']),
            models.Index(fields=['form']),
            models.Index(fields=['combat_power']),
        ]

    def __str__(self):
        return f"{self.pet_id} - {self.name}" 