# 推进计算表数据初始化说明

## 概述

本文档说明如何初始化推进计算表数据，该数据基于QQ飞车官方提供的各等级赛车推进档位上限和平均提升数值。

## 数据来源

根据官方数据，各等级赛车的推进档位参数如下：

| 赛车等级 | 推进1 | 推进2 | 推进3 | 推进4 | 推进5 | 推进6 | 推进7 |
|---------|-------|-------|-------|-------|-------|-------|-------|
| **C/M1各档位推进上限** | 无上限 | 4381 | 4959 | 5462 | 无上限 | 无上限 | 6244 |
| **C/M1各档位每次改装平均提升** | 7.6 | 7.6 | 9.175 | 9.25 | 9.15 | 8.35 | 8.875 |
| **B/M2/L2/R各档位推进上限** | 4599 | 4671 | 5179 | 5752 | 无上限 | 无上限 | 6621 |
| **B/M2/L2/R各档位每次改装平均提升** | 7.975 | 8.1 | 9.575 | 9.725 | 9.575 | 8.55 | 9.425 |
| **T1各档位推进上限** | 4682 | 4736 | 无上限 | 5900 | 无上限 | 无上限 | 6809 |
| **T1各档位每次改装平均提升** | 8.125 | 8.225 | 9.925 | 10 | 9.9 | 9 | 9.7 |
| **A/M3/L3各档位推进上限** | 4709 | 无上限 | 无上限 | 6009 | 6500 | 6753 | 6890 |
| **A/M3/L3各档位每次改装平均提升** | 8.175 | 8.55 | 10 | 10.175 | 10 | 9.05 | 9.8 |
| **T2各档位推进上限** | 无上限 | 5038 | 无上限 | 无上限 | 无上限 | 6753 | 6890 |
| **T2各档位每次改装平均提升** | 8.275 | 8.75 | 10 | 10 | 10 | 9.05 | 9.8 |
| **T2皮肤各档位推进上限** | 4764 | 无上限 | 无上限 | 5900 | 无上限 | 6753 | 6890 |
| **T2皮肤各档位每次改装平均提升** | 8.275 | 8.75 | 10 | 10 | 10 | 9.05 | 9.8 |

## 初始化方法

### 方法1: 使用Django管理命令 (推荐)

```bash
# 进入项目目录
cd manage/backend

# 初始化数据 (只创建不存在的记录)
python manage.py init_propulsion_data

# 强制更新所有数据 (包括已存在的记录)
python manage.py init_propulsion_data --force
```

### 方法2: 使用独立Python脚本

```bash
# 进入项目目录
cd manage/backend

# 运行初始化脚本
python init_propulsion_data.py
```

### 方法3: 使用API接口

```bash
# 使用批量导入接口
curl -X POST "http://localhost:8000/api/propulsion-levels/import_data/" \
  -H "Content-Type: application/json" \
  -d @propulsion_data.json
```

## 数据结构说明

### 数据库字段

每条推进计算表记录包含以下字段：

- `car_level`: 赛车等级 (C/M1, B/M2/L2/R, T1, A/M3/L3, T2, T2皮肤)
- `level_X_max`: 推进X档上限 (null表示无上限)
- `level_X_diff`: 推进X档差值 (平均提升 × 40)
- `level_X_avg_increase`: 推进X档平均提升
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 计算公式

**推进40档位计算公式**:
```
推进40 = 原装推进 + 差值
如果有上限: 推进40 = min(推进40, 上限)
```

**差值计算**:
```
差值 = 平均提升 × 40
```

## 验证数据

初始化完成后，可以通过以下方式验证数据：

### 1. 使用管理命令验证

管理命令会自动显示验证结果，包括：
- 创建和更新的记录数量
- 各等级数据是否存在
- 详细的数据内容

### 2. 使用API接口验证

```bash
# 获取所有推进计算表数据
curl -X GET "http://localhost:8000/api/propulsion-levels/"

# 获取特定等级的数据
curl -X GET "http://localhost:8000/api/propulsion-levels/?car_level=A/M3/L3"

# 获取赛车等级列表
curl -X GET "http://localhost:8000/api/propulsion-levels/levels/"
```

### 3. 使用Django Shell验证

```python
# 进入Django Shell
python manage.py shell

# 验证数据
from cars.models import PropulsionLevelTable

# 查看总记录数
print(f"总记录数: {PropulsionLevelTable.objects.count()}")

# 查看所有等级
levels = PropulsionLevelTable.objects.values_list('car_level', flat=True)
print(f"等级列表: {list(levels)}")

# 查看特定等级的数据
a_level = PropulsionLevelTable.objects.get(car_level='A/M3/L3')
print(f"A/M3/L3等级数据: {a_level}")
```

## 常见问题

### Q: 如何更新已存在的数据？
A: 使用 `--force` 参数：`python manage.py init_propulsion_data --force`

### Q: 如何添加新的赛车等级？
A: 修改管理命令中的 `propulsion_data` 列表，添加新等级的数据，然后重新运行命令。

### Q: 数据初始化失败怎么办？
A: 检查错误信息，确保：
1. 数据库连接正常
2. 模型定义正确
3. 数据格式符合要求

### Q: 如何备份现有数据？
A: 使用导出功能：
```bash
# 导出Excel文件
curl -X GET "http://localhost:8000/api/propulsion-levels/export_excel/" -o backup.xlsx

# 或使用Django命令
python manage.py dumpdata cars.PropulsionLevelTable > backup.json
```

## 注意事项

1. **数据一致性**: 确保平均提升数值与差值的计算关系正确 (差值 = 平均提升 × 40)
2. **空值处理**: 推进上限为null表示无上限，在计算时需要特殊处理
3. **等级命名**: 赛车等级名称必须与系统中的等级定义保持一致
4. **数据备份**: 在更新数据前建议先备份现有数据
5. **测试验证**: 初始化后应进行充分的测试验证

## 相关文档

- [QQ飞车推进计算功能完整API接口文档.md](./QQ飞车推进计算功能完整API接口文档.md)
- [推进计算与绘图功能开发总结.md](./推进计算与绘图功能开发总结.md)
- [项目进度.md](../../项目进度.md)

---

*最后更新: 2025-01-09*
